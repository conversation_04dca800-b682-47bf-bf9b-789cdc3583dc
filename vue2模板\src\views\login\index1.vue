<template>
  <div class="login-container">
    <div class="login-header">
      <div style="width: 100%; height: 100%; background-color: white">
        <img alt="" class="app-logo" :src="require('@/assets/logo1.png')" />
      </div>
    </div>

    <el-row>
      <div
        v-if="onShowBrowser"
        style="
          float: right;
          padding: 15px 8px;
          font-size: 14px;
          font-weight: bold;
        "
      >
        <el-alert
          :closable="false"
          show-icon
          title="为保证良好的使用效果，建议您使用谷歌浏览器访问"
          type="warning"
        />
      </div>
      <div
        class="login-header-item"
        style="padding-top: 80px; text-align: center"
      >
        <img alt="" :src="require('@/assets/title.png')" />
      </div>
      <div class="login-box">
        <el-row :gutter="10">
          <el-col :span="12">
            <img
              alt=""
              :src="require('@/assets/slogan.png')"
              style="padding-top: 150px; padding-left: 200px"
            />
          </el-col>
          <el-col :span="12" style="border-left: 1px solid #606266">
            <div
              class="login-box-item"
              style="width: 400px; background-color: white; border-radius: 15px"
            >
              <div class="login-form">
                <div class="user-form">
                  <el-tabs v-model="activeName" @tab-click="handleClickTabs">
                    <el-tab-pane name="sms_code">
                      <template slot="label">手机短信登录</template>
                      <div
                        v-if="activeName == 'sms_code'"
                        style="width: 80%; margin: 10px auto"
                      >
                        <el-form
                          ref="form"
                          :hide-required-asterisk="true"
                          label-position="left"
                          :model="smsform"
                          :rules="rules"
                        >
                          <el-form-item
                            prop="mobile"
                            style="margin-bottom: 20px"
                          >
                            <el-input
                              v-model.trim="smsform.mobile"
                              v-focus
                              class="input-box"
                              maxlength="11"
                              :placeholder="translateTitle('请输入手机号')"
                              size="mini"
                              type="text"
                            />
                          </el-form-item>
                          <el-form-item prop="code" style="margin-bottom: 20px">
                            <el-input
                              v-model.trim="smsform.code"
                              class="input-box"
                              :placeholder="translateTitle('请输入短信验证码')"
                              type="text"
                            >
                              <template #suffix>
                                <el-button
                                  :disabled="codeState"
                                  type="text"
                                  @click="handleGetMessage"
                                >
                                  {{
                                    codeState ? `${time}s后重发` : '获取验证码'
                                  }}
                                </el-button>
                              </template>
                            </el-input>
                          </el-form-item>
                          <el-button
                            class="login-btn"
                            :loading="loading"
                            type="primary"
                            @click="handleLogin"
                          >
                            {{ translateTitle('登录') }}
                          </el-button>
                        </el-form>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="账号密码登录" name="captcha">
                      <div
                        v-if="activeName == 'captcha'"
                        style="width: 80%; margin: 10px auto"
                      >
                        <el-form
                          ref="form"
                          :hide-required-asterisk="true"
                          label-position="left"
                          :model="form"
                          :rules="rules"
                        >
                          <el-form-item
                            prop="username"
                            style="margin-bottom: 20px"
                          >
                            <el-input
                              v-model.trim="form.username"
                              v-focus
                              class="input-box"
                              :placeholder="translateTitle('请输入用户名')"
                              size="mini"
                              tabindex="1"
                              type="text"
                            />
                          </el-form-item>
                          <el-form-item
                            prop="password"
                            style="margin-bottom: 20px"
                          >
                            <el-input
                              :key="passwordType"
                              ref="password"
                              v-model.trim="form.password"
                              class="input-box"
                              :placeholder="translateTitle('请输入密码')"
                              size="mini"
                              tabindex="2"
                              :type="passwordType"
                              @keyup.enter.native="handleLogin"
                            >
                              <template
                                v-if="passwordType === 'password'"
                                #suffix
                              >
                                <vab-icon
                                  class="show-password"
                                  icon="eye-off-line"
                                  @click="handlePassword"
                                />
                              </template>
                              <template v-else #suffix>
                                <vab-icon
                                  class="show-password"
                                  icon="eye-line"
                                  @click="handlePassword"
                                />
                              </template>
                            </el-input>
                          </el-form-item>

                          <!-- 验证码验证逻辑需自行开发，如不需要验证码功能建议注释 -->
                          <el-form-item prop="code" style="margin-bottom: 20px">
                            <el-input
                              v-model.trim="form.code"
                              class="input-box"
                              :placeholder="translateTitle('请输入验证码')"
                              tabindex="3"
                              type="text"
                            >
                              <template #suffix>
                                <el-image
                                  class="code"
                                  :src="codeUrl"
                                  style="height: 32px"
                                  @click="changeCode"
                                >
                                  <div slot="error">获取失败</div>
                                </el-image>
                              </template>
                            </el-input>
                          </el-form-item>
                          <el-form-item style="margin-bottom: 0">
                            <div
                              style="
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                              "
                            >
                              <el-button style="color: unset" type="text">
                                忘记密码？
                              </el-button>
                              <el-button style="color: unset" type="text">
                                常见问题
                              </el-button>
                            </div>
                          </el-form-item>
                          <el-button
                            class="login-btn"
                            :loading="loading"
                            type="primary"
                            @click="handleLogin"
                          >
                            {{ translateTitle('登录') }}
                          </el-button>
                        </el-form>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
                <el-divider />
                <div class="welcome-tips"></div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- <div
          class="login-box-item"
          style="width: 300px; background-color: white"
        >
          <div
            class="ewm-content"
            style="
              text-align: center;
              margin-top: 40px;
              padding: 20px;
              border-right: 1px solid #f1f1f1;
            "
          >
            <div class="ewm-text" style="font-weight: bold">扫描登录</div>
            <div class="ewm-img">
              <img
                alt=""
                src="data:image/png;base64,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"
              />
            </div>
            <div>使用XXX扫描登录</div>
          </div>
        </div> -->
      </div>
    </el-row>

    <div class="login-footer">
      <p class="p">智能化地震队西南研发团队提供技术服务</p>
      <a
        class="text"
        href="https://beian.miit.gov.cn"
        style="margin-bottom: 10px"
        target="_blank"
      >
        备案号：蜀ICP备19018190号
      </a>
    </div>
  </div>
</template>

<script>
  import { mapActions, mapGetters } from 'vuex'
  import { translateTitle } from '@/utils/i18n'
  import { isPassword } from '@/utils/validate'
  // import QRCode from 'qrcodejs2'
  import { getCaptcha, getSmsCode } from '@/api/user'
  import { environment, onShowBrowserTips } from '@/config'

  export default {
    name: 'Login',
    directives: {
      focus: {
        inserted(el) {
          el.querySelector('input').focus()
        },
      },
    },
    beforeRouteLeave(to, from, next) {
      clearInterval(this.timer)
      next()
    },
    data() {
      const validateUsername = (rule, value, callback) => {
        if ('' === value)
          callback(new Error(this.translateTitle('用户名不能为空')))
        else callback()
      }
      const validatePassword = (rule, value, callback) => {
        if (!isPassword(value))
          callback(new Error(this.translateTitle('密码不能少于6位')))
        else callback()
      }
      return {
        onShowBrowser: false,
        activeName: 'sms_code',
        form: {
          username: '',
          password: '',
          code: '',
        },
        smsform: {
          mobile: '',
          code: '',
        },
        codeState: false,
        time: 60,
        rules: {
          username: [
            {
              required: true,
              trigger: 'blur',
              validator: validateUsername,
            },
          ],
          password: [
            {
              required: true,
              trigger: 'blur',
              validator: validatePassword,
            },
          ],
          code: [
            {
              required: true,
              trigger: 'blur',
              message: '验证码不能空',
            },
          ],
          mobile: [
            {
              required: true,
              pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
              message: '请输入正确的手机号码',
              trigger: 'blur',
            },
          ],
        },
        loading: false,
        passwordType: 'password',
        redirect: undefined,
        timer: 0,
        codeUrl: '',
        // isEwm: false,
      }
    },
    computed: {
      ...mapGetters({
        title: 'settings/title',
      }),
    },
    watch: {
      $route: {
        handler(route) {
          this.redirect = (route.query && route.query.redirect) || '/'
        },
        immediate: true,
      },
    },
    created() {},
    mounted() {
      this.onShowBrowser = onShowBrowserTips ? true : false
      if (environment == 'development') {
        this.form.username = 'admin'
        this.form.password = ''
      }
      // this.changeCode()
    },
    methods: {
      ...mapActions({
        login: 'user/login',
      }),
      translateTitle,
      handlePassword() {
        this.passwordType === 'password'
          ? (this.passwordType = '')
          : (this.passwordType = 'password')
        this.$nextTick(() => {
          this.$refs.password.focus()
        })
      },
      handleRoute() {
        return this.redirect === '/404' || this.redirect === '/403'
          ? '/'
          : this.redirect
      },
      handleLogin() {
        this.$refs.form.validate(async (valid) => {
          if (valid)
            try {
              this.loading = true
              let paramsform = {
                grant_type: this.activeName,
              }
              if (this.activeName == 'captcha') {
                paramsform = Object.assign(paramsform, this.form)
              }
              if (this.activeName == 'sms_code') {
                paramsform = Object.assign(paramsform, this.smsform)
              }
              await this.login(paramsform).catch(() => {
                if (this.activeName == 'captcha') this.changeCode()
              })
              await this.$router.push(this.handleRoute())
            } finally {
              this.loading = false
            }
        })
      },
      async changeCode() {
        //切换验证码
        const {
          data: {
            data: { img, uuid },
          },
        } = await getCaptcha()
        this.codeUrl = 'data:image/gif;base64,' + img
        this.form.uuid = uuid
      },
      async handleGetMessage() {
        if (!this.codeState) {
          this.$refs.form.validateField('mobile', async (valid) => {
            if (!valid) {
              //发送请求
              let params = {
                phoneNumber: this.smsform.mobile,
              }
              const {
                data: { code, msg },
              } = await getSmsCode(params)
              if (code == '00000') {
                this.$baseMessage('验证码已发送，5分钟内有效！', 'info')
                this.codeState = true
                let timeout = setInterval(() => {
                  this.time--
                  if (this.time == 0) {
                    clearInterval(timeout)
                    this.time = 60
                    this.codeState = false
                  }
                }, 1000)
              } else {
                this.$baseMessage(msg, 'error')
              }
            }
          })
        }
      },
      handleClickTabs() {
        if (this.activeName == 'captcha') {
          this.changeCode()
        }
        if (this.activeName == 'sms_code') {
          this.codeState = false
          this.time = 60
        }
        this.$refs.form.resetFields()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .login-container {
    width: 100%;
    height: 100vh;
    background-image: url('~@/assets/login_images/background.png');
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
  }
  .login-container::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: inherit;
    content: '';
    background: #000000;
    opacity: 0.6;
  }

  .login-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    height: 60px;
    font-size: 18px;
    line-height: 60px;
    .login-header-item {
      z-index: 99;
      margin-top: 25px;
      font-family: 'cursive';
      font-size: 42px;
      color: #fff;
      text-align: center;
      text-shadow: 2px 5px 5px #2e2d2d;
      border-radius: 15px;
    }
    .app-logo {
      height: 50px;
      margin-top: 5px;
    }
  }
  .login-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    margin-bottom: 20px;
    text-align: center;

    .text {
      color: white;
    }
    .p {
      font-size: 20px;
      color: white;
      text-align: center;
    }
  }
  .login-box {
    width: 100%;
    margin: calc((100vh - 750px) / 2) auto 5vw;
    border-radius: 4px;
    .login-box-item {
      float: right;
      height: 370px;
      margin-right: 240px;
    }
    .slogan {
      float: left;
      width: 700px;
      height: 120px;
      margin-top: 40px;
      margin-left: 100px;
      border-right: 1px solid #fff;
      .slogan-title {
        margin-bottom: 100px;
        font-size: 50px;
        font-weight: 600;
        color: #fff;
      }
      .slogan-content {
        font-family: '宋体';
        font-size: 30px;
        font-weight: 600;
      }
    }
  }
  .login-form {
    position: relative;
    width: 100%;
    padding-top: 40px;
    // height: inherit;
    overflow: hidden;
    background-color: white;
    border-radius: 15px;
    box-shadow: 5px 5px 5px #828181;
    .user-form {
      height: 310px;
    }
    ::v-deep {
      .input-box .el-input__inner {
        height: 33px;
        font-size: 14px;
        line-height: 33px;
        border-top-width: 0px !important;
        border-right-width: 0px !important;
        border-bottom-width: 1px !important ;
        border-left-width: 0px !important;
        border-radius: 0 !important; // 去除圆角
      }
      .el-tabs__item {
        font-size: 18px;
      }
    }
    .title {
      font-size: 54px;
      font-weight: 500;
      color: #fff;
    }

    .title-tips {
      padding: 1vh 4.5vh;
      margin-top: 20px;
      font-size: 26px;
      font-weight: 400;
      color: rgba(92, 102, 240, 1);
    }

    .login-btn {
      display: inherit;
      width: 100%;
      background-color: #fba242;
      border: 0;

      &:hover {
        opacity: 0.9;
      }
    }

    .tips {
      margin-bottom: 10px;
      font-size: $base-font-size-default;
      color: $base-color-white;

      span {
        &:first-of-type {
          margin-right: 16px;
        }
      }
    }

    .title-container {
      position: relative;

      .title {
        margin: 0 auto 40px auto;
        font-size: 34px;
        font-weight: bold;
        color: $base-color-blue;
        text-align: center;
      }
    }

    i {
      position: absolute;
      top: 8px;
      left: 5px;
      z-index: $base-z-index;
      font-size: 16px;
      color: #d7dee3;
      cursor: pointer;
      user-select: none;
    }

    .show-password {
      position: absolute;
      top: unset;
      right: 25px;
      left: -35px;
      font-size: 16px;
      color: #d7dee3;
      cursor: pointer;
      user-select: none;
    }

    .ewm-content {
      width: 70%;
      height: 330px;
      padding: 50px;
      margin: auto;
      text-align: center;
      .ewm-text {
        font-weight: 600;
        color: #0d3f84;
      }
      .ewm-img {
        width: 150px;
        height: 150px;
        margin: auto;
        margin-top: 30px;
        border: 1px solid #999;
      }
    }
    ::v-deep {
      .el-form-item {
        padding-right: 0;
        margin-bottom: 10px;
        color: #454545;
        background: transparent;
        border: 1px solid transparent;
        border-radius: 2px;
        &__label {
          line-height: 20px;
        }
        &__content {
          min-height: $base-input-height;
          line-height: $base-input-height;
        }

        &__error {
          position: absolute;
          top: 100%;
          left: 18px;
          font-size: $base-font-size-small;
          color: $base-color-red;
        }
      }

      .el-input {
        box-sizing: border-box;
      }

      .el-tabs {
        &__nav {
          // width: 100%;
          margin-left: 35px;
        }
        &__item {
          // width: 50%;
          text-align: center;
        }
        &__item.is-active {
          // color: white;
          // background-color: #4e8cc4;
        }
        .el-tabs__nav-wrap::after {
          width: 0;
        }
      }
      .el-input__suffix {
        right: 0;
      }
      .ewm-box {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 999;
        border: 20px solid transparent;
        border-top-color: white;
        border-right-color: white;
        .img {
          position: absolute;
          top: -15px;
          right: -16px;
          width: 25px;
          height: 25px;
          background-repeat: no-repeat;
          background-position: 100%;
          background-size: cover;
        }
        .ewm {
          background-image: url('~@/assets/login_images/ewm.png');
        }
        .dn {
          background-image: url('~@/assets/login_images/dn.png');
        }
      }
    }
  }

  .qr_ins {
    display: flex;
    align-items: center;
    float: left;
    width: 50%;
    height: 100%;
  }

  .qr_ins .android,
  .qr_ins .ios {
    display: flex;
    flex-direction: row;
    padding: 10px 0;
  }

  .android-header > p {
    width: 100%;
    margin: 0;
    text-align: 20px;
  }
  .welcome-tips {
    width: 80%;
    margin: auto;
    .el-divider {
      background-color: #0d3f84;
    }
    ::v-deep {
      .el-divider__text {
        width: max-content;
        font-size: 14px;
        font-weight: 600;
        text-decoration: underline;
        text-underline-position: under;
        a {
          color: #0d3f84;
        }
      }
    }
  }
</style>
