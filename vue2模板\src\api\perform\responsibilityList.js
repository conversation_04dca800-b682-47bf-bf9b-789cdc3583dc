import request from '@/utils/request'

// 新增责任清单分配
export function addUserDuty(data) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists',
    method: 'post',
    data,
  })
}

// 管理员修改用户责任清单
export function updateUserDuty(data) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists/adminUpdateUserDutyListAndInitiateAnApproval',
    method: 'put',
    data,
  })
}

// 分页获取责任清单列表
export function getUserDutyList(params) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists/list',
    method: 'get',
    params,
  })
}

// 删除责任清单列表
export function delUserDuty(params) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists/delete',
    method: 'delete',
    params,
  })
}

// 管理员确认用户拒绝清单
export function allocateUpdateDutyList(data) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists/allocateUpdateDutyList',
    method: 'put',
    data,
  })
}

// 管理员查看用户拒绝清单
export function queryUserRejectList(params) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists/queryUserRejectList',
    method: 'get',
    params,
  })
}

// 用户同意或拒绝责任清单
export function userUpdateDutyList(data) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists/userUpdateDutyList',
    method: 'put',
    data,
  })
}

// 根据任务清单分配ID查询 单人分配详情
export function getUserDutyInfo(id) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists/' + id,
    method: 'get',
  })
}

// 用户查询自己待确认的责任清单
export function userQueryOnDutyList(params) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists/userQueryMyselfDutyList',
    method: 'get',
    params,
  })
}

// 用户查询自己的所有责任清单详情
export function userQueryAllList(params) {
  return request({
    url: '/perform-duties-service/perform/userDutyListsInfo/userQueryAllList',
    method: 'get',
    params,
  })
}

// 根据ID获取履职责任清单详情
export function getUserDutyInfoById(id) {
  return request({
    url: '/perform-duties-service/perform/userDutyListsInfo/' + id,
    method: 'get',
  })
}

// 根据任务清单分配ID查询 单人分配详情
export function queryUserDutyInfo(id) {
  return request({
    url: '/perform-duties-service/perform/userDutyLists/' + id,
    method: 'get',
  })
}

// 获取个人责任清单
export function queryUserDutyList(params) {
  return request({
    url: '/perform-duties-service/perform/userDutyListsInfo/userQueryAllList',
    method: 'get',
    params,
  })
}
