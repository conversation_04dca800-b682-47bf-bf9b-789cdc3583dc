<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '设备场所'"
      :visible.sync="dialogFormVisible"
      width="700px"
      @close="close"
    >
      <el-form ref="form" label-width="130px" :model="form" :rules="rules">
        <el-form-item label="履职检查项：" prop="dutyInspectionItem">
          <el-input
            v-model="form.dutyInspectionItem"
            placeholder="请输入
"
          />
        </el-form-item>
        <el-form-item label="是否停用：" prop="disabled">
          <el-radio-group v-model="form.disabled">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import {
    addDutyInspectionItems,
    updateDutyInspectionItems,
  } from '@/api/perform/performItems'
  export default {
    name: '',
    data() {
      return {
        form: {
          dutyInspectionItem: '',
          disabled: false,
        },
        content: '',
        rules: {
          dutyInspectionItem: [
            { required: true, trigger: 'blur', message: '请输入' },
          ],
        },
        title: '',
        dialogFormVisible: false,
      }
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          this.form = row
        }
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            if (this.form.id) {
              updateDutyInspectionItems(this.form).then(() => {
                this.$message.success('编辑成功')
                this.$emit('refreshDataList')
                this.close()
              })
            } else {
              addDutyInspectionItems(this.form).then(() => {
                this.$message.success('添加成功')
                this.$emit('refreshDataList')
                this.close()
              })
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
