<template>
  <el-form ref="formRef" label-width="80px" :model="form">
    <el-row>
      <el-col :span="12">
        <el-form-item label="开始时间：" prop="startDate">
          {{ form.createTime }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="发起人：" prop="safetyResponsiblePersonArr">
          {{ form.creator }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="明细表：">
          <el-table border :data="form.scoreRecordData" style="width: 100%">
            <el-table-column label="选择项" prop="id">
              <template #default="scope">
                {{
                  scope.row.username +
                  '-' +
                  scope.row.scoreRecordDate +
                  '-' +
                  scope.row.describe
                }}
              </template>
            </el-table-column>
            <el-table-column label="分值" prop="score" />
            <el-table-column label="说明" prop="describe" />
          </el-table>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="累计分值：">
          {{ form.totalScore }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="说明：" prop="describe">
          {{ form.describe }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="第一审批领导："
          label-width="120px"
          prop="approverId"
        >
          {{ form.firstApproval.name }}
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  export default {
    props: {
      formData: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        form: {},
      }
    },
    watch: {
      formData(val) {
        this.form = val
      },
    },
  }
</script>
