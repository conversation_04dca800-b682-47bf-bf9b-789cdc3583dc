<!-- 积分系统 -->
<template>
  <div class="integralList-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item label="项目名称" prop="projectId">
        <el-select v-model="queryParams.projectId" placeholder="请选择">
          <el-option
            v-for="item in projectList"
            :key="item.id"
            clearable
            filterable
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员" prop="userId">
        <el-select v-model="queryParams.userId" placeholder="请选择">
          <el-option
            v-for="item in storeUserList"
            :key="item.userId"
            clearable
            filterable
            :label="item.nickname"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">
          查询
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格筛选按钮 -->
    <div class="table-operations">
      <el-dropdown @command="handleColumnFilter">
        <el-tag style="cursor: pointer">
          <i class="el-icon-s-operation"></i>
          筛选
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-tag>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="item in columnOptions"
            :key="item.prop"
            :command="item.prop"
          >
            <el-checkbox v-model="item.visible" @click.stop>
              {{ item.label }}
            </el-checkbox>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" prop="id" />
      <el-table-column
        v-if="getColumnVisible('username')"
        align="center"
        label="员工"
        prop="username"
      />
      <el-table-column
        v-if="getColumnVisible('departmentName')"
        align="center"
        label="部门"
        prop="departmentName"
      />
      <el-table-column
        v-if="getColumnVisible('scoreCreateDate')"
        align="center"
        label="创建时间"
        prop="scoreCreateDate"
      />
      <el-table-column
        v-if="getColumnVisible('duty_listCreateDate')"
        align="center"
        label="日期"
        prop="duty_listCreateDate"
      />
      <el-table-column
        v-if="getColumnVisible('departmentName')"
        align="center"
        label="履职项"
        prop="departmentName"
      />
      <el-table-column
        v-if="getColumnVisible('projectName')"
        align="center"
        label="项目"
        prop="projectName"
      />
      <el-table-column
        v-if="getColumnVisible('frequencyCount')"
        align="center"
        label="频率"
        prop="frequencyCount"
      />
      <el-table-column
        v-if="getColumnVisible('score')"
        align="center"
        label="分值"
        prop="score"
      />
      <el-table-column
        v-if="getColumnVisible('position')"
        align="center"
        label="岗位"
        prop="position"
      />
      <el-table-column
        v-if="getColumnVisible('level')"
        align="center"
        label="级别"
        prop="level"
      />

      <!-- <el-table-column align="center" label="操作" width="140">
        <template #default="{ row }">
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(row)"
          />
        </template>
      </el-table-column> -->
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="total"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>
<script>
  import { mapGetters, mapState } from 'vuex'
  import { getUser, getProjectList } from '@/api/project/projectInfo'
  import { getPointsDetails } from '@/api/integral/integralList'

  export default {
    name: 'IntegralList',

    data() {
      return {
        loading: false,
        selectedIds: [],
        total: 0,
        tableData: [],
        projectList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          projectId: undefined,
          userId: undefined,
        },
        // 添加列筛选配置
        columnOptions: [
          {
            label: '员工',
            prop: 'username',
            visible: true,
          },
          {
            label: '部门',
            prop: 'departmentName',
            visible: true,
          },
          {
            label: '创建时间',
            prop: 'scoreCreateDate',
            visible: true,
          },
          {
            label: '日期',
            prop: 'duty_listCreateDate',
            visible: true,
          },
          {
            label: '履职项',
            prop: 'departmentName',
            visible: true,
          },
          {
            label: '项目',
            prop: 'projectName',
            visible: true,
          },
          {
            label: '频率',
            prop: 'frequencyCount',
            visible: true,
          },
          {
            label: '分值',
            prop: 'score',
            visible: true,
          },
          {
            label: '岗位',
            prop: 'position',
            visible: true,
          },
          {
            label: '级别',
            prop: 'level',
            visible: true,
          },
        ],
      }
    },

    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
    },

    mounted() {
      // this.queryParams.userId = this.userId
      // this.queryParams.projectId = this.storeProjectId
      this.getList()
      this.goProjectList()
    },

    methods: {
      async getList() {
        // if (!this.storeProjectId) return

        this.loading = true
        try {
          const { code, data } = await getPointsDetails(this.queryParams)

          if (code === 200) {
            this.tableData = data.list
            this.total = data.total
          }
        } catch (error) {
          console.error('Failed to fetch points:', error)
        } finally {
          this.loading = false
        }
      },

      goUserList() {
        getUser({
          pageNum: 1,
          pageSize: 9999,
        }).then((res) => {
          this.storeUserList = res.data.list || []
        })
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },

      handleSelectionChange(selection) {
        this.selectedIds = selection.map((item) => item.id)
      },

      async handleDelete(row) {
        const ids = row ? [row.id] : this.selectedIds
        if (!ids.length) {
          this.$message.warning('请选择要删除的记录')
          return
        }

        try {
          await this.$confirm('确认删除所选记录?', '提示', {
            type: 'warning',
          })

          // await deletePoints(ids.join(','))
          this.$message.success('删除成功')
          this.getList()
        } catch (error) {
          // User canceled or API error
        }
      },

      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.getList()
      },

      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.getList()
      },

      handleDropdown() {
        // Implement dropdown actions
      },

      handleDownloadTemplate() {
        // Implement template download
      },

      showImportDialog() {
        // Implement import dialog
      },

      handleExport() {
        // Implement export
      },

      handleAdd() {
        // Implement add functionality
      },
      handleColumnFilter(command) {
        console.log(command)
        return
      },
      getColumnVisible(prop) {
        const column = this.columnOptions.find((item) => item.prop === prop)
        return column ? column.visible : true
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParams.pageNum = 1
        this.$refs.queryFormRef.resetFields()
        // this.queryParams.userId = this.userId
        // this.queryParams.projectId = this.storeProjectId
        this.handleQuery()
      },
    },
  }
</script>

<style scoped>
  .ml-3 {
    margin-left: 12px;
  }
  .table-operations {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }
</style>
