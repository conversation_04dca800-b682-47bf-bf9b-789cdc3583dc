import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LoginData, LoginResult, VerifyCodeResult, TenantInfo } from './types';
import { UserInfo } from '@/api/system/user/types';
import axios from 'axios';
import { getToken } from '@/utils/auth';

// pc端固定客户端授权id
const clientId = import.meta.env.VITE_APP_CLIENT_ID;
// Vue2项目的服务器地址和认证token
const serverURL = import.meta.env.VITE_APP_SERVER_URL;
const appToken = import.meta.env.VITE_APP_TOKEN;

/**
 * Vue2风格的登录接口 - 使用OAuth2认证
 * @param data {LoginData}
 * @returns
 */
export function login(data: LoginData): AxiosPromise<LoginResult> {
  return axios({
    url: `${serverURL}/swisp-auth-service/oauth/token`,
    method: 'post',
    params: data,
    headers: {
      /**
       * Basic clientId:clientSecret
       * 客户端信息Base64明文(客户端ID:客户端密钥)：hsePdPerformDuty:03bd51200b8511f0a9434fe4b398b706
       * 测试环境客户端信息Base64密文：aHNlLXBkLXBlcmZvcm0tZHV0eTowM2JkNTEyMDBiODUxMWYwYTk0MzRmZTRiMzk4YjcwNg==
       */
      Authorization: appToken
    }
  });
}

// 注册方法
export function register(data: any) {
  const params = {
    ...data,
    clientId: clientId,
    grantType: 'password'
  };
  return request({
    url: '/auth/register',
    headers: {
      isToken: false,
      isEncrypt: true,
      repeatSubmit: false
    },
    method: 'post',
    data: params
  });
}

/**
 * 注销
 */
export function logout() {
  if (import.meta.env.VITE_APP_SSE === 'true') {
    request({
      url: '/resource/sse/close',
      method: 'get'
    });
  }
  return request({
    url: '/auth/logout',
    method: 'post'
  });
}

/**
 * 获取验证码 - Vue2风格
 */
export function getCodeImg(): AxiosPromise<VerifyCodeResult> {
  return request({
    url: `${serverURL}/swisp-auth-service/oauth/captcha?t=${new Date().getTime().toString()}`,
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  });
}

/**
 * 获取短信验证码 - Vue2风格
 */
export function getSmsCode(params: { phoneNumber: string }): AxiosPromise<any> {
  return request({
    url: `${serverURL}/swisp-auth-service/sms/send`,
    headers: {
      isToken: false
    },
    method: 'post',
    data: params
  });
}

/**
 * 第三方登录
 */
export function callback(data: LoginData): AxiosPromise<any> {
  const LoginData = {
    ...data,
    clientId: clientId,
    grantType: 'social'
  };
  return request({
    url: '/auth/social/callback',
    method: 'post',
    data: LoginData
  });
}

// 获取用户详细信息
export function getInfo(): AxiosPromise<UserInfo> {
  const systemType = 'hse-pd-perform-duty'
  return axios({
    url: `${serverURL}/swisp-base-service/api/v1/users/me/client/${systemType}`,
    method: 'get',
    headers: {
      authorization: getToken()
    }
  })
}

// 获取租户列表
export function getTenantList(isToken: boolean): AxiosPromise<TenantInfo> {
  return request({
    url: '/auth/tenant/list',
    headers: {
      isToken: isToken
    },
    method: 'get'
  });
}
