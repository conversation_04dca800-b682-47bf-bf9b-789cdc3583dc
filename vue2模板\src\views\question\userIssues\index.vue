<!-- 履职反馈 -->
<template>
  <div class="app-container">
    <!-- 查询区域 -->

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      row-key="id"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" label="清单名称" prop="sourceNumber" />
      <el-table-column
        align="center"
        label="问题描述"
        prop="issueDescription"
      />
      <el-table-column align="center" label="问题责任人" prop="rectifierName" />
      <el-table-column
        align="center"
        label="整改审核人"
        prop="rectificationReviewerName"
      />
      <el-table-column align="center" label="部门" prop="" />
      <el-table-column align="center" label="项目" prop="" />
      <el-table-column align="center" label="问题生成日期" prop="createdAt" />
      <el-table-column
        align="center"
        label="截止日期"
        prop="requiredCompletionTime"
      />
      <el-table-column align="center" label="状态" prop="currentStatus">
        <template #default="scope">
          <el-tag v-if="scope.row.currentStatus === 0">待审核</el-tag>
          <el-tag v-else-if="scope.row.currentStatus === 1" type="success">
            已通过
          </el-tag>
          <el-tag v-else-if="scope.row.currentStatus === 2" type="warning">
            待整改
          </el-tag>
          <el-tag v-else-if="scope.row.currentStatus === 3" type="danger">
            已驳回
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" prop="type">
        <template #default="scope">
          <el-button
            v-if="scope.row.type"
            type="text"
            @click="handleUpdate(scope.row)"
          >
            查看
          </el-button>
          <el-button
            v-if="scope.row.currentStatus === 0"
            type="text"
            @click="handleUpdate(scope.row)"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />

    <!-- 整改详情 -->
    <Details ref="details" @refreshDataList="getList" />
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { getRectifierQueryList } from '@/api/question/issuesList'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  import { getDictItems } from '@/api/user'
  import Details from './details.vue'
  export default {
    name: 'IssuesList',
    components: {
      Details,
    },
    data() {
      return {
        // 遮罩层
        loading: false,
        // 选中数组
        selectionList: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 责任清单表格数据
        dataList: [],
        tableData: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          deptName: undefined,
          projectName: undefined,
          projectId: undefined,
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          dataTitle: [
            { required: true, message: '数据标题不能为空', trigger: 'blur' },
          ],
          deptName: [
            { required: true, message: '人员单位不能为空', trigger: 'blur' },
          ],
          projectName: [
            { required: true, message: '项目信息不能为空', trigger: 'blur' },
          ],
        },
        dutyList: [],
        processList: [],
        frequencyList: [],
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
    },
    watch: {
      storeProjectId: {
        handler(newVal) {
          this.queryParams.projectId = newVal
          this.getList()
        },
      },
    },
    mounted() {
      this.queryParams.projectId = this.storeProjectId
      this.getList()
      this.getDutyList()
      this.goDictItems('project_state', 'process')
      this.goDictItems('project_frequency', 'frequency')
    },
    methods: {
      /** 查询责任清单列表 */
      async getList() {
        this.loading = true
        this.queryParams.associatedProjectId = this.storeProjectId
        this.queryParams.rectifierId = this.userId
        try {
          if (!this.queryParams.associatedProjectId) return

          const res = await getRectifierQueryList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            this.tableData = data
            this.total = page.totalCount
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryFormRef')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 履职 */
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
      // 获取类型字典
      async goDictItems(type, str) {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: type,
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        if (str === 'process') {
          this.processList = res.data.data.list || []
        } else {
          this.frequencyList = res.data.data.list || []
        }
      },
      async getDutyList() {
        const res = await getDutyInspectionItemsList({
          pageNum: 1,
          pageSize: 9999,
        })
        const { code, data } = res

        if (code === 200) {
          this.dutyList = data
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .mb8 {
    margin-bottom: 8px;
  }
</style>
