<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '扫码履职检查单'"
      :visible.sync="dialogFormVisible"
      width="1000px"
      @close="close"
    >
      <el-form ref="form" label-width="140px" :model="form" :rules="rules">
        <el-row>
          <el-col :span="12">
            <el-form-item label="身份认证：" prop="idCard">
              <!-- <span>拍照功能仅移动端支持</span> -->
              <image-uploader
                ref="imageUploader"
                v-model="imageFiles"
                :limit="10"
                :multiple="true"
                @on-remove="handleImageRemove"
                @on-success="handleImageSuccess"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定位：" prop="location">
              <el-input
                v-model="form.location"
                placeholder="请选择位置"
                readonly
                style="width: 96%"
              >
                <el-button
                  style="width: 100px"
                  slot="append"
                  type="text"
                  @click="getLocation"
                >
                  获取位置
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="人员：" prop="userId">
              <el-select
                v-model="form.userId"
                clearable
                disabled
                filterable
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称：" prop="projectId">
              <el-select
                v-model="form.projectId"
                disabled
                placeholder="请选择"
                style="width: 96%"
                @change="changeResponItem"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.id"
                  :label="item.projectName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="岗位：" prop="positionName">
              <el-input
                v-model="form.positionName"
                placeholder="请输入"
                readonly
                style="width: 96%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="级别：" prop="level">
              <el-input
                v-model="form.level"
                placeholder="请输入"
                readonly
                style="width: 96%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="履职检查项：" prop="templatesInfoId">
              <el-select
                v-model="form.templatesInfoId"
                disabled
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in dutyArray"
                  :key="item.id"
                  :label="item.dutyInspectionItem"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日期：" prop="dutyDate">
              <el-date-picker
                v-model="form.dutyDate"
                placeholder="选择日期时间"
                readonly
                style="width: 96%"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="检查设备/场所："
              prop="inspectedEquipmentOrSite"
            >
              <el-select
                v-model="form.inspectedEquipmentOrSite"
                placeholder="请选择"
                style="width: 96%"
                @change="changeSite"
              >
                <el-option
                  v-for="item in siteList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="设备/场所负责人："
              prop="equipmentOrSiteResponsiblePerson"
            >
              <el-select
                v-model="form.equipmentOrSiteResponsiblePerson"
                clearable
                disabled
                filterable
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查类型：" prop="inspectionType">
              <el-select
                v-model="form.inspectionType"
                clearable
                disabled
                filterable
                placeholder="请选择检查类型"
                style="width: 96%"
                @change="changeDutyTemplate"
              >
                <el-option
                  v-for="item in safeDictList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查内容：" prop="inspectionContent">
              <el-select
                v-model="form.inspectionContent"
                clearable
                disabled
                filterable
                placeholder="请选择"
                style="width: 96%"
                @change="changeInspection"
              >
                <el-option
                  v-for="item in inspectionContentList"
                  :key="item.id"
                  :label="item.inspectionContent"
                  :value="item.id"
                />
              </el-select>
              <!-- <el-input v-model="form.inspectionContent" disabled /> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <div class="inspectionContent" style="margin: 10px 0">
            <span style="color: red">*</span>
            检查内容明细
            <div style="margin: 10px 0">
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <!-- <el-button type="" @click="handleUpdate">导入</el-button> -->
              <!-- <el-button type="danger" @click="handleDelete">删除</el-button> -->
            </div>
          </div>
          <el-table
            border
            :data="form.inspectionContentList"
            style="margin-bottom: 20px"
          >
            <el-table-column align="center" label="明细要求" prop="process">
              <template #default="scope">
                <el-select
                  v-model="scope.row.inspectionRequirements"
                  clearable
                  filterable
                  placeholder="请选择"
                  style="width: 96%"
                  @change="
                    changeInspectionInfo(
                      scope.row.inspectionRequirements,
                      scope.$index
                    )
                  "
                >
                  <el-option
                    v-for="item in inspectionInfoDtoList"
                    :key="item.id"
                    :label="item.inspectionRequirements"
                    :value="item.id"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="检查项"
              prop="inspectionItem"
            />
          </el-table>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否合格：" prop="qualified">
              <el-radio-group v-model="form.qualified">
                <el-radio :label="true">合格</el-radio>
                <el-radio :label="false">不合格</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="form.qualified === false" :span="12">
            <el-form-item label="要求整改时间：" prop="requiredCompletionTime">
              <el-date-picker
                v-model="form.requiredCompletionTime"
                placeholder="选择日期时间"
                style="width: 96%"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.qualified === false">
          <el-col :span="12">
            <el-form-item label="检查问题描述：" prop="issueDescription">
              <el-input v-model="form.issueDescription" style="width: 96%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改人：" prop="rectifierId">
              <el-select
                v-model="form.rectifierId"
                clearable
                filterable
                placeholder="请选择"
                style="width: 96%"
                @change="handlePersonChange"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.qualified === false">
          <el-col :span="12">
            <el-form-item
              label="隐患违章问题："
              prop="hiddenDangersViolationsNum"
            >
              <el-select
                v-model="form.hiddenDangersViolationsNum"
                clearable
                filterable
                placeholder="请选择检查类型"
                style="width: 96%"
                @change="changeHiddenDanger"
              >
                <el-option
                  v-for="item in hiddenList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="因素级别：" prop="facotrLevel">
              <el-select
                v-model="form.facotrLevel"
                clearable
                disabled
                filterable
                placeholder="请选择检查类型"
                style="width: 96%"
                @change="changeFactorLevel"
              >
                <el-option
                  v-for="item in factorsList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.qualified === false">
          <el-col :span="24">
            <el-form-item label="具体描述：" prop="specificDesc">
              <el-input
                v-model="form.specificDesc"
                :rows="4"
                style="width: 98%"
                type="textarea"
              />
              <div>
                ，违反了《东方地球物理公司HSE违章行为管理规定》第&nbsp;{{
                  form.hiddenDangersViolationsNum || ' '
                }}&nbsp;条
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.qualified === false">
          <el-col :span="12">
            <el-form-item
              class="logo"
              label="现场照片或视频："
              prop="onSitePhotosOrVideos"
            >
              <!-- <el-upload
                :action="uploadUrl"
                :before-upload="beforeAvatarUpload"
                class="avatar-uploader"
                :headers="headerObj"
                :on-success="handleAvatarSuccess"
                :show-file-list="false"
              >
                <img
                  v-if="form.onSitePhotosOrVideos"
                  class="avatar"
                  :src="form.onSitePhotosOrVideos"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload> -->
              <file-uploader
                ref="fileUploader"
                v-model="uploadedFiles"
                @on-remove="handleFileRemove"
                @on-success="handleFileSuccess"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" />
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save(true)">暂 存</el-button>
        <el-button type="primary" @click="save(false)">提 交</el-button>
      </template>
    </el-dialog>
    <!-- 添加地图选点组件 -->
    <map-picker ref="mapPicker" @confirm="handleLocationConfirm" />
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { baseURL, buketName } from '@/config'
  import { getToken } from '@/utils/token'
  import { getDictItems } from '@/api/user'
  import { getProjectList } from '@/api/project/projectInfo'
  import {
    getDutyTemplatesList,
    getDutyTemplatesInfo,
  } from '@/api/perform/performanceTemplate'
  import {
    scanCodeOfDuties,
    getUserDutyDetailsTempById,
  } from '@/api/perform/userResponsibility'
  import { getSafetyInspectionStandardsList } from '@/api/perform/safetyCheck'
  import { getEquipmentSitesList } from '@/api/system/facilities'
  import MapPicker from '@/components/MapPicker.vue'
  import FileUploader from '@/components/FileUploader'
  import ImageUploader from '@/components/ImageUploader'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  import moment from 'moment'
  export default {
    name: '',
    components: {
      MapPicker,
      FileUploader,
      ImageUploader,
    },
    props: {
      dutyList: {
        type: Array,
        default: () => [],
      },
      processList: {
        type: Array,
        default: () => [],
      },
      frequencyList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {},
        content: '',
        rules: {
          // idCard: [{ required: true, trigger: 'blur', message: '请选择' }],
          location: [{ required: true, trigger: 'change', message: '请选择' }],
          userId: [{ required: true, trigger: 'change', message: '请选择' }],
          templatesInfoId: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          approverId: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          completionDescription: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          // inspectionType: [
          //   { required: true, trigger: 'change', message: '请选择' },
          // ],
          inspectedEquipmentOrSite: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          // equipmentOrSiteResponsiblePerson: [
          //   { required: true, trigger: 'change', message: '请选择' },
          // ],
          // inspectionContent: [
          //   { required: true, trigger: 'change', message: '请选择' },
          // ],
          hiddenDangersViolationsNum: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          facotrLevel: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
        },
        title: '',
        dialogFormVisible: false,
        //上传
        uploadUrl: `${baseURL}swisp-base-service/api/v1/oss/ali/upload?subPath=perform-file/${moment().format(
          'YYYY/MM/DD'
        )}&buketName=${buketName}`,
        headerObj: {
          Authorization: `${getToken()}`,
        },
        projectList: [],
        safeDictList: [],
        templatesList: [],
        siteList: [],
        inspectionContentList: [],
        inspectionInfoDtoList: [],
        uploadedFiles: [],
        imageFiles: [],
        dutyArray: [],
        hiddenList: [],
        factorsList: [],
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
        userInfo: 'user/user',
      }),
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
    },
    watch: {
      inspectionContentList: {
        handler(val) {
          if (val.length > 0 && this.form.inspectionContent) {
            const obj = this.inspectionContentList.find(
              (item) => item.id == this.form.inspectionContent
            )
            if (obj) {
              this.inspectionInfoDtoList = obj.safetyInspectionInfoDtoList || []
            }
          }
        },
        immediate: true,
      },
      dutyList: {
        handler(val) {
          if (val && val.length) {
            this.dutyArray = val
          } else {
            this.getDutyList()
          }
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      async showEdit(row) {
        this.title = ''
        this.uploadedFiles = []
        this.imageFiles = []
        if (row) {
          // this.form = {
          //   ...row,
          //   inspectionContentList: [],
          // }
          await this.goDetails(row)
        } else {
          this.form = {
            ...this.$options.data().form,
            inspectionContentList: [],
          }
        }
        this.form.dutyDate = moment().format('YYYY-MM-DD HH:mm:ss')
        this.dialogFormVisible = true
        this.goProjectList()
        // this.goTemplatesList()
        this.goDictItems()
        this.goSiteList()
        this.goHiddenProblem()
        this.goFactorLevel()
      },
      goDetails(row) {
        try {
          getUserDutyDetailsTempById(row.listId).then((res) => {
            this.form = res.data || row
            if (this.form.inspectionType || this.form.inspectionType === 0) {
              this.changeDutyTemplate(this.form.inspectionType)
            }
            this.form = {
              ...this.form,
              ...this.form.dutyListInfo,
              ...this.form.issueFeedbackAndRectificationDto,
              templatesInfoId: this.form.dutyInspectionItemCont?.id,
              dutyInspectionItemCont: row.dutyInspectionItemCont,
              dutyDate: this.form.dutyDate
                ? this.form.dutyDate
                : moment().format('YYYY-MM-DD HH:mm:ss'),

              inspectionContentList:
                this.form.safetyInspectionInfoDtoList || [],
              inspectionType:
                this.form.inspectionType || this.form.inspectionType === 0
                  ? this.form.inspectionType + ''
                  : null,
              inspectionContent: this.form.inspectionContent
                ? this.form.inspectionContent * 1
                : null,
              inspectedEquipmentOrSite: this.form.inspectedEquipmentOrSite
                ? this.form.inspectedEquipmentOrSite * 1
                : null,
              equipmentOrSiteResponsiblePerson: this.form
                .equipmentOrSiteResponsiblePerson
                ? this.form.equipmentOrSiteResponsiblePerson * 1
                : null,
              issueFeedbackAndRectificationDtoId: this.form
                .issueFeedbackAndRectificationDto
                ? this.form.issueFeedbackAndRectificationDto?.id
                : undefined,
              hiddenDangersViolationsNum:
                this.form.issueFeedbackAndRectificationDto &&
                this.form.issueFeedbackAndRectificationDto
                  .hiddenDangersViolationsNum &&
                this.form.issueFeedbackAndRectificationDto
                  .hiddenDangersViolationsNum + '',
            }
            if (
              this.form.onSitePhotosOrVideosAttach &&
              this.form.onSitePhotosOrVideosAttach.attachInfoList &&
              this.form.onSitePhotosOrVideosAttach.attachInfoList.length
            ) {
              this.uploadedFiles =
                this.form.onSitePhotosOrVideosAttach.attachInfoList
            }
            if (
              this.form.idcardImgsAttach &&
              this.form.idcardImgsAttach.attachInfoList &&
              this.form.idcardImgsAttach.attachInfoList.length
            ) {
              this.imageFiles = this.form.idcardImgsAttach.attachInfoList
            }
          })
        } catch (error) {
          this.form = row
          this.form.inspectionType =
            this.form.inspectionType || this.form.inspectionType === 0
              ? this.form.inspectionType + ''
              : null
          this.form.dutyDate = moment().format('YYYY-MM-DD HH:mm:ss')
        }
      },
      async getDutyList() {
        const res = await getDutyInspectionItemsList({
          pageNum: 1,
          pageSize: 9999,
        })
        const { code, data } = res

        if (code === 200) {
          this.dutyArray = data
        }
      },

      close() {
        this.uploadedFiles = []
        this.imageFiles = []
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save(flag) {
        let list = []
        if (
          this.form.inspectionContentList &&
          this.form.inspectionContentList.length > 0
        ) {
          this.form.inspectionContentList.forEach((item) => {
            list.push(item.inspectionRequirements)
          })
        }
        if (!flag) {
          if (
            !this.form.idcardImgsAttach ||
            !this.form.idcardImgsAttach.attachInfoList ||
            !this.form.idcardImgsAttach.attachInfoList.length
          ) {
            this.$message({
              type: 'warning',
              message: '请上传证件照',
            })
            return
          }
          if (
            (!this.form.onSitePhotosOrVideosAttach ||
              !this.form.onSitePhotosOrVideosAttach.attachInfoList ||
              !this.form.onSitePhotosOrVideosAttach.attachInfoList.length) &&
            this.form.qualified === false
          ) {
            this.$message({
              type: 'warning',
              message: '请上传现场照片或视频',
            })
            return
          }
        }
        this.form.projectId = this.form.projectId || this.storeProjectId
        this.form.qualified = this.form.qualified || false
        this.form.allocateUserId = this.userId
        // this.form.idCard = '拍照功能仅移动端支持'
        const formData = {
          ...this.form,
          inspectionContentList: JSON.stringify(list),
          issueFeedbackAndRectificationDto: {
            requiredCompletionTime: this.form.requiredCompletionTime,
            issueDescription: this.form.issueDescription,
            rectifierId: this.form.rectifierId,
            rectifierName: this.form.rectifierName,
            feedbackPersonId: this.userId,
            feedbackPersonName: this.userInfo.nickname,
            rectificationReviewerName: this.form.rectificationReviewerName,
            onSitePhotosOrVideosAttach:
              this.form.onSitePhotosOrVideosAttach || {},
            hiddenDangersViolationsNum: this.form.hiddenDangersViolationsNum,
            hiddenDangersViolations: this.form.hiddenDangersViolations,
            facotrLevel: this.form.facotrLevel,
            facotrLevelDesc: this.form.facotrLevelDesc,
            specificDesc: this.form.specificDesc,
            id: this.form.issueFeedbackAndRectificationDtoId,
          },
          idcardImgsAttach: this.form.idcardImgsAttach || {},
          id: undefined,
          dataStatus: flag ? false : true,
          requiredCompletionTime: undefined,
          issueDescription: undefined,
          rectifierId: undefined,
          onSitePhotosOrVideosAttach: undefined,
          rectificationReviewerName: undefined,
          idCard: undefined,
          responsibilityListName: undefined,
          hiddenDangersViolationsNum: undefined,
          hiddenDangersViolations: undefined,
          facotrLevel: undefined,
          facotrLevelDesc: undefined,
          specificDesc: undefined,
          userId: undefined,
          userName: undefined,
          projectName: undefined,
          positionId: undefined,
          positionName: undefined,
          level: undefined,
          dutyTemplateId: undefined,
          dutyTemplateName: undefined,
          dutyStatus: undefined,
          allocateUserId: undefined,
          allocateUserName: undefined,
          allocateTime: undefined,
          userConfirmationStatus: undefined,
          rejectCause: undefined,
          userConfirmationTime: undefined,
          dutyTemplatesInfoDtoList: undefined,
          userDutyListsInfoDtoList: undefined,
          approverConfirmationStatus: undefined,
          approverRejectCause: undefined,
          approverConfirmationTime: undefined,
          type: undefined,
          process: undefined,
          frequency: undefined,
          frequencyCount: undefined,
          templatesInfoId: undefined,
          isFirst: undefined,
          rowspan: undefined,
        }
        if (flag) {
          scanCodeOfDuties(formData).then(() => {
            this.$emit('refreshDataList')
            this.close()
          })
        } else {
          this.$refs['form'].validate((valid) => {
            if (valid) {
              scanCodeOfDuties(formData).then(() => {
                this.$message.success('扫码履职成功')
                this.$emit('refreshDataList')
                this.close()
              })
            } else {
              return false
            }
          })
        }
      },
      handleAdd() {
        this.form.inspectionContentList.push({
          inspectionItem: '',
          inspectionRequirements: '',
        })
      },
      handleDelete(index) {
        this.form.inspectionContentList.splice(index, 1)
      },
      handleAvatarSuccess(res) {
        this.formData.clientLogo = res.data.path
        this.formData.clientLogoUrl = res.data.file
      },
      beforeAvatarUpload(file) {
        const isLt20M = file.size / 1024 / 1024 < 50
        if (!isLt20M) {
          this.$message.error('上传附件大小不能超过 50MB!')
        }
        return isLt20M
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      goTemplatesList() {
        getDutyTemplatesList().then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.templatesList = data
          }
        })
      },
      goDutyTemplatesInfo(id) {
        getDutyTemplatesInfo(id).then((res) => {
          this.form.dutyTemplatesInfoDtoList =
            res.data?.dutyTemplatesInfoDtoList
        })
      },
      goSiteList() {
        getEquipmentSitesList({ pageNum: 1, pageSize: 1000 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.siteList = data
          }
        })
      },
      handlePersonChange(e) {
        const obj = this.storeUserList.find((item) => item.userId === e)
        if (obj) {
          this.form.rectifierName = obj.nickname
        }
      },
      changeResponItem(e) {
        const obj = this.projectList.find((item) => item.id === e)
        if (obj) {
          this.form.projectName = obj.projectName
        }
      },
      async changeDutyTemplate(e) {
        this.form.inspectionContentList = []
        this.inspectionContentList = []
        this.form.inspectionContent = ''
        const obj = this.safeDictList.find((item) => item.id === e)
        if (obj) {
          this.$set(this.form, 'inspectionContent', obj.name)
        }
        try {
          const res = await getSafetyInspectionStandardsList({
            pageNum: 1,
            pageSize: 9999,
            inspectionType: e,
          })
          if (res.code === 200) {
            this.inspectionContentList = res.data || []
          }
        } catch (error) {
          console.error('获取检查内容列表失败:', error)
          this.$message.error('获取检查内容列表失败')
        }
      },
      changeSite(e) {
        const obj = this.siteList.find((item) => item.id === e)
        if (obj) {
          if (obj.inspectionType || obj.inspectionType === 0) {
            this.changeDutyTemplate(obj.inspectionType)
          }
          this.$set(
            this.form,
            'equipmentOrSiteResponsiblePerson',
            obj.responsiblePersonId || ''
          )
          this.$set(
            this.form,
            'inspectionType',
            obj.inspectionType || obj.inspectionType === 0
              ? obj.inspectionType + ''
              : ''
          )
          this.$set(
            this.form,
            'inspectionContent',
            obj.inspectionContent ? obj.inspectionContent * 1 : ''
          )
        }
      },
      changeInspection(e) {
        this.form.inspectionContentList = []
        const obj = this.inspectionContentList.find((item) => item.id == e)
        if (obj) {
          this.inspectionInfoDtoList = obj.safetyInspectionInfoDtoList || []
        }
      },
      changeInspectionInfo(e, index) {
        const obj = this.inspectionInfoDtoList.find((item) => item.id === e)
        if (obj) {
          this.$set(
            this.form.inspectionContentList[index],
            'inspectionItem',
            obj.inspectionItem
          )
        }
      },
      changeHiddenDanger(e) {
        const obj = this.hiddenList.find((item) => item.value === e)
        if (obj) {
          this.form.hiddenDangersViolations = obj.name
          if (obj.remark) {
            const obj2 = this.factorsList.find(
              (item) => item.name == obj.remark
            )
            if (obj2) {
              this.$set(this.form, 'facotrLevel', obj2.id)
              this.$set(this.form, 'facotrLevelDesc', obj2.name)
            }
          }
        }
      },
      changeFactorLevel(e) {
        const obj = this.factorsList.find((item) => item.id === e)
        if (obj) {
          this.form.facotrLevelDesc = obj.name
        }
      },
      // 获取类型字典
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'safe_check_type',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.safeDictList = res.data.data.list || []
      },
      // 隐患问题列表
      async goHiddenProblem() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'hse_violation_level',
          clientId: 'hse-supervision',
        }
        const res = await getDictItems(params)
        this.hiddenList = res.data.data.list || []
      },
      // 因素级别列表
      async goFactorLevel() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'yh_level',
          clientId: 'hse-supervision',
        }
        const res = await getDictItems(params)
        this.factorsList = res.data.data.list || []
      },
      getLocation() {
        this.$refs.mapPicker.show()
      },
      handleLocationConfirm(location) {
        // this.form.longitude = location.longitude
        // this.form.latitude = location.latitude
        this.$set(this.form, 'location', location.address)
      },
      handleFileSuccess(fileInfo) {
        if (!this.form.onSitePhotosOrVideosAttach) {
          this.form.onSitePhotosOrVideosAttach = {
            code: '',
            attachInfoList: [],
          }
        }

        this.form.onSitePhotosOrVideosAttach.attachInfoList = [
          ...this.uploadedFiles,
        ]

        this.$message.success(`文件 ${fileInfo.name} 上传成功`)
      },
      handleFileRemove(file) {
        if (this.form.onSitePhotosOrVideosAttach) {
          this.form.onSitePhotosOrVideosAttach.attachInfoList = [
            ...this.uploadedFiles,
          ]
        }

        this.$message.info(`文件 ${file.name} 已移除`)
      },
      handleImageSuccess(fileInfo) {
        if (!this.form.idcardImgsAttach) {
          this.form.idcardImgsAttach = {
            code: '',
            attachInfoList: [],
          }
        }

        this.form.idcardImgsAttach.attachInfoList = [...this.imageFiles]

        this.$message.success(`图片 ${fileInfo.name} 上传成功`)
      },

      handleImageRemove(file) {
        if (this.form.idcardImgsAttach) {
          this.form.idcardImgsAttach.attachInfoList = [...this.imageFiles]
        }

        this.$message.info(`图片 ${file.name} 已移除`)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
