<!-- 责任清单 -->
<template>
  <div class="app-container">
    <!-- 查询区域 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item>
        <el-button icon="el-icon-plus" type="success" @click="handleAdd">
          新增
        </el-button>
        <el-button
          :disabled="single"
          icon="el-icon-delete"
          type="danger"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectId">
        <el-select
          v-model="queryParams.projectId"
          clearable
          filterable
          placeholder="请选择"
          @change="goUserByProject"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            clearable
            filterable
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="userByProjectList && userByProjectList.length"
        label="人员"
        prop="userId"
      >
        <el-select
          v-model="queryParams.userId"
          clearable
          filterable
          placeholder="请选择"
          style="width: 200px"
          @change="changeUser"
        >
          <el-option
            v-for="item in userByProjectList"
            :key="item.employeeId"
            :label="item.employeeName"
            :value="item.employeeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="部门" prop="deptIds">
        <el-cascader
          v-model="deptIdArray"
          clearable
          filterable
          :options="deptList"
          placeholder="所属部门"
          :props="{ checkStrictly: true, multiple: true, emitPath: false }"
          :show-all-levels="false"
          style="width: 96%"
          @change="handleDeptChange"
        />
      </el-form-item>
      <el-form-item label="时间段" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          style="width: 300px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="履职状态" prop="dutyStatus">
        <el-select
          v-model="queryParams.dutyStatus"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option label="待确认" :value="0" />
          <el-option label="履职中" :value="1" />
          <el-option label="已履职" :value="2" />
          <el-option label="已驳回" :value="-1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">
          查询
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格筛选按钮 -->
    <div class="table-operations">
      <el-dropdown @command="handleColumnFilter">
        <el-tag style="cursor: pointer">
          <i class="el-icon-s-operation"></i>
          筛选
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-tag>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="item in columnOptions"
            :key="item.prop"
            :command="item.prop"
          >
            <el-checkbox v-model="item.visible" @click.stop>
              {{ item.label }}
            </el-checkbox>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      row-key="id"
      :span-method="objectSpanMethod"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <!-- <el-table-column
        v-if="getColumnVisible('index')"
        align="center"
        label="序号"
        type="index"
        width="50"
      /> -->
      <el-table-column
        v-if="getColumnVisible('projectName')"
        align="center"
        label="数据标题"
        prop="projectName"
      >
        <template slot-scope="scope">
          {{ scope.row.userName }} - {{ scope.row.projectName }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="getColumnVisible('dutyStatus')"
        align="center"
        label="用户确认状态"
        prop="dutyStatus"
        width="120px"
      >
        <template #default="scope">
          <el-tag v-if="scope.row.dutyStatus === 0">待确认</el-tag>
          <el-tag v-else-if="scope.row.dutyStatus === 1" type="primary">
            履职中
          </el-tag>
          <el-tag v-else-if="scope.row.dutyStatus === 2" type="success">
            已履职
          </el-tag>
          <el-tag v-else-if="scope.row.dutyStatus === -1" type="danger">
            已驳回
          </el-tag>
          <el-tag v-else-if="scope.row.dutyStatus === -2" type="info">
            暂存
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getColumnVisible('userName')"
        align="center"
        label="人员"
        prop="userName"
      />
      <el-table-column
        v-if="getColumnVisible('projectName')"
        align="center"
        label="项目名称"
        prop="projectName"
      />
      <el-table-column
        v-if="getColumnVisible('checkItem')"
        align="center"
        label="安全生产责任清单明细"
        prop="checkItem"
      >
        <!-- <el-table-column label="是否完成" prop="isComplete">
          <el-tag
            v-model="form.isComplete"
            :type="form.isComplete === 'true' ? 'success' : 'danger'"
          >
            {{ form.isComplete ? '是' : '否' }}
          </el-tag>
        </el-table-column> -->
        <el-table-column
          v-if="getColumnVisible('templatesInfoId')"
          align="center"
          label="履职检查项"
          prop="templatesInfoId"
          width="260px"
        >
          <template #default="scope">
            {{ scope.row?.dutyInspectionItemCont?.dutyInspectionItem }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="getColumnVisible('type')"
          align="center"
          label="类型"
          prop="type"
          width="80px"
        >
          <template #default="scope">
            <span v-if="scope.row.type">扫码履职</span>
            <span v-else-if="scope.row.type === 0">自证履职</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="getColumnVisible('process')"
          align="center"
          label="工序"
          prop="process"
          width="80px"
        >
          <template #default="scope">
            <span v-for="item in processList" :key="item.id">
              <span v-if="scope.row.process === item.value">
                {{ item.name }}
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="getColumnVisible('frequency')"
          align="center"
          label="履职时间/频率"
          prop="frequency"
        >
          <template #default="scope">
            <span v-for="item in frequencyList" :key="item.id">
              <span v-if="scope.row.frequency === item.value">
                {{ item.name }}
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="getColumnVisible('frequencyCount')"
          align="center"
          label="要求频次"
          prop="frequencyCount"
          width="80px"
        />
        <el-table-column
          v-if="getColumnVisible('completedTimes')"
          label="已完成次数"
          prop="completedTimes"
          width="100px"
        />
        <el-table-column
          v-if="getColumnVisible('cycleCompletedTimes')"
          align="center"
          label="周期内完成次数"
          prop="cycleCompletedTimes"
          width="130px"
        />
        <!-- <el-table-column label="工序是否停用" prop="process" /> -->
        <!-- <el-table-column label="分值" prop="score" /> -->
      </el-table-column>
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        label="操作"
        width="120"
      >
        <template slot-scope="scope">
          <el-button
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <Details
      ref="details"
      :duty-list="dutyList"
      :frequency-list="frequencyList"
      :process-list="processList"
      @refreshDataList="getList"
    />
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import {
    getUserDutyList,
    delUserDuty,
  } from '@/api/perform/responsibilityList'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  import { getDictItems } from '@/api/user'
  import Details from './details.vue'
  import { getProjectList, getUserByProject } from '@/api/project/projectInfo'
  import { listSelectDepartments } from '@/api/system/dept'

  export default {
    name: 'ResponsibilityList',
    components: {
      Details,
    },
    data() {
      return {
        // 遮罩层
        loading: false,
        // 选中数组
        selectionList: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 责任清单表格数据
        dataList: [],
        tableData: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          userId: undefined,
          projectName: undefined,
          projectId: undefined,
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          dataTitle: [
            { required: true, message: '数据标题不能为空', trigger: 'blur' },
          ],
          deptName: [
            { required: true, message: '人员单位不能为空', trigger: 'blur' },
          ],
          projectName: [
            { required: true, message: '项目信息不能为空', trigger: 'blur' },
          ],
        },
        // 添加列筛选配置
        columnOptions: [
          // { label: '选择框', prop: 'selection', visible: true },
          // { label: '序号', prop: 'index', visible: true },
          { label: '数据标题', prop: 'dataTitle', visible: true },
          { label: '用户确认状态', prop: 'dutyStatus', visible: true },
          { label: '人员', prop: 'userName', visible: true },
          { label: '项目信息', prop: 'projectName', visible: true },
          { label: '履职检查项', prop: 'templatesInfoId', visible: true },
          { label: '类型', prop: 'type', visible: true },
          { label: '工序', prop: 'process', visible: true },
          { label: '履职时间/频率', prop: 'frequency', visible: true },
          { label: '要求频次', prop: 'frequencyCount', visible: true },
          { label: '已完成次数', prop: 'completedTimes', visible: true },
          {
            label: '周期内完成次数',
            prop: 'cycleCompletedTimes',
            visible: true,
          },
          { label: '操作', prop: 'operation', visible: true },
        ],
        dutyList: [],
        processList: [],
        frequencyList: [],
        projectList: [],
        userByProjectList: [],
        person: '1',
        deptList: [], // 部门列表
        timeRange: [], // 时间范围
        deptIdArray: [], // 用于存储级联选择器的值
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
    },
    // watch: {
    //   storeProjectId: {
    //     handler(newVal) {
    //       this.queryParams.projectId = newVal
    //       this.getList()
    //     },
    //   },
    // },
    async mounted() {
      // this.queryParams.projectId = this.storeProjectId
      await this.goProjectList()
      this.getList()
      this.getDutyList()
      this.getDeptList()
      this.goDictItems('project_state', 'process')
      this.goDictItems('project_frequency', 'frequency')
    },
    methods: {
      /** 查询责任清单列表 */
      async getList() {
        this.loading = true
        this.dataList = []
        this.tableData = []
        this.total = 0
        // this.queryParams.projectId = this.storeProjectId
        // this.queryParams.allocateUserId = this.userId
        try {
          // if (!this.queryParams.projectId || !this.queryParams.allocateUserId)
          //   return

          const res = await getUserDutyList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            this.dataList = data
            this.total = page.totalCount || 0
            // 处理表格数据
            this.handleTableData()
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      // 处理表格数据
      handleTableData() {
        this.tableData = []
        this.dataList.forEach((item) => {
          if (
            item.userDutyListsInfoDtoList &&
            item.userDutyListsInfoDtoList.length
          ) {
            // 获取责任清单明细列表
            const detailList = item.userDutyListsInfoDtoList
            // 判断是否超过5条
            const showMore = detailList.length > 5
            // 最多显示5条
            const displayList = showMore ? detailList.slice(0, 5) : detailList

            // 添加前5条数据
            displayList.forEach((detail, index) => {
              this.tableData.push({
                ...item,
                templatesInfoId: detail.templatesInfoId,
                dutyInspectionItemCont: detail.dutyInspectionItemCont,
                type: detail.type,
                process: detail.process,
                frequency: detail.frequency,
                frequencyCount: detail.frequencyCount,
                completedTimes: detail.completedTimes,
                cycleCompletedTimes: detail.cycleCompletedTimes,
                // process: detail.process,
                isFirst: index === 0,
                rowspan: showMore ? 6 : detailList.length, // 如果显示更多，则rowspan需要+1（5条数据+1条省略号）
              })
            })

            // 如果超过5条，添加一条显示省略号的记录
            if (showMore) {
              this.tableData.push({
                ...item,
                templatesInfoId: '...',
                dutyInspectionItemCont: { dutyInspectionItem: '...' },
                type: null,
                process: null,
                frequency: null,
                frequencyCount: null,
                completedTimes: null,
                cycleCompletedTimes: null,
                isFirst: false,
                rowspan: 0,
                isEllipsis: true, // 标记为省略号行
              })
            }
          } else {
            this.tableData.push({
              ...item,
              isFirst: true,
              rowspan: 1,
            })
          }
        })
      },
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        console.log(row, column, rowIndex, columnIndex)
        if (
          columnIndex === 0 ||
          columnIndex === 1 ||
          columnIndex === 2 ||
          columnIndex === 3 ||
          columnIndex === 4 ||
          columnIndex === 12
        ) {
          if (row.isFirst) {
            return {
              rowspan: row.rowspan,
              colspan: 1,
            }
          } else {
            return {
              rowspan: 0,
              colspan: 0,
            }
          }
        }
      },
      getDeptList() {
        // 从API获取部门列表
        listSelectDepartments().then((response) => {
          this.deptList = response.data || []
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        // 处理时间范围
        if (this.timeRange && this.timeRange.length === 2) {
          this.queryParams.queryStartTime = this.timeRange[0] + ' 00:00:00'
          this.queryParams.queryEndTime = this.timeRange[1] + ' 23:59:59'
        } else {
          this.queryParams.queryStartTime = undefined
          this.queryParams.queryEndTime = undefined
        }
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.$refs.queryFormRef.resetFields()
        this.timeRange = []
        this.userByProjectList = []
        this.deptIdArray = []
        this.queryParams.deptIdArray = undefined
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.$refs.details.showEdit()
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        let idstr = ''
        if (row) {
          idstr = row.id
        } else {
          const ids = this.selectionList.map((item) => item.id)
          if (ids.length === 0) {
            this.$message.warning('请选择要删除的记录')
            return
          }
          idstr = ids.join(',')
        }

        this.$confirm('确认删除所选中数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          delUserDuty({ ids: idstr }).then(() => {
            this.$message.success('删除成功')
            this.getList()
          })
        })
      },
      // 获取类型字典
      async goDictItems(type, str) {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: type,
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        if (str === 'process') {
          this.processList = res.data.data.list || []
        } else {
          this.frequencyList = res.data.data.list || []
        }
      },
      async getDutyList() {
        const res = await getDutyInspectionItemsList({
          pageNum: 1,
          pageSize: 9999,
        })
        const { code, data } = res

        if (code === 200) {
          this.dutyList = data
        }
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.getList()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.getList()
      },
      handleColumnFilter(command) {
        console.log(command)
        return
      },
      getColumnVisible(prop) {
        const column = this.columnOptions.find((item) => item.prop === prop)
        return column ? column.visible : true
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
            // this.queryParams.projectId =
            //   this.storeProjectId || data[0].projectId
          }
        })
      },
      goUserByProject() {
        this.queryParams.userId = null
        if (!this.queryParams.projectId) {
          this.userByProjectList = []
          return
        }
        getUserByProject({
          projectId: this.queryParams.projectId,
          pageNum: 1,
          pageSize: 9999,
        }).then((res) => {
          const { code, data } = res
          if (code === 200 && data.relationship !== 2) {
            this.userByProjectList = data
          } else {
            this.userByProjectList = []
          }
        })
      },
      changeUser() {
        // console.log('所选项', e)
      },
      handleDeptChange(value) {
        if (value && value.length > 0) {
          this.queryParams.deptIdArray = value.join(',')
        } else {
          this.queryParams.deptIdArray = undefined
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .mb8 {
    margin-bottom: 8px;
  }

  .table-operations {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }
</style>
