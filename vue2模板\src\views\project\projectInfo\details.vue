<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '项目'"
      :visible.sync="dialogFormVisible"
      width="1000px"
      @close="close"
    >
      <el-form
        ref="form"
        label-position="top"
        label-width="130px"
        :model="form"
        :rules="rules"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称：" prop="linkProjectId">
              <el-select
                v-model="form.linkProjectId"
                placeholder="请选择"
                style="width: 96%"
                @change="changeProject"
              >
                <el-option
                  v-for="item in baseProjectList"
                  :key="item.projectCode"
                  :label="item.projectName"
                  :value="item.projectCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间：" prop="startDate">
              <el-date-picker
                v-model="form.startDate"
                clearable
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期"
                style="width: 96%"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="安全责任人："
              prop="safetyResponsiblePersonArr"
            >
              <el-select
                v-model="form.safetyResponsiblePersonArr"
                filterable
                multiple
                placeholder="请选择"
                style="width: 96%"
                @change="
                  (val) => handlePersonChange(val, 'safetyResponsiblePerson')
                "
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目管理员：" prop="projectAdminArr">
              <el-select
                v-model="form.projectAdminArr"
                filterable
                multiple
                placeholder="请选择"
                style="width: 96%"
                @change="(val) => handlePersonChange(val, 'projectAdmin')"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="项目重点生产安全风险防控责任清单"
              prop="riskResponsibilityDtoList"
            >
              <div class="btn" style="margin-bottom: 10px">
                <el-button size="mini" type="primary" @click="addList">
                  新增
                </el-button>
                <el-button size="mini" type="danger" @click="delList">
                  删除
                </el-button>
                <!-- <el-button size="mini" @click="importList">导入</el-button> -->
                <el-dropdown
                  split-button
                  style="margin: 0px 10px"
                  @command="handleDropdown"
                >
                  导入
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        command="handleDownloadTemplate"
                        icon="el-icon-download"
                      >
                        下载模板
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="showImportDialog"
                        icon="el-icon-top"
                      >
                        导入数据
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              <el-table
                border
                :data="form.riskResponsibilityDtoList"
                style="width: 100%"
              >
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column label="项目风险名称" prop="riskName">
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.riskName"
                      placeholder="请输入"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="方案名称/管控措施"
                  prop="schemeMeasures"
                >
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.schemeMeasures"
                      placeholder="请输入"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="关键环节" prop="keyLink">
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.keyLink"
                      placeholder="请输入"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="责任领导" prop="responsibilityLeader">
                  <template #default="scope">
                    <el-select
                      v-model="scope.row.responsibilityLeaderArr"
                      filterable
                      multiple
                      placeholder="请选择"
                      @change="
                        changeResponItem(
                          scope.row.responsibilityLeaderArr,
                          scope.$index,
                          'responsibilityLeader'
                        )
                      "
                    >
                      <el-option
                        v-for="item in storeUserList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.userId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="协管领导" prop="assistLeader">
                  <template #default="scope">
                    <el-select
                      v-model="scope.row.assistLeaderArr"
                      filterable
                      multiple
                      placeholder="请选择"
                      @change="
                        changeResponItem(
                          scope.row.assistLeaderArr,
                          scope.$index,
                          'assistLeader'
                        )
                      "
                    >
                      <el-option
                        v-for="item in storeUserList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.userId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="直线管理人员" prop="lineManager">
                  <template #default="scope">
                    <el-select
                      v-model="scope.row.lineManagerArr"
                      filterable
                      multiple
                      placeholder="请选择"
                      @change="
                        changeResponItem(
                          scope.row.lineManagerArr,
                          scope.$index,
                          'lineManager'
                        )
                      "
                    >
                      <el-option
                        v-for="item in storeUserList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.userId"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="50">
                  <template #default="scope">
                    <el-button
                      circle
                      icon="el-icon-delete"
                      size="mini"
                      type="danger"
                      @click="handleDelete(scope.$index)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </template>

      <!-- 导入 -->
      <el-dialog
        append-to-body
        :close-on-click-modal="false"
        :title="importDialog.title"
        :visible.sync="importDialog.visible"
        width="600px"
        @close="closeImportDialog"
      >
        <el-form ref="importFormRef" label-width="80px" :model="importFormData">
          <el-form-item label="Excel">
            <el-upload
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              action=""
              :auto-upload="false"
              class="upload-demo"
              drag
              :file-list="excelFilelist"
              :limit="1"
              :on-change="handleExcelChange"
            >
              <el-icon class="el-icon--upload">
                <upload-filled />
              </el-icon>
              <div class="el-upload__text">
                <em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">xls/xlsx 文件</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitImportForm">
              确 定
            </el-button>
            <el-button @click="closeImportDialog">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
  import {
    creatProject,
    updateProject,
    getProjectById,
    getBaseProjectList,
    getImportExcel,
    getExportTemplate,
  } from '@/api/project/projectInfo'
  import { mapState } from 'vuex'
  export default {
    name: '',
    data() {
      return {
        form: {
          riskResponsibilityDtoList: [], // 风险清单列表
        },
        content: '',
        rules: {
          linkProjectId: [
            { required: true, trigger: 'blur', message: '请输入' },
          ],
          safetyResponsiblePersonArr: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          projectAdminArr: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          startDate: [{ required: true, trigger: 'change', message: '请选择' }],
          status: [{ required: true, trigger: 'change', message: '请选择' }],
        },
        title: '',
        dialogFormVisible: false,
        baseProjectList: [],
        importFormData: {},
        // 导入
        importDialog: {
          title: '项目重点生产安全风险防控责任清单导入',
          visible: false,
        },
        excelFile: undefined,
        excelFilelist: [],
        messageBox: undefined,
      }
    },
    computed: {
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          // this.form = row
          this.getDetails(row.id)
        }
        this.getBaseProjectList()
        this.dialogFormVisible = true
      },
      async getDetails(id) {
        try {
          const res = await getProjectById(id)
          if (!res.data) return

          this.form = res.data

          // 处理主要人员数组
          const mainPersonnelFields = [
            'projectAdmin',
            'safetyResponsiblePerson',
          ]
          mainPersonnelFields.forEach((field) => {
            if (this.form[field]?.length) {
              this.$set(
                this.form,
                `${field}Arr`,
                this.form[field].map((item) => item.id)
              )
            } else {
              this.$set(this.form, `${field}Arr`, [])
            }
          })

          // 处理风险责任清单
          if (this.form.riskResponsibilityDtoList?.length) {
            this.form.riskResponsibilityDtoList.forEach((item) => {
              const personnelFields = [
                'responsibilityLeader',
                'assistLeader',
                'lineManager',
              ]
              personnelFields.forEach((field) => {
                if (item[field]?.length) {
                  this.$set(
                    item,
                    `${field}Arr`,
                    item[field].map((person) => person.id)
                  )
                } else {
                  this.$set(item, `${field}Arr`, [])
                }
              })
            })
          } else {
            this.form.riskResponsibilityDtoList = []
          }
        } catch (error) {
          console.error('获取详情失败：', error)
          this.$message.error('获取详情失败')
        }
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            const form = {
              ...this.form,
              safetyResponsiblePersonArr: undefined,
              projectAdminArr: undefined,
            }
            if (
              form.riskResponsibilityDtoList &&
              form.riskResponsibilityDtoList.length > 0
            ) {
              form.riskResponsibilityDtoList.forEach((item) => {
                item.responsibilityLeaderArr = undefined
                item.assistLeaderArr = undefined
                item.lineManagerArr = undefined
              })
            }
            if (!this.form.id) {
              creatProject(form).then(() => {
                this.$message.success('添加成功')
                this.$emit('refreshDataList')
                this.close()
              })
            } else {
              updateProject(form).then(() => {
                this.$message.success('修改成功')
                this.$emit('refreshDataList')
                this.close()
              })
            }
          } else {
            return false
          }
        })
      },
      addList() {
        this.form.riskResponsibilityDtoList.push({
          riskName: '',
          schemeMeasures: '',
          keyLink: '',
          responsibilityLeader: '',
          assistLeader: '',
          lineManager: '',
        })
      },
      handleDelete(index) {
        this.form.riskResponsibilityDtoList.splice(index, 1)
      },
      delList() {
        const selection = this.$refs.table.selection
        selection.forEach((item) => {
          const index = this.form.riskResponsibilityDtoList.indexOf(item)
          if (index > -1) {
            this.form.riskResponsibilityDtoList.splice(index, 1)
          }
        })
      },
      changeProject(e) {
        const obj = this.baseProjectList.find((item) => item.projectCode === e)
        if (obj) {
          this.form.projectName = obj.projectName
        }
      },
      handlePersonChange(ids, type) {
        this.form[type] = []
        if (ids?.length) {
          this.form[type] = ids.map((id) => {
            const user = this.storeUserList.find((item) => item.userId === id)
            return user
              ? {
                  id: user.userId,
                  name: user.nickname,
                }
              : null
          })
        }
      },
      changeResponItem(row, index, type) {
        // 确保数组存在
        if (!this.form.riskResponsibilityDtoList[index]) {
          this.$set(this.form.riskResponsibilityDtoList, index, {})
        }

        // 转换选中的用户数据
        const selectedUsers = row.map((id) => {
          const user = this.storeUserList.find((item) => item.userId === id)
          return user
            ? {
                id: user.userId,
                name: user.nickname,
              }
            : null
        })

        // 使用 Vue.$set 确保响应式更新
        this.$set(
          this.form.riskResponsibilityDtoList[index],
          type,
          selectedUsers
        )
      },
      getBaseProjectList() {
        getBaseProjectList({
          pageNum: 1,
          pageSize: 1000,
        }).then((res) => {
          this.baseProjectList = res.data.list || []
        })
      },
      /**
       * 导入下拉框
       */
      handleDropdown(command) {
        if (command == 'handleDownloadTemplate') {
          this.handleDownloadTemplate()
        }
        if (command == 'showImportDialog') {
          this.showImportDialog()
        }
      },
      /**
       * 下载导入模板
       */
      handleDownloadTemplate() {
        getExportTemplate().then((res) => {
          const blob = new Blob([res.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = '项目重点生产安全风险防控责任清单导入模板.xlsx'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        })
      },

      /**
       * 导入表单弹窗
       */
      async showImportDialog() {
        this.importDialog.visible = true
      },
      /**
       * Excel文件change事件
       *
       * @param file
       */
      handleExcelChange(file) {
        if (!/\.(xlsx|xls|XLSX|XLS)$/.test(file.name)) {
          this.$baseMessage('上传Excel只能为xlsx、xls格式', 'warning')
          this.excelFile = undefined
          this.excelFilelist = []
          return false
        }
        this.excelFile = file.raw
      },

      /**
       * Excel文件上传
       */
      submitImportForm() {
        this.$refs.importFormRef.validate((valid) => {
          if (valid) {
            if (!this.excelFile) {
              this.$baseMessage('上传Excel文件不能为空', 'warning')
              return false
            }
            this.messageBox = this.$loading({
              lock: true,
              text: '上传中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            getImportExcel(this.excelFile)
              .then((response) => {
                this.messageBox.close()
                if (response.data == null) response.data = []
                this.form.riskResponsibilityDtoList =
                  this.form.riskResponsibilityDtoList || []
                if (response.data.length) {
                  response.data.forEach((item) => {
                    this.form.riskResponsibilityDtoList.push(item)
                  })
                }
                this.closeImportDialog()
                this.$message.success('导入成功')
              })
              .catch((err) => {
                this.messageBox.close()
                console.log(err, 'err')
              })
          }
        })
      },
      closeInnerVisible() {
        this.errTitle = ''
        this.errData = {}
        this.innerVisible = false
        this.closeImportDialog()
        this.handleQuery({ clientId: this.clientId })
      },
      /**
       * 关闭导入弹窗
       */
      closeImportDialog() {
        this.importDialog.visible = false
        this.excelFile = undefined
        this.excelFilelist = []
        this.$refs.importFormRef.resetFields()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
