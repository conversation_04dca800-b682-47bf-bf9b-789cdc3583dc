<template>
  <div>
    <el-form
      ref="form"
      disabled
      label-position="left"
      label-width="140px"
      :model="form"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="人员：" prop="userName">
            <el-input
              v-model="form.userName"
              placeholder="请输入"
              style="width: 96%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目名称：" prop="projectId">
            <el-select
              v-model="form.projectId"
              placeholder="请选择"
              style="width: 96%"
            >
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.projectName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="岗位：" prop="positionName">
            <el-input
              v-model="form.positionName"
              placeholder="请输入"
              style="width: 96%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="级别：" prop="level">
            <el-input
              v-model="form.level"
              placeholder="请输入"
              style="width: 96%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="履职检查项：" prop="templatesInfoId">
            <el-select
              v-model="form.templatesInfoId"
              placeholder="请选择"
              style="width: 96%"
            >
              <el-option
                v-for="item in dutyList"
                :key="item.id"
                :label="item.dutyInspectionItem"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="日期：" prop="dutyDate">
            <el-date-picker
              v-model="form.dutyDate"
              placeholder="选择日期时间"
              style="width: 96%"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="检查类型：" prop="inspectionType">
            <el-select
              v-model="form.inspectionType"
              placeholder="请选择检查类型"
              style="width: 96%"
            >
              <el-option
                v-for="item in safeDictList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审批人：" prop="approverId">
            <el-select
              v-model="form.approverId"
              placeholder="请选择"
              style="width: 96%"
            >
              <el-option
                v-for="item in userList"
                :key="item.userId"
                :label="item.nickname"
                :value="item.userId"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item class="logo" label="图片：" prop="images">
            <div
              v-if="
                form.imagesAttach &&
                form.imagesAttach.attachInfoList &&
                form.imagesAttach.attachInfoList.length
              "
            >
              <div
                v-for="(file, index) in form.imagesAttach.attachInfoList"
                :key="index"
                class="file-item"
              >
                <el-image
                  v-if="isImage(file.name)"
                  class="preview-image"
                  :preview-src-list="[file.url]"
                  :src="file.url"
                />
                <!-- <span>{{ file.name }}</span> -->
              </div>
            </div>
            <div v-else>无图片</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="logo" label="附件：" prop="attachments">
            <div
              v-if="
                form.attachmentsAttach &&
                form.attachmentsAttach.attachInfoList &&
                form.attachmentsAttach.attachInfoList.length
              "
            >
              <div
                v-for="(file, index) in form.attachmentsAttach.attachInfoList"
                :key="index"
                class="file-item"
              >
                <el-image
                  v-if="isImage(file.name)"
                  class="preview-image"
                  :preview-src-list="[file.url]"
                  :src="file.url"
                />
                <div v-else>
                  <span>{{ file.name }}</span>
                  <a :href="file.url" target="_blank">查看</a>
                </div>
              </div>
            </div>
            <div v-else>无附件</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="完成情况描述：" prop="completionDescription">
        <el-input
          v-model="form.completionDescription"
          :rows="4"
          type="textarea"
        />
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否合格：" prop="qualified">
            <el-radio-group v-model="form.qualified" disabled>
              <el-radio :label="true">合格</el-radio>
              <el-radio :label="false">不合格</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="!form.qualified" :span="12">
          <el-form-item label="要求整改时间：" prop="requiredCompletionTime">
            {{ form.requiredCompletionTime }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="!form.qualified">
        <el-col :span="12">
          <el-form-item label="检查问题描述：" prop="issueDescription">
            {{ form.issueDescription }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="整改人：" prop="rectifierId">
            {{ form.rectifierName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="!form.qualified">
        <el-col :span="12">
          <el-form-item label="隐患违章问题：" prop="hiddenDangersViolations">
            {{ form.hiddenDangersViolations }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="因素级别：" prop="facotrLevelDesc">
            {{ form.facotrLevelDesc }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="!form.qualified">
        <el-col :span="24">
          <el-form-item label="具体描述：" prop="hiddenDangersViolations">
            {{
              form.specificDesc
            }}，违反了《东方地球物理公司HSE违章行为管理规定》第&nbsp;{{
              form.hiddenDangersViolationsNum || ' '
            }}&nbsp;条
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="!form.qualified">
        <el-col :span="12">
          <el-form-item
            class="logo"
            label="现场照片或视频："
            prop="onSitePhotosOrVideos"
          >
            <div
              v-if="
                form.onSitePhotosOrVideosAttach &&
                form.onSitePhotosOrVideosAttach.attachInfoList &&
                form.onSitePhotosOrVideosAttach.attachInfoList.length
              "
            >
              <div
                v-for="(file, index) in form.onSitePhotosOrVideosAttach
                  .attachInfoList"
                :key="index"
                class="file-item"
              >
                <el-image
                  v-if="isImage(file.name)"
                  class="preview-image"
                  :preview-src-list="[file.url]"
                  :src="file.url"
                />
                <div v-else>
                  <span>{{ file.name }}</span>
                  <a :href="file.url" target="_blank">查看</a>
                </div>
              </div>
            </div>
            <div v-else>无附件</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" />
      </el-row>
    </el-form>
  </div>
</template>
<script>
  import { mapGetters } from 'vuex'
  export default {
    name: 'SelfView',
    props: {
      formData: {
        type: Object,
        default: () => {},
      },
      dutyList: {
        type: Array,
        default: () => [],
      },
      projectList: {
        type: Array,
        default: () => [],
      },
      userList: {
        type: Array,
        default: () => [],
      },
      safeDictList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {},
        dutyArray: [],
        safeDictArray: [],
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
    },
    watch: {
      formData(val) {
        this.form = {
          ...val,
          ...val.dutyListInfo,
          ...val.issueFeedbackAndRectificationDto,
          inspectionType: val.inspectionType ? val.inspectionType + '' : null,
          templatesInfoId: val.dutyInspectionItemCont?.id,
        }
      },
      dutyList(val) {
        this.dutyArray = val || []
      },
      safeDictList(val) {
        this.safeDictArray = val || []
      },
    },
    methods: {
      isImage(filename) {
        const ext = filename.split('.').pop().toLowerCase()
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .file-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .preview-image {
      width: 80px;
      height: 80px;
      margin-right: 10px;
      object-fit: cover;
    }

    a {
      margin-left: 10px;
      color: #409eff;
    }
  }
</style>
