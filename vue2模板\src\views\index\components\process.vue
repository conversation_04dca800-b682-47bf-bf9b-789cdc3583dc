<template>
  <el-dialog
    class="NoticeInfo"
    :close-on-click-modal="false"
    :show-close="false"
    :visible.sync="dialogFormVisible"
    :width="
      dataType === 'DUTYLISTAPPROVAL' ||
      dataType === 'DUTYSELFPROOFAPPROVAL' ||
      dataType === 'PROBLEMFEEDBACKAPPROVAL' ||
      dataType === 'USERCONFIRMATIONDUTY' ||
      dataType === 'SCOREAPPEALS'
        ? '80%'
        : '600px'
    "
    @close="close"
  >
    <el-form
      ref="form"
      label-width="80px"
      :model="form"
      style="min-height: 200px; margin: 0 20px"
    >
      <h3 class="alignCenter">{{ title }}</h3>
      <p class="alignCenter" style="font-size: 12px">
        <span class="p10">发布时间：{{ time }}</span>
      </p>
      <Processes
        v-if="dataType === 'PROCESSES'"
        :dict-list="processesDict"
        :form-data="formData"
        :initiator-person="initiatorPerson"
      />
      <Transfer
        v-else-if="dataType === 'TRANSFER'"
        :form-data="formData"
        :reviews-status="reviewsStatus"
      />
      <DutyListApproval
        v-else-if="dataType === 'DUTYLISTAPPROVAL'"
        :duty-list="dutyList"
        :form-data="formData"
        :frequency-list="frequencyList"
        :process-list="processList"
      />
      <DutySelfProofApproval
        v-else-if="dataType === 'DUTYSELFPROOFAPPROVAL'"
        :duty-list="dutyList"
        :form-data="formData"
        :project-list="projectList"
        :safe-dict-list="safeDictList"
        :user-list="storeUserList"
      />
      <ProblemFeedbackApproval
        v-else-if="dataType === 'PROBLEMFEEDBACKAPPROVAL'"
        :form-data="formData"
      />
      <UserConfirmationDuty
        v-else-if="dataType === 'USERCONFIRMATIONDUTY'"
        :form-data="formData"
        :frequency-list="frequencyList"
        :process-list="processList"
      />
      <ScoreAppeals
        v-else-if="dataType === 'SCOREAPPEALS'"
        :form-data="formData"
      />
      <el-form-item label="是否通过：">
        <el-radio-group v-model="form.approvalStatus" :disabled="isDisabled">
          <el-radio label="PASS">通过</el-radio>
          <el-radio label="REJECT">驳回</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.approvalStatus === 'REJECT'" label="驳回原因：">
        <el-input
          v-model="form.reason"
          :disabled="isDisabled"
          rows="5"
          type="textarea"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer alignCenter">
      <el-button @click="dialogFormVisible = false">关 闭</el-button>
      <el-button
        v-if="!isDisabled"
        :loading="loading"
        type="primary"
        @click="audit"
      >
        审 核
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { mapState } from 'vuex'
  import { baseURL } from '@/config'
  import { getToDoDetail, getToDoAudit } from '@/api/todo'
  import { getProjectStatusLogsById } from '@/api/project/projectApproval'
  import { getProjectRelationsById } from '@/api/project/projectPerson'
  import { queryUserDutyInfo } from '@/api/perform/responsibilityList'
  import { getUserDutyDetailsById } from '@/api/perform/userResponsibility'
  import { getRectificationById } from '@/api/question/issuesList'
  import { getAppealDetail, approvalAppeal } from '@/api/integral/integralList'
  import Processes from '../template/Processes.vue'
  import Transfer from '../template/Transfer.vue'
  import DutySelfProofApproval from '../template/DutySelfProofApproval.vue'
  import DutyListApproval from '../template/DutyListApproval.vue'
  import ProblemFeedbackApproval from '../template/ProblemFeedbackApproval.vue'
  import UserConfirmationDuty from '../template/UserConfirmationDuty.vue'
  import ScoreAppeals from '../template/ScoreAppeals.vue'
  import { getDictItems } from '@/api/user'
  import { getProjectList } from '@/api/project/projectInfo'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  import { getUserDutyInfo } from '@/api/perform/responsibilityList'
  export default {
    name: 'TodoEdit',
    components: {
      Processes,
      Transfer,
      DutyListApproval,
      DutySelfProofApproval,
      ProblemFeedbackApproval,
      UserConfirmationDuty,
      ScoreAppeals,
    },
    props: {
      isDisabled: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        form: {
          id: '',
          approvalStatus: 'PASS',
          reason: '',
          name: '',
        },
        fileUrl: baseURL,
        title: '',
        time: '',
        dialogFormVisible: false,
        loading: false,
        dataType: '',
        formData: {},
        processesDict: [],
        dutyList: [],
        processList: [],
        frequencyList: [],
        safeDictList: [],
        projectList: [],
        initiatorPerson: null,
        reviewsStatus: null,
      }
    },
    computed: {
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
    },
    methods: {
      showEdit(row) {
        this.resetForm()

        this.loading = true
        if (row.dataType == 'SCOREAPPEALS') {
          this.title = row.title
          this.time = row.createdAt
          this.dataType = row.dataType
          this.form.id = row.masterDataId
          this.form.approvalStatus = 'PASS'
          this.goAppealById(row.masterDataId)
          this.loading = false
          this.dialogFormVisible = true
        } else {
          getToDoDetail(row.id)
            .then(({ data }) => {
              if (data) {
                this.title = data.title
                this.time = data.createdAt
                this.form.id = row.id
                this.form.approvalStatus = 'PASS'
                this.dataType = data.dataType

                switch (data.dataType) {
                  case 'PROCESSES':
                    // 项目阶段
                    this.initiatorPerson = row.initiatorPerson
                    this.goProcessesDictItems('processes')
                    this.goProjectDetails(data.masterDataId)
                    break
                  case 'TRANSFER':
                    // 人员调动
                    this.reviewsStatus = row.reviewsStatus
                    this.goProjectRelations(data.masterDataId)
                    break
                  case 'DUTYLISTAPPROVAL':
                    // 责任清单审批
                    this.getDutyList()
                    this.goDictItems('project_state', 'process')
                    this.goDictItems('project_frequency', 'frequency')
                    this.goUserDutyInfoById(data.masterDataId)
                    break
                  case 'DUTYSELFPROOFAPPROVAL':
                    // 自证履职审批
                    this.getDutyList()
                    this.goProjectList()
                    this.goDictItems('safe_check_type', 'safeType')
                    this.goUserDutyDetailsById(data.masterDataId)
                    break
                  case 'PROBLEMFEEDBACKAPPROVAL':
                    // 问题反馈审批
                    this.goRectificationById(data.masterDataId)
                    break
                  case 'USERCONFIRMATIONDUTY':
                    // 用户确认责任
                    this.goDictItems('project_state', 'process')
                    this.goDictItems('project_frequency', 'frequency')
                    this.goAllocateById(data.masterDataId)
                    break
                  default:
                    break
                }
              }
              this.dialogFormVisible = true
            })
            .catch((error) => {
              console.error('获取待办详情失败:', error)
              this.$message.error('获取待办详情失败')
            })
            .finally(() => {
              this.loading = false
            })
        }
        if (row.approvalStatus) {
          this.form.approvalStatus = row.approvalStatus
        }
        if (row.reason) {
          this.form.reason = row.reason
        }
      },

      // 获取项目阶段详情
      goProjectDetails(id) {
        getProjectStatusLogsById(id)
          .then(({ data }) => {
            this.formData = data
          })
          .catch((error) => {
            console.error('获取项目详情失败:', error)
          })
      },

      // 获取人员调整详情
      async goProjectRelations(id) {
        getProjectRelationsById(id)
          .then(({ data }) => {
            this.formData = data
          })
          .catch((error) => {
            console.error('获取人员调整失败:', error)
          })
      },

      audit() {
        if (this.form.approvalStatus === 'REJECT' && !this.form.reason) {
          this.$message.warning('请填写驳回原因')
          return
        }

        this.loading = true

        if (this.dataType === 'SCOREAPPEALS') {
          const { id, approvalStatus, reason } = this.form
          const data = {
            id,
            result: approvalStatus === 'PASS' ? 1 : 2,
            opinion: reason,
            tempStorage: false,
          }
          approvalAppeal(data)
            .then(() => {
              this.dialogFormVisible = false
              this.$message({
                message: '审批成功',
                type: 'success',
              })
              // 通知父组件刷新数据
              this.$emit('refresh')
            })
            .catch((error) => {
              console.error('审批失败:', error)
              this.$message.error('审批失败，请重试')
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          const { id, approvalStatus, reason } = this.form
          const params = {
            id,
            approvalStatus,
            reason,
          }
          getToDoAudit(params)
            .then(() => {
              this.dialogFormVisible = false
              this.$message({
                message: '审批成功',
                type: 'success',
              })
              // 通知父组件刷新数据
              this.$emit('refresh')
            })
            .catch((error) => {
              console.error('审批失败:', error)
              this.$message.error('审批失败，请重试')
            })
            .finally(() => {
              this.loading = false
            })
        }
      },

      resetForm() {
        this.form = {
          id: '',
          approvalStatus: 'PASS',
          reason: '',
          name: '',
        }
        this.title = ''
        this.time = ''
        this.dataType = ''
      },
      close() {
        this.dialogFormVisible = false
        this.resetForm()
        this.$emit('show')
      },
      // 获取项目阶段列表
      async goProcessesDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'project_state',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.processesDict = res.data.data.list || []
      },
      // 根据任务清单分配ID查询
      async goUserDutyInfoById(id) {
        const res = await queryUserDutyInfo(id)
        this.formData = res.data || {}
      },
      async getDutyList() {
        const res = await getDutyInspectionItemsList({
          pageNum: 1,
          pageSize: 9999,
        })
        const { code, data } = res

        if (code === 200) {
          this.dutyList = data
        }
      },
      // 获取自证履职详情
      async goUserDutyDetailsById(id) {
        const res = await getUserDutyDetailsById(id)
        this.formData = res.data || {}
      },
      // 获取类型字典
      async goDictItems(type, str) {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: type,
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        if (str === 'process') {
          this.processList = res.data.data.list || []
        } else if (str === 'frequency') {
          this.frequencyList = res.data.data.list || []
        } else if (str === 'safeType') {
          this.safeDictList = res.data.data.list || []
        }
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      // 积分申诉详情
      async goAppealById(id) {
        const res = await getAppealDetail({ id })
        this.formData = res.data || {}
      },
      // 查询整改详情
      async goRectificationById(id) {
        const res = await getRectificationById(id)
        this.formData = res.data || {}
      },
      // 根据任务清单分配ID查询 单人分配详情
      async goAllocateById(id) {
        const res = await getUserDutyInfo(id)
        this.formData = res.data || {}
      },
    },
  }
</script>
<style scoped>
  .alignCenter {
    text-align: center;
  }
  .p10 {
    padding: 2px 10px;
  }
  .NoticeInfo /deep/.el-dialog__body {
    padding-top: 0;
  }
  .content p {
    text-indent: 2em;
  }
</style>
