{"author": "wdysoft", "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@logicflow/core": "^1.0.2", "@logicflow/extension": "^1.0.2", "axios": "^1.4.0", "clipboard": "^2.0.11", "core-js": "^3.30.2", "cross-env": "^7.0.3", "dayjs": "^1.11.8", "echarts": "^5.4.2", "element-ui": "^2.15.13", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "jsplumb": "^2.15.6", "jszip": "^3.10.1", "lodash": "^4.17.21", "mockjs": "^1.1.0", "moment": "^2.29.4", "nprogress": "^0.2.0", "qrcodejs2": "0.0.2", "qs": "^6.11.2", "register-service-worker": "^1.7.2", "resize-detector": "^0.3.0", "screenfull": "5.2.0", "vab-icons": "file:vab-icons", "vue": "^2.6.11", "vue-i18n": "^8.26.8", "vue-json-viewer": "^2.2.22", "vue-router": "^3.5.3", "vue-ueditor-wrap": "^2.5.6", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "xlsx": "^0.17.4"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.15", "@vue/cli-plugin-eslint": "^4.5.15", "@vue/cli-plugin-pwa": "^4.5.15", "@vue/cli-plugin-router": "^4.5.15", "@vue/cli-plugin-vuex": "^4.5.15", "@vue/cli-service": "^4.5.15", "@vue/eslint-config-prettier": "6.0.0", "body-parser": "^1.20.2", "call-rely": "^1.3.1", "chalk": "^4.1.2", "chokidar": "^3.5.3", "compression-webpack-plugin": "6.1.1", "dotenv": "^16.3.1", "eslint": "6.8.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-vue": "^8.2.0", "filemanager-webpack-plugin": "3.1.1", "image-webpack-loader": "^8.1.0", "lint-staged": "^13.2.2", "postcss": "^8.4.24", "postcss-html": "^1.5.0", "postcss-jsx": "^0.36.4", "postcss-scss": "^4.0.6", "postcss-syntax": "^0.36.2", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "sass": "1.32.13", "sass-loader": "10.2.0", "stylelint": "^15.7.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^4.2.0", "svg-sprite-loader": "^6.0.11", "vue-eslint-parser": "^8.0.1", "webpack": "4.46.0", "webpackbar": "^5.0.2"}, "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://chu1204505056.gitee.io/admin-pro", "license": "Mozilla Public License Version 2.0", "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "name": "admin-pro", "participants": ["fwfmiao"], "private": true, "repository": {"type": "git", "url": "git+https://github.com/vue-admin-beautiful/admin-pro.git"}, "scripts": {"build-dev": "export NODE_OPTIONS=--openssl-legacy-provider && cross-env VUE_APP_MODE=dev vue-cli-service build", "build-prod": "export NODE_OPTIONS=--openssl-legacy-provider && cross-env VUE_APP_MODE=prod vue-cli-service build", "build-gmbicloud-test": "export NODE_OPTIONS=--openssl-legacy-provider && cross-env VUE_APP_MODE=gmbicloud_test vue-cli-service build", "build-gmbicloud-local": "cross-env VUE_APP_MODE=gmbicloud_test vue-cli-service build", "global:install": "npm install -g nrm,cnpm,npm-check-updates", "globle:update": "ncu -g", "lint": "vue-cli-service lint", "lint:eslint": "eslint {src,mock}/**/*.{vue,js} --fix", "lint:prettier": "prettier {src,mock}/**/*.{html,vue,css,sass,scss,js,md} --write", "lint:stylelint": "stylelint {src,mock}/**/*.{html,vue,css,sass,scss} --fix --cache --cache-location node_modules/.cache/stylelint/", "local": "set NODE_OPTIONS=--openssl-legacy-provider && cross-env VUE_APP_MODE=local  vue-cli-service serve", "module:install": "cnpm i", "module:reinstall": "rimraf node_modules&&npm run module:install", "module:update": "ncu -u --reject  chalk,@logicflow/core,@logicflow/extension,screenfull,@vue/eslint-config-prettier,compression-webpack-plugin,eslint,eslint-plugin-prettier,filemanager-webpack-plugin,sass,sass-loader,webpack,vue,vuex,vue-router,@vue/cli-plugin-babel,@vue/cli-plugin-eslint, @vue/cli-plugin-pwa,@vue/cli-plugin-router,@vue/cli-plugin-vuex,@vue/cli-service,plop,vue-eslint-parser,eslint-plugin-vue,vue-i18n,vab-player,xlsx --registry=https://registry.npm.taobao.org&&npm run module:install", "nrm:npm": "nrm use npm", "nrm:taobao": "nrm use taobao", "serve": "cross-env VUE_APP_MODE=dev vue-cli-service serve", "serve:node-18": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "serve-dev": "set NODE_OPTIONS=--openssl-legacy-provider && cross-env VUE_APP_MODE=dev vue-cli-service serve", "serve-prod": "set NODE_OPTIONS=--openssl-legacy-provider && cross-env VUE_APP_MODE=prod  vue-cli-service serve"}, "version": "2.7.0"}