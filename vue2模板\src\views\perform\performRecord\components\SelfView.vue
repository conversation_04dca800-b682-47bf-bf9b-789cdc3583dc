<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '自证履职检查单详情'"
      :visible.sync="dialogFormVisible"
      width="1000px"
      @close="close"
    >
      <el-form ref="form" disabled label-width="140px" :model="form">
        <el-row>
          <el-col :span="12">
            <el-form-item label="人员：" prop="userId">
              {{ form.userName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称：" prop="projectId">
              <el-select
                v-model="form.projectId"
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.id"
                  :label="item.projectName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="岗位：" prop="positionName">
              {{ form.positionName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="级别：" prop="level">
              {{ form.level }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="履职检查项：" prop="templatesInfoId">
              <el-select
                v-model="form.templatesInfoId"
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in dutyList"
                  :key="item.id"
                  :label="item.dutyInspectionItem"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日期：" prop="dutyDate">
              {{ form.dutyDate }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查类型：" prop="inspectionType">
              <el-select
                v-model="form.inspectionType"
                placeholder="请选择检查类型"
                style="width: 96%"
              >
                <el-option
                  v-for="item in safeDictList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审批人：" prop="approverId">
              {{ form.approverName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item class="logo" label="图片：" prop="images">
              <div
                v-if="
                  form.imagesAttach &&
                  form.imagesAttach.attachInfoList &&
                  form.imagesAttach.attachInfoList.length
                "
              >
                <div
                  v-for="(item, index) in form.imagesAttach.attachInfoList"
                  :key="index"
                >
                  <el-image
                    v-if="['img', 'jpg', 'jpeg', 'png'].includes(item.platform)"
                    lazy
                    :preview-src-list="[item.url]"
                    :src="item.url"
                    style="width: 50%"
                  />
                  <el-tag v-else @click="handlePreview(item.url)">
                    {{ item.name }}
                  </el-tag>
                </div>
              </div>
              <div v-else>无图片</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="logo" label="附件：" prop="attachments">
              <div
                v-if="
                  form.attachmentsAttach &&
                  form.attachmentsAttach.attachInfoList &&
                  form.attachmentsAttach.attachInfoList.length
                "
              >
                <div
                  v-for="(item, index) in form.attachmentsAttach.attachInfoList"
                  :key="index"
                >
                  <el-image
                    v-if="['img', 'jpg', 'jpeg', 'png'].includes(item.platform)"
                    lazy
                    :preview-src-list="[item.url]"
                    :src="item.url"
                    style="width: 50%"
                  />
                  <el-tag v-else @click="handlePreview(item.url)">
                    {{ item.name }}
                  </el-tag>
                </div>
              </div>
              <div v-else>无附件</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="完成情况描述：" prop="completionDescription">
          {{ form.completionDescription }}
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否合格：" prop="qualified">
              {{ form.qualified ? '合格' : '不合格' }}
            </el-form-item>
          </el-col>
          <el-col v-if="!form.qualified" :span="12">
            <el-form-item label="要求整改时间：" prop="requiredCompletionTime">
              {{ form.requiredCompletionTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!form.qualified">
          <el-col :span="12">
            <el-form-item label="检查问题描述：" prop="issueDescription">
              {{ form.issueDescription }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改人：" prop="rectifierId">
              {{ form.rectifierName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!form.qualified">
          <el-col :span="12">
            <el-form-item label="隐患违章问题：" prop="hiddenDangersViolations">
              {{ form.hiddenDangersViolations }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="因素级别：" prop="facotrLevelDesc">
              {{ form.facotrLevelDesc }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!form.qualified">
          <el-col :span="24">
            <el-form-item label="具体描述：" prop="hiddenDangersViolations">
              {{
                form.specificDesc
              }}，违反了《东方地球物理公司HSE违章行为管理规定》第&nbsp;{{
                form.hiddenDangersViolationsNum || ' '
              }}&nbsp;条
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!form.qualified">
          <el-col :span="12">
            <el-form-item
              class="logo"
              label="现场照片或视频："
              prop="onSitePhotosOrVideos"
            >
              <div
                v-if="
                  form.onSitePhotosOrVideosAttach &&
                  form.onSitePhotosOrVideosAttach.attachInfoList &&
                  form.onSitePhotosOrVideosAttach.attachInfoList.length
                "
              >
                <div
                  v-for="(file, index) in form.onSitePhotosOrVideosAttach
                    .attachInfoList"
                  :key="index"
                  class="file-item"
                >
                  <el-image
                    v-if="isImage(file.name)"
                    class="preview-image"
                    :preview-src-list="[file.url]"
                    :src="file.url"
                  />
                  <div v-else>
                    <span>{{ file.name }}</span>
                    <a :href="file.url" target="_blank">查看</a>
                  </div>
                </div>
              </div>
              <div v-else>无附件</div>
            </el-form-item>
          </el-col>
          <el-col :span="12" />
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="close">关 闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { getDictItems } from '@/api/user'
  import { getProjectList } from '@/api/project/projectInfo'
  import { getUserDutyDetailsById } from '@/api/perform/userResponsibility'
  export default {
    name: 'SelfView',
    props: {
      dutyList: {
        type: Array,
        default: () => [],
      },
      processList: {
        type: Array,
        default: () => [],
      },
      frequencyList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {},
        title: '',
        dialogFormVisible: false,
        projectList: [],
        safeDictList: [],
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
    },
    methods: {
      showView(row) {
        console.log(row, 'row')
        this.title = ''
        if (row) {
          this.getDateils(row)
        }
        this.dialogFormVisible = true
        this.goProjectList()
        this.goDictItems()
      },
      getDateils(row) {
        getUserDutyDetailsById(row.id).then((res) => {
          this.form = res.data
          this.form = {
            ...this.form,
            ...this.form.dutyListInfo,
            ...this.form.issueFeedbackAndRectificationDto,
            inspectionType: this.form.inspectionType
              ? this.form.inspectionType + ''
              : null,
            templatesInfoId: this.form.dutyInspectionItemCont?.id,
          }
        })
      },
      close() {
        this.dialogFormVisible = false
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      // 获取类型字典
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'safe_check_type',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.safeDictList = res.data.data.list || []
      },
      isImage(filename) {
        const ext = filename.split('.').pop().toLowerCase()
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)
      },
      // 新的标签页
      handlePreview(url) {
        window.open(url, '_blank')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .file-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .preview-image {
      width: 80px;
      height: 80px;
      margin-right: 10px;
      object-fit: cover;
    }

    a {
      margin-left: 10px;
      color: #409eff;
    }
  }
</style>
