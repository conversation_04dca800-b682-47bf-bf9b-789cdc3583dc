<!-- 责任清单 -->
<template>
  <div class="app-container">
    <!-- 查询区域 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item label="项目名称" prop="projectId">
        <el-select
          v-model="queryParams.projectId"
          clearable
          filterable
          placeholder="请选择"
          @change="goUserByProject"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="userByProjectList && userByProjectList.length && isAdmin"
        label="人员"
        prop="userId"
      >
        <el-select
          v-model="queryParams.userId"
          clearable
          filterable
          placeholder="请选择"
          style="width: 200px"
          @change="changeUser"
        >
          <el-option
            v-for="item in userByProjectList"
            :key="item.employeeId"
            :label="item.employeeName"
            :value="item.employeeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="部门" prop="deptIds">
        <el-cascader
          v-model="deptIdArray"
          clearable
          filterable
          :options="deptList"
          placeholder="所属部门"
          :props="{ checkStrictly: true, multiple: true, emitPath: false }"
          :show-all-levels="false"
          style="width: 96%"
          @change="handleDeptChange"
        />
      </el-form-item>
      <el-form-item label="时间段" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          style="width: 300px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">
          查询
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      row-key="id"
      :span-method="objectSpanMethod"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" label="项目名称" prop="projectName" />
      <el-table-column
        align="center"
        label="履职状态"
        prop="dutyStatus"
        width="80px"
      >
        <template #default="scope">
          <el-tag v-if="scope.row.dutyStatus === 0">待确认</el-tag>
          <el-tag v-else-if="scope.row.dutyStatus === 1" type="primary">
            履职中
          </el-tag>
          <el-tag v-else-if="scope.row.dutyStatus === 2" type="success">
            已履职
          </el-tag>
          <el-tag v-else-if="scope.row.dutyStatus === -1" type="danger">
            已驳回
          </el-tag>
          <el-tag v-else-if="scope.row.dutyStatus === -2" type="info">
            暂存
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column
        align="center"
        label="用户确认状态"
        prop="userConfirmationStatus"
        width="120px"
      >
        <template #default="scope">
          <el-tag v-if="scope.row.userConfirmationStatus === 0" type="warning">
            待确认
          </el-tag>
          <el-tag v-else-if="scope.row.userConfirmationStatus === 1">
            履职中
          </el-tag>
          <el-tag
            v-else-if="scope.row.userConfirmationStatus === 2"
            type="success"
          >
            已履职
          </el-tag>
          <el-tag
            v-else-if="scope.row.userConfirmationStatus === -1"
            type="info"
          >
            已作废
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column
        align="center"
        label="安全生产责任清单明细"
        prop="checkItem"
      >
        <el-table-column align="center" label="人员" prop="userName" />
        <el-table-column align="center" label="是否完成" prop="onOver">
          <template #default="scope">
            <el-tag :type="scope.row.onOver ? 'success' : 'danger'">
              {{ scope.row.onOver ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="履职检查项"
          prop="templatesInfoId"
          width="260px"
        >
          <template #default="scope">
            {{ scope.row?.dutyInspectionItemCont?.dutyInspectionItem }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="类型" prop="type">
          <template #default="scope">
            <span v-if="scope.row.type">扫码履职</span>
            <span v-else>自证履职</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="工序" prop="process">
          <template #default="scope">
            <span v-for="item in processList" :key="item.id">
              <span v-if="scope.row.process === item.value">
                {{ item.name }}
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="履职时间/频率"
          prop="frequency"
          width="120"
        >
          <template #default="scope">
            <span v-for="item in frequencyList" :key="item.id">
              <span v-if="scope.row.frequency === item.value">
                {{ item.name }}
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="要求频次"
          prop="frequencyCount"
          width="80"
        />
        <!-- <el-table-column
          align="center"
          label="已完成次数"
          prop="completedTimes"
        /> -->
        <el-table-column
          align="center"
          label="周期内完成次数"
          prop="cycleCompletedTimes"
          width="140"
        />
        <!-- <el-table-column label="工序是否停用" prop="process" /> -->
        <!-- <el-table-column align="center" label="分值" prop="score" /> -->
      </el-table-column>
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            v-if="
              scope.row.dutyStatus === 1 &&
              startStage &&
              startStage.includes(scope.row.process)
            "
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >
            跳转履职
          </el-button>
          <!-- <el-button
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >
            修改
          </el-button>
          <el-button
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <SelfDetails
      ref="selfDetails"
      :duty-list="dutyList"
      :frequency-list="frequencyList"
      :process-list="processList"
      @refreshDataList="getList"
    />
    <ScanDetails
      ref="scanDetails"
      :duty-list="dutyList"
      :frequency-list="frequencyList"
      :process-list="processList"
      @refreshDataList="getList"
    />
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import {
    userQueryOnDutyList,
    delUserDuty,
  } from '@/api/perform/responsibilityList'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  import { getDictItems } from '@/api/user'
  import SelfDetails from './selfDetails.vue'
  import ScanDetails from './scanDetails.vue'
  import {
    getProjectList,
    getProjectById,
    getUserByProject,
  } from '@/api/project/projectInfo'
  import { listSelectDepartments } from '@/api/system/dept'
  export default {
    name: 'ResponsibilityList',
    components: {
      SelfDetails,
      ScanDetails,
    },
    data() {
      return {
        // 遮罩层
        loading: false,
        // 选中数组
        selectionList: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 责任清单表格数据
        dataList: [],
        tableData: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          deptName: undefined,
          projectName: undefined,
          projectId: undefined,
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          dataTitle: [
            { required: true, message: '数据标题不能为空', trigger: 'blur' },
          ],
          deptName: [
            { required: true, message: '人员单位不能为空', trigger: 'blur' },
          ],
          projectName: [
            { required: true, message: '项目信息不能为空', trigger: 'blur' },
          ],
        },
        dutyList: [],
        processList: [],
        frequencyList: [],
        projectList: [],
        userByProjectList: [],
        startStage: null,
        isAdmin: false,
        deptList: [], // 部门列表
        timeRange: [], // 时间范围
        deptIdArray: [], // 用于存储级联选择器的值
        layout: 'total, sizes, prev, pager, next, jumper', // 添加分页布局配置
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        userInfo: (state) => state.user.user,
        storeUserList: (state) => state.user.userList,
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
    },
    watch: {
      // storeProjectId: {
      //   handler(newVal) {
      //     this.queryParams.projectId = newVal
      //     this.getList()
      //   },
      // },
      'queryParams.projectId': {
        handler(newVal) {
          if (newVal) {
            this.goProjectDetails(newVal)
          }
        },
        immediate: true,
      },
    },
    async mounted() {
      const { roles } = this.userInfo
      const isAdmin = roles.includes('root', 'admin')
      this.isAdmin = isAdmin
      this.$set(this.queryParams, 'userId', isAdmin ? undefined : undefined)
      // this.queryParams.allocateUserId = userId
      // this.queryParams.projectId = this.storeProjectId
      await this.goProjectList()
      // this.getList()
      this.getDutyList()
      this.getDeptList()
      this.goDictItems('project_state', 'process')
      this.goDictItems('project_frequency', 'frequency')
    },
    methods: {
      /** 查询责任清单列表 */
      async getList() {
        this.loading = true
        this.dataList = []
        this.tableData = []
        this.total = 0
        // this.queryParams.projectId = this.storeProjectId
        // this.queryParams.allocateUserId = this.userId
        // this.queryParams.userId = this.userId
        try {
          // if (!this.queryParams.projectId && this.queryParams.userId) return

          const res = await userQueryOnDutyList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            this.dataList = data
            this.total = page.totalCount || 0
            // 处理表格数据
            this.handleTableData()
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      async goProjectDetails(id) {
        const res = await getProjectById(id)
        if (!res.data) return
        this.startStage = res.data.startStage || []
      },
      // 处理表格数据
      handleTableData() {
        this.tableData = []
        if (!this.dataList.length) return
        this.dataList.forEach((item) => {
          if (
            item.userDutyListsInfoDtoList &&
            item.userDutyListsInfoDtoList.length
          ) {
            item.userDutyListsInfoDtoList.forEach((detail, index) => {
              this.tableData.push({
                ...item,
                listId: detail.id,
                type: detail.type,
                onOver: detail.onOver,
                process: detail.process,
                templatesInfoId: detail.templatesInfoId,
                dutyInspectionItemCont: detail.dutyInspectionItemCont,
                frequency: detail.frequency,
                frequencyCount: detail.frequencyCount,
                completedTimes: detail.completedTimes,
                cycleCompletedTimes: detail.cycleCompletedTimes,
                dutyInspectionItemId: detail.dutyInspectionItemId,
                // process: detail.process,
                isFirst: index === 0,
                rowspan: item.userDutyListsInfoDtoList.length,
              })
            })
          } else {
            this.tableData.push({
              ...item,
              isFirst: true,
              rowspan: 1,
            })
          }
        })
      },
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        console.log(row, column, rowIndex, columnIndex)
        if (columnIndex === 0 || columnIndex === 1) {
          if (row.isFirst) {
            return {
              rowspan: row.rowspan,
              colspan: 1,
            }
          } else {
            return {
              rowspan: 0,
              colspan: 0,
            }
          }
        }
        return undefined
      },
      goProjectList() {
        try {
          getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.projectList = data
              this.$set(
                this.queryParams,
                'projectId',
                data[0].id || this.storeProjectId
              )
              this.goUserByProject()
              this.getList()
            } else {
              this.getList()
            }
          })
        } catch (error) {
          this.getList()
        }
      },
      getDeptList() {
        // 从API获取部门列表
        listSelectDepartments().then((response) => {
          this.deptList = response.data || []
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        // 处理时间范围
        if (this.timeRange && this.timeRange.length === 2) {
          this.queryParams.queryStartTime = this.timeRange[0] + ' 00:00:00'
          this.queryParams.queryEndTime = this.timeRange[1] + ' 23:59:59'
        } else {
          this.queryParams.queryStartTime = undefined
          this.queryParams.queryEndTime = undefined
        }
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.$refs.queryFormRef.resetFields()
        if (this.isAdmin) {
          this.queryParams.userId = undefined
        } else {
          this.queryParams.userId = undefined
        }
        this.timeRange = []
        this.userByProjectList = []
        this.deptIdArray = []
        this.queryParams.deptIdArray = undefined
        // this.queryParams.projectId = this.storeProjectId
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 履职 */
      handleUpdate(row) {
        if (row.type) {
          this.$refs.scanDetails.showEdit(row)
        } else {
          this.$refs.selfDetails.showEdit(row)
        }
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        let idstr = ''
        if (row) {
          idstr = row.id
        } else {
          const ids = this.selectionList.map((item) => item.id)
          if (ids.length === 0) {
            this.$message.warning('请选择要删除的记录')
            return
          }
          idstr = ids.join(',')
        }

        this.$confirm('确认删除所选中数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          delUserDuty({ ids: idstr }).then(() => {
            this.$message.success('删除成功')
            this.getList()
          })
        })
      },
      // 获取类型字典
      async goDictItems(type, str) {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: type,
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        if (str === 'process') {
          this.processList = res.data.data.list || []
        } else {
          this.frequencyList = res.data.data.list || []
        }
      },
      async getDutyList() {
        const res = await getDutyInspectionItemsList({
          pageNum: 1,
          pageSize: 9999,
        })
        const { code, data } = res

        if (code === 200) {
          this.$set(this, 'dutyList', data)
        }
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.getList()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.getList()
      },
      goUserByProject() {
        this.queryParams.userId = null
        if (!this.queryParams.projectId) {
          this.userByProjectList = []
          return
        }
        getUserByProject({
          projectId: this.queryParams.projectId,
          pageNum: 1,
          pageSize: 9999,
        }).then((res) => {
          const { code, data } = res
          if (code === 200 && data.relationship !== 2) {
            this.userByProjectList = data
          } else {
            this.userByProjectList = []
          }
        })
      },
      changeUser() {
        // console.log('所选项', e)
      },
      handleDeptChange(value) {
        if (value && value.length > 0) {
          this.queryParams.deptIdArray = value.join(',')
        } else {
          this.queryParams.deptIdArray = undefined
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .mb8 {
    margin-bottom: 8px;
  }
</style>
