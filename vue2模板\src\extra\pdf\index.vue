<template>
  <div>
    <iframe frameborder="0" :src="url" style="width: 100%; height: 880px" />
  </div>
</template>

<script>
  import { baseURL } from '@/config'
  import { getToken } from '@/utils/token'
  import axios from 'axios'
  export default {
    props: {
      pdfurl: {
        type: String,
        default: () => '',
      },
    },

    data() {
      return {
        url: '',
        numPages: null, // pdf 总页数
      }
    },
    watch: {
      pdfurl: {
        handler() {
          this.getpdfUrl()
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      // # 计算pdf页码总数
      async getpdfUrl() {
        const Loading = this.$baseLoading(6)
        axios
          .get(baseURL + this.pdfurl, {
            params: {},
            responseType: 'blob',
            headers: { Authorization: getToken() },
          })
          .then((res) => {
            const binaryData = []
            binaryData.push(res.data) //res就是pdf流文件
            //获取blob链接
            let src = window.URL.createObjectURL(
              new Blob(binaryData, { type: 'application/pdf' })
            )
            this.url = src
            Loading.close()
          })
      },
    },
  }
</script>

<style></style>
