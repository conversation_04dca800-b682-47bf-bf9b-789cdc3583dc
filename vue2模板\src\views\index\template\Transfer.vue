<template>
  <el-form ref="formRef" label-width="80px" :model="form">
    <el-form-item label="项目名称：" prop="projectId">
      {{ form.projectName }}
    </el-form-item>
    <el-row>
      <el-col :span="12">
        <el-form-item label="项目人员：" prop="employeeName">
          {{ form.employeeName }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="人员状态：" prop="reviewsStatus">
          <span v-for="(item, index) in personStatus" :key="index">
            <el-tag v-if="item.value == reviewsStatus" :type="item.type">
              {{ item.name }}
            </el-tag>
          </span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item prop="changeTime">
          <template slot="label">
            {{ reviewsStatus == '0' ? '返回时间：' : '离开时间：' }}
          </template>
          {{ form.changeTime ? form.changeTime.split(' ')[0] : '' }}
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  export default {
    props: {
      formData: {
        type: Object,
        default: () => {},
      },
      dictList: {
        type: Array,
        default: () => [],
      },
      reviewsStatus: {
        type: String,
        default: '0',
      },
    },
    data() {
      return {
        form: {},
        personStatus: [
          {
            value: '0',
            name: '在岗',
            type: 'success',
          },
          {
            value: '1',
            name: '暂离',
            type: 'warning',
          },
          {
            value: '2',
            name: '调离',
            type: 'danger',
          },
        ],
      }
    },
    watch: {
      formData(val) {
        this.form = val
      },
    },
  }
</script>
