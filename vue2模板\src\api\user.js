import { appToken, loginRSA, serverURL } from '@/config'
import { encryptedData } from '@/utils/encrypt'
import request from '@/utils/request'
import { getToken } from '@/utils/token'
import axios from 'axios'

export async function login(data) {
  return axios({
    url: `${serverURL}/swisp-auth-service/oauth/token`,
    method: 'post',
    params: data,
    headers: {
      /**
       * Basic clientId:clientSecret
       * 客户端信息Base64明文(客户端ID:客户端密钥)：hsePdPerformDuty:03bd51200b8511f0a9434fe4b398b706
       * 测试环境客户端信息Base64密文：aHNlLXBkLXBlcmZvcm0tZHV0eTowM2JkNTEyMDBiODUxMWYwYTk0MzRmZTRiMzk4YjcwNg==
       */
      Authorization: appToken,
    },
  })
}

export async function socialLogin(data) {
  if (loginRSA) {
    data = await encryptedData(data)
  }
  return request({
    url: '/socialLogin',
    method: 'post',
    data,
  })
}

export function getUserInfo() {
  let systemType = 'hse-pd-perform-duty'
  return axios({
    url: `${serverURL}/swisp-base-service/api/v1/users/me/client/${systemType}`,
    method: 'get',
    headers: {
      authorization: getToken(),
    },
  })
}

export function logout() {
  return request({
    url: '/logout',
    method: 'get',
  })
}

export function register(data) {
  return request({
    url: '/register',
    method: 'post',
    data,
  })
}
// export function getMe(params) {
//   return request({
//     url: '/system/uniroleUser/me',
//     method: 'get',
//     params,
//   })
// }
//修改密码
export function editPassWord(data) {
  return axios({
    url: `${serverURL}/swisp-base-service/api/v1/users/cpwd/${data.userId}`,
    method: 'post',
    data,
    headers: {
      Authorization: getToken(),
    },
  })
}

/**
 * 获取图片验证码
 */
export function getCaptcha(params) {
  return axios({
    url: `${serverURL}/swisp-auth-service/oauth/captcha?t=${new Date()
      .getTime()
      .toString()}`,
    method: 'get',
    params,
  })
}

/**
 * 获取短信验证码
 */
export function getSmsCode(data) {
  return axios({
    url: `${serverURL}/swisp-auth-service/sms-code?phoneNumber=${data.phoneNumber}`,
    method: 'post',
  })
}

/**
 * 获取客户端用户
 */

export function getClientUser(params) {
  let systemType = 'hse-pd-perform-duty'
  return axios({
    url: `${serverURL}/swisp-base-service/api/v1/users/client/${systemType}`,
    method: 'get',
    params,
    headers: {
      Authorization: getToken(),
    },
  })
}
/**
 * 获取机构tree
 */
export function getDeptTree(params) {
  return axios({
    // url: `${serverURL}/swisp-base-service/api/v1/depts/table`,
    url: `${serverURL}/swisp-base-service/api/v1/depts/select`,
    method: 'get',
    params,
    headers: {
      Authorization: getToken(),
    },
  })
}
/**
 * 根据机构类型查询获取机构tree
 */
export function getDeptTreeByGroup(params) {
  return axios({
    url: `${serverURL}/swisp-base-service/api/v1/depts/selectByGroup`,
    method: 'get',
    params,
    headers: {
      Authorization: getToken(),
    },
  })
}
/**
 * 根据机构类型查询获取机构tree
 */
export function getDeptByGroupId(params) {
  return axios({
    url: `${serverURL}/swisp-base-service/api/v1/depts/getTreeByDeptGroupId`,
    method: 'get',
    params,
    headers: {
      Authorization: getToken(),
    },
  })
}

/**
 * 根据角色岗位获取对应用户
 */
export function getUserByRoleId(params) {
  let systemType = 'hse-pd-perform-duty'
  return axios({
    url: `${serverURL}/swisp-base-service/api/v1/roles/getUserListByRoleId?roleIds=${
      params.roleIds
    }&deptId=${params.deptId || ''}&clientId=${systemType}`,
    method: 'get',
    headers: {
      Authorization: getToken(),
    },
  })
}

/**
 * 根据角色岗位获取对应菜单
 */
export function getRouterList(params) {
  return axios({
    url: `${serverURL}/swisp-base-service/api/v1/menus/route`,
    method: 'get',
    params,
    headers: {
      Authorization: getToken(),
    },
  })
}

/**
 * 获取字典信息
 */
export function getDictItems(params) {
  return axios({
    url: `${serverURL}/swisp-base-service/api/v1/dict-items/page`,
    method: 'get',
    params,
    headers: {
      Authorization: getToken(),
    },
  })
}
