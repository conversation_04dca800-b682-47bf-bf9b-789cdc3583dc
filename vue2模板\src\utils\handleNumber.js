import { isArray } from './validate'
import { hasRoute } from '@/utils/routes'

/**
 * @description 将阿拉伯数字转换为中文数字
 * @param num
 * @returns {void|*}
 */
export function toChinesNum(num) {
  let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  let unit = ['', '十', '百', '千', '万']
  num = parseInt(num)
  let getWan = (temp) => {
    let strArr = temp.toString().split('').reverse()
    let newNum = ''
    for (var i = 0; i < strArr.length; i++) {
      newNum =
        (i == 0 && strArr[i] == 0
          ? ''
          : i > 0 && strArr[i] == 0 && strArr[i - 1] == 0
          ? ''
          : changeNum[strArr[i]] + (strArr[i] == 0 ? unit[0] : unit[i])) +
        newNum
    }
    if (newNum == '一十') {
      newNum = '十'
    }
    return newNum
  }
  let overWan = Math.floor(num / 10000)
  let noWan = num % 10000
  if (noWan.toString().length < 4) {
    // console.log(noWan)
    noWan = '0' + noWan
  }
  return overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num)
}

/**
 * @description 重置Tree树形结构的序号
 * @param data=[]
 * @returns {void|*}
 */
export function resetTreeIndex(data) {
  if (!isArray(data)) {
    return false
  }
  data.map((item, index) => {
    item.index = index + 1
    if (item.children && item.children.length > 0) {
      resetTreeIndex(item.children)
    }
  })
  return data
}

/**
 * @description 级联选择器去除最后一级的children
 * @param data=[]
 * @returns {void|*}
 */
export function handleChildren(tree, childrenName) {
  if (!(tree && tree.length)) return []
  if (!childrenName) childrenName = 'children'
  return tree.map((item) => {
    if (item[childrenName]) {
      if (item[childrenName] && item[childrenName].length) {
        handleChildren(item[childrenName], childrenName)
      } else {
        item[childrenName] = undefined
      }
    }
    return item
  })
}

/**
 * @description 禁止input记录历史输入：目前针对k-form-build
 * @param Vnode
 * @returns {void|*}
 */
export function banRecordHis(Vnode) {
  if (Vnode) {
    let getInput = Vnode.getElementsByTagName('input')
    Array.from(getInput).map((item) => {
      item.setAttribute('autocomplete', 'off')
    })
  }
}

/**
 * @description 公用页面的返回跳转路径：index或者section
 * @param
 * @returns {void|*} path
 */
export function returnHomePath() {
  let path = ''
  if (hasRoute('/')) {
    path = '/'
  }
  if (hasRoute('/section')) {
    path = '/section'
  }
  return path
}

/**
 * @description 将多维数组转化成一位数组
 * @param
 * @returns {void|*} arr
 */
export function flatten(arr) {
  return [].concat.apply([], arr)
  // return [].concat(...arr.map((x) => (Array.isArray(x) ? flatten(x) : x)))
}
