<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="待办事项" name="todo">
        <span slot="label">
          <el-tag v-if="todoNum > 0" effect="dark" type="warning">
            {{ todoNum }}
          </el-tag>
          待办事项
        </span>
        <el-table v-loading="loading" :data="projectList">
          <el-table-column align="center" fixed label="事项名称" prop="title" />
          <!-- <el-table-column
            align="center"
            label="待办类型"
            prop="mtype"
            width="100"
          /> -->
          <el-table-column
            align="center"
            label="发起人"
            prop="sender"
            width="100"
          >
            <template #default="scope">
              {{ scope.row.initiatorPerson.name }}
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="发起时间"
            prop="createdAt"
            width="210"
          />
          <el-table-column
            align="center"
            fixed="right"
            label="操作"
            width="120"
          >
            <template #default="scope">
              <el-button type="text" @click="handleTodo(scope.row)">
                处理
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="total > 0"
          background
          :current-page="queryParams.pageNum"
          :layout="layout"
          :page-size="queryParams.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </el-tab-pane>
      <el-tab-pane label="已办事项" name="done">
        <el-card class="box-card">
          <el-form ref="queryFormRef" :inline="true" :model="queryParams">
            <el-row>
              <el-col :span="24" :xs="24">
                <el-form-item label="待办发起时间" prop="createdAt">
                  <el-date-picker
                    v-model="times"
                    end-placeholder="结束日期"
                    format="yyyy 年 MM 月 dd 日"
                    range-separator="至"
                    start-placeholder="开始日期"
                    type="daterange"
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item>
                <!-- <el-form-item label="待办处理时间" prop="clientId">
                  <el-date-picker
                    v-model="times1"
                    end-placeholder="结束日期"
                    format="yyyy 年 MM 月 dd 日"
                    range-separator="至"
                    start-placeholder="开始日期"
                    type="daterange"
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item> -->
                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    @click="handleQuery"
                  >
                    搜索
                  </el-button>
                  <el-button icon="el-icon-refresh" @click="resetQuery">
                    重置
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <el-table v-loading="loading" :data="doneList">
            <el-table-column
              align="center"
              label="事项名称"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                【{{ scope.row.title }}】 {{ scope.row.projectName }}
              </template>
            </el-table-column>
            <!-- <el-table-column
              align="center"
              label="事项类型"
              prop="mtype"
              width="100"
            /> -->
            <el-table-column
              align="center"
              label="发起人"
              prop="sender"
              width="100"
            >
              <template #default="scope">
                {{ scope.row.initiatorPerson.name }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="发起时间"
              prop="createdAt"
              width="210"
            />
            <el-table-column
              align="center"
              label="处理时间"
              prop="approverTime"
              width="210"
            />
            <el-table-column
              align="center"
              label="事项状态"
              prop="approvalStatus"
              width="80"
            >
              <template slot-scope="scope">
                <el-tag
                  v-if="scope.row.approvalStatus == 'PASS'"
                  type="success"
                >
                  通过
                </el-tag>
                <el-tag v-else type="danger">驳回</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              width="120"
            >
              <template #default="scope">
                <el-button type="text" @click="handleView(scope.row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-if="donetotal > 0"
            background
            :current-page="queryParams.pageNum"
            :layout="layout"
            :page-size="queryParams.pageSize"
            :total="donetotal"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 审核页面 -->
    <Process ref="processRef" :is-disabled="isDisable" @refresh="refreshData" />
  </div>
</template>

<!-- setup 无法设置组件名称，组件名称keepAlive必须 -->
<script>
  import { getAssignClientByUserId } from '@/api/clientUser.js'
  import { mapGetters, mapState } from 'vuex'
  // import { listUsersPage } from '@/api/system/user'
  // import { baseURL } from '@/config'
  // import { getAccessToken } from '@/utils/token'
  import { getToDoList } from '@/api/todo'
  import Process from '@/views/index/components/process.vue'

  export default {
    name: 'User',
    components: {
      Process,
    },
    data() {
      return {
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
        },
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        clientList: [],
        times: [],
        times1: [],
        userloading: false,
        userList: [],
        loading: false,
        projectList: [],
        activeName: 'todo',
        doneList: [],
        todoNum: 0,
        isDisable: false,
      }
    },
    computed: {
      ...mapGetters({
        curUserId: 'user/userId',
      }),
      ...mapState({
        userInfo: (state) => state.user.user,
      }),
    },
    mounted() {
      this.loadData()
    },

    methods: {
      handleClick() {
        this.queryParams.pageNum = 1
        if (this.activeName === 'todo') {
          this.isDisable = false
        } else {
          this.isDisable = true
        }
        this.fetchData()
      },
      /**
       * 项目查询
       **/
      handleQuery() {
        if (this.times == null) this.times = []
        if (this.times.length) {
          this.queryParams.sendTime = this.times.join(',')
        } else {
          this.$delete(this.queryParams, 'sendTime')
        }
        if (this.times1 == null) this.times1 = []
        if (this.times1.length) {
          this.queryParams.receiveTime = this.times1.join(',')
        } else {
          this.$delete(this.queryParams, 'receiveTime')
        }
        this.queryParams.pageNum = 1
        this.fetchData()
      },
      async fetchData() {
        try {
          this.loading = true
          const { roles, userId } = this.userInfo
          const isAdmin =
            roles.includes('ADMIN') || roles.includes('root', 'admin')

          let response
          if (this.activeName === 'todo') {
            const params = {
              ...this.queryParams,
              approver: isAdmin ? undefined : userId,
              approvalStatus: 'IN_APPROVAL',
            }
            response = await getToDoList(params)
            this.projectList = response.data
            this.total = response.page.totalCount
            this.todoNum = response.page.totalCount
          } else {
            const params = {
              ...this.queryParams,
              approvalDataPerson: userId,
            }
            response = await getToDoList(params)
            this.doneList = response.data
            this.donetotal = response.page.totalCount
          }
        } catch (error) {
          console.error('获取数据失败:', error)
        } finally {
          this.loading = false
        }
      },

      /**
       * 重置查询
       */
      resetQuery() {
        this.queryParams = {}
        this.times = []
        this.times1 = []
        this.handleQuery()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.fetchData()
      },

      /**
       * 初始化数据
       */
      loadData() {
        // 初始化客户端列表数据
        if (this.$route.query.type) this.activeName = this.$route.query.type
        this.getClientList()
      },
      getClientList() {
        getAssignClientByUserId({ userId: this.curUserId }).then(({ data }) => {
          this.clientList = []
          data.map((item) => {
            if (item.data.length) {
              item.data.filter((i) => i.hasAccess == 1)
              this.clientList = [...this.clientList, ...item.data]
            }
          })
          if (this.clientList.length == 1) {
            this.$set(this.queryParams, 'clientId', this.clientList[0].clientId)
            this.$nextTick(() => {
              this.handleQuery()
            })
          } else {
            this.handleQuery()
          }
        })
      },
      remoteMethod(query) {
        if (query !== '') {
          // this.userloading = true
          // setTimeout(() => {
          //   listUsersPage({ pageNo: 1, keywords: query }).then(({ data }) => {
          //     this.userList = data.list
          //     this.userloading = false
          //   })
          // })
        }
      },

      /**
       * 处理待办
       */
      handleTodo(row) {
        this.isDisable = false
        this.$refs.processRef.showEdit(row)
      },
      // 查看
      handleView(row) {
        this.isDisable = true
        this.$refs.processRef.showEdit(row)
      },
    },
  }
</script>
<style lang="scss" scoped></style>
