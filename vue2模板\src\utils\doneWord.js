import { baseURL } from '@/config'
import store from '@/store'
import axios from 'axios'
import { Loading } from 'element-ui'
import Vue from 'vue'
const options = {
  lock: true,
  text: '正在导出中...请稍候...',
  spinner: 'el-icon-loading',
  background: 'rgba(0, 0, 0, 0.7)',
}

export default {
  doneWord: function (urls, params, name) {
    let loadingInstance = Loading.service(options)
    const token = store.getters['user/token']
    axios
      .get(baseURL + urls, {
        responseType: 'arraybuffer',
        params: params,
        headers: { Authorization: token },
      })
      .then((res) => {
        loadingInstance.close()
        const data = res.data
        const url = window.URL.createObjectURL(
          new Blob([data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          })
        )
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.setAttribute('download', name + '.docx')
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        this.dialogInfo.dialogVisible = false
      })
      .catch(() => {
        loadingInstance.close()
      })
  },
  donePDF: function (urls, params, name, type = 'download') {
    let loadingInstance = Loading.service(options)
    const token = store.getters['user/token']
    axios
      .get(baseURL + urls, {
        params: params,
        responseType: 'arraybuffer',
        headers: { Authorization: token },
      })
      .then((res) => {
        loadingInstance.close()
        const data = res.data
        const url = window.URL.createObjectURL(
          new Blob([data], {
            type: 'application/pdf',
          })
        )
        if (type == 'preview') {
          window.open(url, 'DetailRunTime')
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          // a.setAttribute('download', name + '.pdf')
          // document.body.appendChild(a)
          // a.click()
          // document.body.removeChild(a)
        } else {
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          a.setAttribute('download', name + '.pdf')
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
        }
        this.dialogInfo.dialogVisible = false
      })
      .catch(() => {
        loadingInstance.close()
      })
  },
  doneExcel: function (urls, params, name, methods) {
    let loadingInstance = Loading.service(options)
    const token = store.getters['user/token']
    if (!methods) methods = 'get'
    let option = {
      method: methods,
      url: baseURL + urls,
      responseType: 'blob',
      headers: {
        Authorization: token,
      },
    }
    if (methods == 'get') {
      option.params = params
    } else {
      option.data = params
    }
    axios(option)
      .then((res) => {
        loadingInstance.close()
        const data = res.data
        const url = window.URL.createObjectURL(
          new Blob([data], {
            type: 'application/vnd.ms-excel',
          })
        )
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.setAttribute('download', name + '.xlsx')
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      })
      .catch((err) => {
        loadingInstance.close()
        console.log(err.message)
      })
  },
  doneZip: function (urls, params, name) {
    let loadingInstance = Loading.service(options)
    const token = store.getters['user/token']
    axios
      .get(baseURL + urls, {
        responseType: 'blob',
        params: params,
        headers: { Authorization: token },
      })
      .then((res) => {
        loadingInstance.close()
        if (res.data.type != 'application/octet-stream') {
          Vue.prototype.$baseAlert('导出失败或未查询到数据!', null, '关 闭')
          return false
        }
        const data = res.data
        const url = window.URL.createObjectURL(
          new Blob([data], {
            type: 'application/zip',
          })
        )
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.setAttribute('download', name)
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        this.dialogInfo.dialogVisible = false
      })
      .catch(() => {
        loadingInstance.close()
      })
  },
}
