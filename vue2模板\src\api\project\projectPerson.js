import request from '@/utils/request'

// 获取列表
export function getProjectRelationsList(params) {
  return request({
    url: '/perform-duties-service/project/employeeProjectRelations/list',
    method: 'GET',
    params,
  })
}

// 创建数据
export function creatProjectRelations(data) {
  return request({
    url: '/perform-duties-service/project/employeeProjectRelations/batchBinding',
    method: 'post',
    data,
  })
}

// 根据id获取数据
export function getProjectRelationsById(id) {
  return request({
    url: `/perform-duties-service/project/employeeProjectRelations/${id}`,
    method: 'get',
  })
}

// 修改数据
export function updateProjectRelations(data) {
  return request({
    url: `/perform-duties-service/project/employeeProjectRelations/update`,
    method: 'put',
    data,
  })
}

//删除对象组
export function delProjectRelations(params) {
  return request({
    url: '/perform-duties-service/project/employeeProjectRelations/delete',
    method: 'delete',
    params,
  })
}

// 人员项目状态调整
export function getChangeStatus(data) {
  return request({
    url: '/perform-duties-service/project/employeeProjectRelations/changeStatus',
    method: 'post',
    data,
  })
}

// 查询人员进出项目审核记录
export function entryAndExitProjectExamineRecord(params) {
  return request({
    url: '/perform-duties-service/project/employeeProjectRelations/entryAndExitProjectExamineRecord',
    method: 'GET',
    params,
  })
}
