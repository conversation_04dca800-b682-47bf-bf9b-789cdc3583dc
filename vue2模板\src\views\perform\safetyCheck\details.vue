<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '标准库'"
      :visible.sync="dialogFormVisible"
      width="700px"
      @close="close"
    >
      <el-form ref="form" label-width="150px" :model="form" :rules="rules">
        <el-form-item label="检查内容：" prop="inspectionContent">
          <el-input
            v-model="form.inspectionContent"
            placeholder="请输入检查内容
"
          />
        </el-form-item>
        <el-form-item label="检查类型：" prop="inspectionType">
          <el-select
            v-model="form.inspectionType"
            placeholder="请选择检查类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in dictList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="数据状态：" prop="dataStatus">
          <el-radio-group v-model="form.dataStatus">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <!-- <el-form-item
          class="logo"
          label="检查项参考标准："
          prop="referenceStandardAttachmentAttach"
        >
          <file-uploader
            ref="fileUploader"
            v-model="uploadedFiles"
            @on-remove="handleFileRemove"
            @on-success="handleFileSuccess"
          />
        </el-form-item> -->
      </el-form>
      <div style="margin: 10px 0">
        <el-button type="primary" @click="handleAdd">添 加</el-button>
        <el-dropdown
          split-button
          style="margin-left: 10px"
          @command="handleDropdown"
        >
          导入
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                command="handleDownloadTemplate"
                icon="el-icon-download"
              >
                下载模板
              </el-dropdown-item>
              <el-dropdown-item command="showImportDialog" icon="el-icon-top">
                导入数据
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <el-table border :data="form.safetyInspectionInfoDtoList">
        <el-table-column align="center" label="检查项" prop="inspectionItem">
          <template #default="scope">
            <el-input
              v-model="scope.row.inspectionItem"
              placeholder="请输入检查项"
            />
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="检查要求"
          prop="inspectionRequirements"
        >
          <template #default="scope">
            <el-input
              v-model="scope.row.inspectionRequirements"
              placeholder="请输入检查要求"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </template>

      <!-- 导入 -->
      <el-dialog
        append-to-body
        :close-on-click-modal="false"
        :title="importDialog.title"
        :visible.sync="importDialog.visible"
        width="600px"
        @close="closeImportDialog"
      >
        <el-form ref="importFormRef" label-width="80px" :model="importFormData">
          <el-form-item label="Excel">
            <el-upload
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              action=""
              :auto-upload="false"
              class="upload-demo"
              drag
              :file-list="excelFilelist"
              :limit="1"
              :on-change="handleExcelChange"
            >
              <el-icon class="el-icon--upload">
                <upload-filled />
              </el-icon>
              <div class="el-upload__text">
                <em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">xls/xlsx 文件</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitImportForm">
              确 定
            </el-button>
            <el-button @click="closeImportDialog">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
  import { baseURL, buketName } from '@/config'
  import { getToken } from '@/utils/token'
  // import FileUploader from '@/components/FileUploader'
  import {
    getSafetyInspectionDetails,
    addSafetyInspectionStandards,
    updateSafetyInspectionStandards,
    importSafetyInspectionStandards,
    exportSafetyInspectionStandardsTemplate,
  } from '@/api/perform/safetyCheck'
  import moment from 'moment'
  export default {
    name: '',
    // components: {
    //   FileUploader,
    // },
    props: {
      dictList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          inspectionContent: '',
          inspectionType: '',
          dataStatus: true,
          referenceStandardAttachmentAttach: {
            code: '',
            attachInfoList: [],
          },
          safetyInspectionInfoDtoList: [],
        },
        content: '',
        rules: {
          inspectionContent: [
            { required: true, trigger: 'blur', message: '检查内容必填' },
          ],
          inspectionType: [
            { required: true, trigger: 'change', message: '检查类型必填' },
          ],
          referenceStandardAttachmentAttach: [
            {
              required: true,
              trigger: ['change', 'blur'],
              message: '检查项参考标准必填',
              validator: (rule, value, callback) => {
                if (
                  value &&
                  value.attachInfoList &&
                  value.attachInfoList.length > 0
                ) {
                  callback()
                } else {
                  callback(new Error('检查项参考标准必填'))
                }
              },
            },
          ],
        },
        title: '',
        dialogFormVisible: false,
        //上传
        uploadUrl: `${baseURL}swisp-base-service/api/v1/oss/ali/upload?subPath=perform-file/${moment().format(
          'YYYY/MM/DD'
        )}&buketName=${buketName}`,
        headerObj: {
          Authorization: `${getToken()}`,
        },
        uploadedFiles: [],
        importFormData: {},
        // 导入
        importDialog: {
          title: '安全检查标准库导入',
          visible: false,
        },
        excelFile: undefined,
        excelFilelist: [],
        messageBox: undefined,
      }
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.uploadedFiles = []
        } else {
          this.title = '编辑'
          this.getDetails(row.id)
        }
        this.dialogFormVisible = true
      },
      getDetails(id) {
        getSafetyInspectionDetails(id).then((res) => {
          const { data } = res
          this.form = data

          this.uploadedFiles = []

          if (
            data.referenceStandardAttachmentAttach &&
            data.referenceStandardAttachmentAttach.attachInfoList &&
            Array.isArray(data.referenceStandardAttachmentAttach.attachInfoList)
          ) {
            // 如果是数组格式
            this.uploadedFiles = JSON.parse(
              JSON.stringify(
                data.referenceStandardAttachmentAttach.attachInfoList
              )
            )
          } else if (
            data.referenceStandardAttachmentAttach &&
            data.referenceStandardAttachmentAttach.attachInfoList
          ) {
            // 兼容旧数据，如果是字符串格式
            const fileInfo = {
              name: '附件',
              url: data.referenceStandardAttachmentAttach.attachInfoList,
              platform: '',
            }
            this.uploadedFiles.push(fileInfo)
          } else {
            // 确保初始化附件属性
            this.form.referenceStandardAttachmentAttach = {
              code: '',
              attachInfoList: [],
            }
            this.uploadedFiles = []
          }
        })
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.uploadedFiles = []
        this.dialogFormVisible = false
      },
      save() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            const formData = {
              ...this.form,
              // referenceStandardAttachmentAttach: {
              //   code: '',
              //   attachInfoList: this.uploadedFiles,
              // },
            }
            if (!this.form.id) {
              addSafetyInspectionStandards(formData).then(() => {
                this.$message.success('添加成功')
                this.$emit('refreshDataList')
                this.close()
              })
            } else {
              updateSafetyInspectionStandards(formData).then(() => {
                this.$message.success('修改成功')
                this.$emit('refreshDataList')
                this.close()
              })
            }
          } else {
            return false
          }
        })
      },
      handleAdd() {
        this.form.safetyInspectionInfoDtoList.push({
          inspectionItem: '',
          inspectionRequirements: '',
        })
      },
      handleDelete(index) {
        this.form.safetyInspectionInfoDtoList.splice(index, 1)
      },
      handleFileSuccess(fileInfo) {
        if (!this.form.referenceStandardAttachmentAttach) {
          this.form.referenceStandardAttachmentAttach = {
            code: '',
            attachInfoList: [],
          }
        }

        this.form.referenceStandardAttachmentAttach.attachInfoList = [
          ...this.uploadedFiles,
        ]

        this.$message.success(`文件 ${fileInfo.name} 上传成功`)
      },
      handleFileRemove(file) {
        if (this.form.referenceStandardAttachmentAttach) {
          this.form.referenceStandardAttachmentAttach.attachInfoList = [
            ...this.uploadedFiles,
          ]
        }

        this.$message.info(`文件 ${file.name} 已移除`)
      },
      /**
       * 导入下拉框
       */
      handleDropdown(command) {
        if (command == 'handleDownloadTemplate') {
          this.handleDownloadTemplate()
        }
        if (command == 'showImportDialog') {
          this.showImportDialog()
        }
      },
      /**
       * 下载导入模板
       */
      handleDownloadTemplate() {
        exportSafetyInspectionStandardsTemplate().then((res) => {
          const blob = new Blob([res.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = '安全检查标准库导入模板.xlsx'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        })
      },

      /**
       * 导入表单弹窗
       */
      async showImportDialog() {
        this.importDialog.visible = true
      },
      /**
       * Excel文件change事件
       *
       * @param file
       */
      handleExcelChange(file) {
        if (!/\.(xlsx|xls|XLSX|XLS)$/.test(file.name)) {
          this.$baseMessage('上传Excel只能为xlsx、xls格式', 'warning')
          this.excelFile = undefined
          this.excelFilelist = []
          return false
        }
        // console.log(file.raw, 'file')
        this.excelFile = file.raw
      },

      /**
       * Excel文件上传
       */
      submitImportForm() {
        this.$refs.importFormRef.validate((valid) => {
          if (valid) {
            if (!this.excelFile) {
              this.$baseMessage('上传Excel文件不能为空', 'warning')
              return false
            }
            this.messageBox = this.$loading({
              lock: true,
              text: '上传中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            importSafetyInspectionStandards(this.excelFile)
              .then((response) => {
                this.messageBox.close()
                if (response.data == null) response.data = []
                this.form.safetyInspectionInfoDtoList =
                  this.form.safetyInspectionInfoDtoList || []
                if (response.data.length) {
                  response.data.forEach((item) => {
                    this.form.safetyInspectionInfoDtoList.push({
                      inspectionItem: item.checkTheItem,
                      inspectionRequirements: item.checkTheRequirements,
                    })
                  })
                }
                this.closeImportDialog()
                this.$message.success('导入成功')
              })
              .catch((err) => {
                this.messageBox.close()
                console.log(err, 'err')
              })
          }
        })
      },
      closeInnerVisible() {
        this.errTitle = ''
        this.errData = {}
        this.innerVisible = false
        this.closeImportDialog()
        this.handleQuery({ clientId: this.clientId })
      },
      /**
       * 关闭导入弹窗
       */
      closeImportDialog() {
        this.importDialog.visible = false
        this.excelFile = undefined
        this.excelFilelist = []
        this.$refs.importFormRef.resetFields()
      },
    },
  }
</script>

<style lang="scss" scoped>
  /*
    .logo {
      ::v-deep {
        .avatar-uploader {
          .el-upload {
            position: relative;
            overflow: hidden;
            cursor: pointer;
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
          }
          .el-upload:hover {
            border-color: #409eff;
          }
          .avatar-uploader-icon {
            width: 100px;
            height: 100px;
            font-size: 28px;
            line-height: 100px;
            color: #8c939d;
            text-align: center;
          }
          .avatar {
            display: block;
            width: 100px;
            height: 100px;
          }
        }
      }
    }
*/
</style>
