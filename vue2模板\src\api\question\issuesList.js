import request from '@/utils/request'

// 整改人查询问题反馈
export function getRectifierQueryList(params) {
  return request({
    url: '/perform-duties-service/issueFeedbackAndRectification/rectifierQueryList',
    method: 'get',
    params,
  })
}

// 查询问题反馈
export function getRectifierList(params) {
  return request({
    url: '/perform-duties-service/issueFeedbackAndRectification/list',
    method: 'get',
    params,
  })
}

// 通过ID查询问题整改详情
export function getRectificationById(id) {
  return request({
    url: '/perform-duties-service/issueFeedbackAndRectification/' + id,
    method: 'get',
  })
}

// 整改提交
export function rectificationSubmit(data) {
  return request({
    url: '/perform-duties-service/issueFeedbackAndRectification/handleFeedbackOnIssues ',
    method: 'post',
    data,
  })
}

// 列表导出
export function exportIssuesList(params) {
  return request({
    url: '/perform-duties-service/issueFeedbackAndRectification/export_excel',
    method: 'get',
    params,
    responseType: 'blob',
  })
}
