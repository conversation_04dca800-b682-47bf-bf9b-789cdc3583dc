<!-- 项目人员 -->
<template>
  <div class="app-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item>
        <el-button icon="el-icon-plus" type="success" @click="handleAdd">
          新增
        </el-button>
        <el-button
          :disabled="single"
          icon="el-icon-delete"
          type="danger"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-form-item>
      <!-- <el-form-item>
        <el-dropdown
          split-button
          style="margin-left: 0px"
          @command="handleDropdown"
        >
          导入
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                command="handleDownloadTemplate"
                icon="el-icon-download"
              >
                下载模板
              </el-dropdown-item>
              <el-dropdown-item command="showImportDialog" icon="el-icon-top">
                导入数据
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          icon="el-icon-download"
          style="margin-left: 12px"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-form-item> -->

      <el-form-item label="项目名称" prop="projectId">
        <el-select
          v-model="queryParams.projectId"
          clearable
          filterable
          placeholder="请选择"
          @change="goUserByProject"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="userByProjectList && userByProjectList.length"
        label="人员"
        prop="employeeId"
      >
        <el-select
          v-model="queryParams.employeeId"
          clearable
          filterable
          placeholder="请选择"
          style="width: 200px"
        >
          <el-option
            v-for="item in userByProjectList"
            :key="item.employeeId"
            :label="item.employeeName"
            :value="item.employeeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          class="filter-item"
          icon="el-icon-search"
          type="primary"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="人员" prop="employeeName" />
      <el-table-column align="center" label="项目名称" prop="projectName" />
      <el-table-column align="center" label="部门" prop="deptName" />
      <el-table-column align="center" label="级别" prop="level" />
      <el-table-column align="center" label="岗位" prop="position" />
      <el-table-column align="center" label="操作" width="140">
        <template #default="scope">
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页工具条 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <el-dialog
      :before-close="handleClose"
      :title="dialog.title + '项目人员'"
      :visible.sync="dialog.visible"
      width="50%"
    >
      <el-form ref="formRef" label-width="80px" :model="dialog.formData">
        <el-form-item label="选择项目">
          <el-select
            v-model="dialog.formData.userId"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in storeUserList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目人员">
          <el-select
            v-model="dialog.formData.userId"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in storeUserList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <!-- 满足条件显示 -->
        <el-form-item label="审批人">
          <el-select
            v-model="dialog.formData.userId"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in storeUserList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleClose">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 详情 -->
    <Details ref="details" @refreshDataList="getList" />
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import Details from './details.vue'
  import {
    getProjectRelationsList,
    delProjectRelations,
  } from '@/api/project/projectPerson'
  import { getProjectList, getUserByProject } from '@/api/project/projectInfo'
  export default {
    name: 'ProjectPerson',
    components: {
      Details,
    },
    data() {
      return {
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          isInProject: true,
          projectId: '',
          employeeId: undefined,
        },
        total: 0,
        dataList: [],
        tableData: [], // 用于存储处理后的表格数据
        loading: false,
        selectionList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        single: true,
        dialog: {
          title: '添加',
          visible: false,
          formData: {},
        },
        projectList: [],
        userByProjectList: [],
      }
    },
    computed: {
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
    },
    mounted() {
      this.getList()
      this.goProjectList()
    },
    methods: {
      async getList() {
        this.loading = true
        try {
          const res = await getProjectRelationsList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            this.tableData = data
            this.total = page.totalCount
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      goUserByProject() {
        this.queryParams.employeeId = null
        if (!this.queryParams.projectId) {
          this.userByProjectList = []
          return
        }
        getUserByProject({
          projectId: this.queryParams.projectId,
          pageNum: 1,
          pageSize: 9999,
        }).then((res) => {
          const { code, data } = res
          if (code === 200 && data.relationship !== 2) {
            this.userByProjectList = data
          } else {
            this.userByProjectList = []
          }
        })
      },
      handleAdd() {
        // this.dialog.title = '添加'
        // this.dialog.visible = true
        this.$refs.details.showEdit()
      },
      handleUpdate(row) {
        this.dialog.title = '编辑'
        this.$refs.formRef.resetFields()
        this.dialog.visible = true
        this.dialog.formData = row
      },
      handleClose() {
        this.$refs.formRef.resetFields()
        this.dialog.visible = false
      },
      handleDelete(row) {
        let idstr = ''
        if (row) {
          idstr = row.id
        } else {
          const ids = this.selectionList.map((item) => item.id)
          if (ids.length === 0) {
            this.$message.warning('请选择要删除的记录')
            return
          }
          idstr = ids.join(',')
        }
        console.log(idstr)

        this.$confirm('确认删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          delProjectRelations({ ids: idstr }).then(() => {
            this.$message.success('删除成功')
            this.single = true
            this.getList()
          })
        })
      },
      handleQuery() {
        this.getList()
      },
      resetQuery() {
        this.userByProjectList = []
        this.$refs.queryFormRef.resetFields()
        this.getList()
      },
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length ? false : true
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.handleQuery()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.handleQuery()
      },
    },
  }
</script>

<style lang="scss" scoped></style>
