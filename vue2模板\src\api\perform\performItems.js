import request from '@/utils/request'

// 创建数据
export function addDutyInspectionItems(data) {
  return request({
    url: '/perform-duties-service/perform/dutyInspectionItems',
    method: 'post',
    data,
  })
}

// 修改数据
export function updateDutyInspectionItems(data) {
  return request({
    url: '/perform-duties-service/perform/dutyInspectionItems/update',
    method: 'put',
    data,
  })
}

// 分页获取列表
export function getDutyInspectionItemsList(params) {
  return request({
    url: '/perform-duties-service/perform/dutyInspectionItems/list',
    method: 'get',
    params,
  })
}

//删除对象组
export function delDutyInspectionItems(params) {
  return request({
    url: '/perform-duties-service/perform/dutyInspectionItems/delete',
    method: 'delete',
    params,
  })
}
