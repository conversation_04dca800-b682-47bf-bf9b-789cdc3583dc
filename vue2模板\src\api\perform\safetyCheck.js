import request from '@/utils/request'
import { getToken } from '@/utils/token'

// 创建数据
export function addSafetyInspectionStandards(data) {
  return request({
    url: '/perform-duties-service/system/safetyInspectionStandards',
    method: 'post',
    data,
  })
}

// 获取数据
export function getSafetyInspectionDetails(id) {
  return request({
    url: '/perform-duties-service/system/safetyInspectionStandards/' + id,
    method: 'get',
  })
}

// 分页获取列表
export function getSafetyInspectionStandardsList(params) {
  return request({
    url: '/perform-duties-service/system/safetyInspectionStandards/list',
    method: 'get',
    params,
  })
}

// 更新数据
export function updateSafetyInspectionStandards(data) {
  return request({
    url: '/perform-duties-service/system/safetyInspectionStandards/update',
    method: 'put',
    data,
  })
}

//删除对象组
export function delSafetyInspectionStandards(params) {
  return request({
    url: '/perform-duties-service/system/safetyInspectionStandards/delete',
    method: 'delete',
    params,
  })
}

// PC-导出检查明细模板
export function exportSafetyInspectionStandardsTemplate() {
  return request({
    url: '/perform-duties-service/system/safetyInspectionStandards/checkTheDetailImportTemplate',
    method: 'post',
    responseType: 'blob',
  })
}

// PC-导入检查明细
export function importSafetyInspectionStandards(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/perform-duties-service/system/safetyInspectionStandards/importCheckInfoExcel',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `${getToken()}`,
    },
  })
}
