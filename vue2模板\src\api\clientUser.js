import request from '@/utils/request'
import { getToken } from '@/utils/token'

export function getClientUserList(clientId, params) {
  return request({
    url: `/swisp-base-service/api/v1/users/client/${clientId}`,
    method: 'get',
    params,
  })
}

//给用户授权客户端
export function putAssignClientToUser(data) {
  return request({
    url: '/swisp-base-service/api/v1/users/assign/clients',
    method: 'put',
    data,
  })
}
//根据用户id获取已授权客户端
export function getAssignClientByUserId(params) {
  return request({
    url: '/swisp-base-service/api/v1/oauth-clients/listClientsByUserId',
    method: 'get',
    params,
  })
}

/**
 * 导入客户端用户
 * @param  data
 * @returns
 */
export function importClientUser(file, clientId) {
  const formData = new FormData()
  formData.append('file', file)
  // formData.append('deptId', deptId.toString())
  formData.append('clientId', clientId)
  return request({
    url: '/swisp-base-service/api/v1/users/importClientUser',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `${getToken()}`,
    },
  })
}

// export function importYSClientUser(clientId, file) {
//   const formData = new FormData()
//   formData.append('file', file)
//   // formData.append('deptId', deptId.toString())
//   formData.append('clientId', clientId)
//   return request({
//     url: '/swisp-base-service/api/v1/users/importYueShengClientByExcel',
//     method: 'post',
//     data: formData,
//     headers: {
//       'Content-Type': 'multipart/form-data',
//       Authorization: `${getToken()}`,
//     },
//   })
// }

/**
 * 新增授权
 */
export function addClients(data) {
  return request({
    url: '/swisp-base-service/api/v1/users/assign/addClients',
    method: 'put',
    data,
  })
}
/**
 * 取消授权
 */
export function cancelClients(data) {
  return request({
    url: '/swisp-base-service/api/v1/users/assign/clientsCancel',
    method: 'post',
    data,
  })
}
