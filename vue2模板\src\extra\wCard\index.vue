<template>
  <el-card shadow="hover" style="height: 315px">
    <div slot="header" style="display: flex; justify-content: space-between">
      <span>
        <vab-icon :icon="icon || 'align-justify'" />
        {{ title }}
      </span>
      <div style="display: flex">
        <slot name="search"></slot>
        <el-tag v-if="number > 0" effect="dark" type="warning">
          {{ number }}
        </el-tag>
        <el-button v-if="moreUrl" size="mini" type="primary">
          <router-link
            style="display: block; width: 100%; color: #fff"
            :to="{
              name: moreUrl,
            }"
          >
            更多...
          </router-link>
        </el-button>
      </div>
    </div>
    <div
      class="infinite-list-wrapper"
      :class="[list.length >= 6 ? 'showSpace' : '']"
    >
      <div
        v-if="isShowloading || list.length == 0"
        style="width: 100%; height: 100%; text-align: center"
      >
        <div v-if="loading" style="margin-top: 100px">
          <vab-icon icon="loader-2-line" />
          <span>正在加载中...</span>
        </div>
        <div v-else style="margin-top: 100px">
          <span>暂无数据</span>
        </div>
      </div>
      <div v-else>
        <ul class="list">
          <li v-for="(item, index) in list" :key="index" class="list-item">
            <div
              v-if="item.action == 'LeaderIdeas'"
              class="nresTl"
              @click="goDetail(item)"
            >
              <div class="ti-shoe" style="margin-right: 5px">
                <i v-if="item.icon" :class="item.icon"></i>
                <span v-if="item.No">{{ item.No }}、</span>
                <el-tag
                  v-if="showListTag"
                  style="margin-bottom: 5px"
                  type="warning"
                >
                  审查
                </el-tag>
              </div>
              <div class="shoe">{{ item.subject.name }}</div>
              <div class="times">{{ item.updated_at }}</div>
            </div>
            <div v-else class="nresTl" @click="goDetail(item)">
              <div class="ti-shoe" style="margin-right: 5px">
                <i v-if="item.icon" :class="item.icon"></i>
                <span v-if="item.No">{{ item.No }}、</span>
                <el-tag
                  v-if="showListTag"
                  style="margin-bottom: 5px"
                  :type="!item.onReview ? 'success' : ''"
                >
                  {{ !item.onReview ? '编制' : '审核' }}
                </el-tag>
              </div>
              <div class="shoe">{{ item.title }} {{ item.descr }}</div>
              <div class="times">{{ item.handleDate }}</div>
            </div>
            <div style="clear: both"></div>
          </li>
        </ul>
      </div>
    </div>
  </el-card>
</template>
<script>
  export default {
    props: {
      cardChickType: {
        type: String,
        default: function () {
          return ''
        },
      },
      //卡片标题
      title: {
        type: String,
        default: function () {
          return ''
        },
      },
      //卡片内容列表
      list: {
        type: Array,
        default: function () {
          return []
        },
      },
      //卡片头部icon
      icon: {
        type: String,
        default: function () {
          return ''
        },
      },
      //卡片是否显示滚动条
      showSpace: {
        type: Boolean,
        default: function () {
          return false
        },
      },
      number: {
        type: Number,
        default: function () {
          return 0
        },
      },
      moreUrl: {
        type: String,
        default: function () {
          return ''
        },
      },
      showIconType: {
        type: String,
        default: function () {
          return ''
        },
      },
      showListTag: {
        type: Boolean,
        default: function () {
          return false
        },
      },
      loading: {
        type: Boolean,
        default: function () {
          return false
        },
      },
    },
    data() {
      return {
        isShowloading: true,
      }
    },
    watch: {
      list(val) {
        var that = this
        that.list = val
        if (that.list.length != 0) {
          that.isShowloading = false
        }
      },
    },
    methods: {
      goDetail(item) {
        this.$emit('godetail', item)
      },
    },
  }
</script>
<style lang="scss" scoped>
  .showSpace {
    height: 222px;
    overflow: auto;
  }
  .ti-shoe {
    float: left;
    line-height: 30px;
  }
  .shoe {
    float: left;
    width: 70%;
    line-height: 30px;
  }
  .times {
    float: right;
    line-height: 30px;
  }
  .red {
    color: red;
  }
  .grey {
    color: #909399;
  }
  .list {
    padding: 0;
    list-style: none;
  }
  .list-item {
    border-bottom: 1px dashed #dcdfe6;
  }
  .nresTl {
    width: 100%;
    padding: 10px 0px;
    padding-left: 10px;
    cursor: pointer;
    &:hover {
      color: #1890ff;
    }
  }
</style>
