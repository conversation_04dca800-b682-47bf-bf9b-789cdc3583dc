/**
 * @description vue.config.js全局配置
 */
const path = require('path')
const {
  // baseURL,
  publicPath,
  assetsDir,
  outputDir,
  lintOnSave,
  transpileDependencies,
  title,
  abbreviation,
  devPort,
  providePlugin,
  build7z,
  buildGzip,
  imageCompression,
  // curNodeEnv,
  // npm_config_env,
} = require('./src/config')
const rely = require('./vab.config')
const { webpackBarName, webpackBanner } = require('./vab.config')
const { version, author } = require('./package.json')
const Webpack = require('webpack')
const WebpackBar = require('webpackbar')
const FileManagerPlugin = require('filemanager-webpack-plugin')
const dayjs = require('dayjs')
const dateTime = dayjs().format('YYYYMMDDHHmmss')
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const productionGzipExtensions = ['html', 'js', 'css', 'svg']
const pc = require('picocolors')
process.env.VUE_APP_TITLE = title
process.env.VUE_APP_AUTHOR = author
process.env.VUE_APP_RANDOM = `${dateTime}-${process.env.VUE_GITHUB_USER_NAME}`
process.env.VUE_APP_UPDATE_TIME = dateTime
process.env.VUE_APP_VERSION = version
process.env.VUE_APP_RELY = rely
// process.env.NODE_ENV = curNodeEnv
// process.env.npm_config_env = npm_config_env

const resolve = (dir) => {
  return path.join(__dirname, dir)
}
//获取当前环境，由cross-env设置的
const VUE_APP_MODE = process.env.VUE_APP_MODE
//加载对应.env文件的变量
require('dotenv').config({ path: `./src/config/env/.env.${VUE_APP_MODE}` })
//因为process.env里面的值很多，所以我们定义一个正则，只有是命名以React_App_开头的变量我们才去设置它
const reg = /^VUE_APP_/
const env = {}
for (const key in process.env) {
  //只有key为NODE_ENV或者正则校验通过才去设置
  if (key === 'NODE_ENV' || reg.test(key)) {
    env[key] = process.env[key]
  }
}
module.exports = {
  publicPath,
  assetsDir,
  outputDir,
  lintOnSave,
  transpileDependencies,
  devServer: {
    hot: true,
    port: devPort,
    open: true,
    noInfo: false,
    overlay: {
      warnings: true,
      errors: true,
    },
    // 注释掉的地方是前端配置代理访问后端的示例
    // baseURL必须为/xxx，而不是后端服务器，请先了解代理逻辑，再设置前端代理
    // ！！！一定要注意！！！
    // 1.这里配置了跨域及代理只针对开发环境生效
    // 2.不建议你在前端配置跨域，建议你后端配置Allow-Origin,Method,Headers，放行token字段，一步到位
    // 3.后端配置了跨域，就不需要前端再配置，会发生Origin冲突
    // proxy: 'https://gateway.gmbicloud.com/',
    proxy: {
      // [baseURL]: {
      //   target: `http://你的后端接口地址`,
      //   ws: true,
      //   changeOrigin: true,
      //   pathRewrite: {
      //     ['^' + baseURL]: '',
      //   },
      // },
      '/captcha': {
        target: `https://gateway.gmbicloud.com`,
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/captcha': '',
        },
      },
    },
    // after: require('./mock'),
  },
  pwa: {
    workboxOptions: {
      skipWaiting: true,
      clientsClaim: true,
    },
    themeColor: '#ffffff',
    msTileColor: '#ffffff',
    appleMobileWebAppCapable: 'yes',
    appleMobileWebAppStatusBarStyle: 'black',
    manifestOptions: {
      // name: 'Vue Admin  - Admin Pro',
      // short_name: 'Admin Pro',
      // background_color: '#ffffff',
    },
  },
  configureWebpack() {
    return {
      resolve: {
        alias: {
          '~': resolve('.'),
          '@': resolve('src'),
        },
      },
      plugins: [
        new Webpack.ProvidePlugin(providePlugin),
        new WebpackBar({
          name: webpackBarName,
        }),
        new Webpack.DefinePlugin({
          'process.env': JSON.stringify(env),
        }),
      ],
      output: {
        // 输出重构 打包编译后的js文件名称,添加时间戳.
        filename: `js/js[name].${dateTime}.js`,
        chunkFilename: `js/chunk.[id].${dateTime}.js`,
      },
    }
  },
  chainWebpack(config) {
    config.resolve.symlinks(true)
    config.module.rule('svg').exclude.add(resolve('src/icon'))
    config.module
      .rule('vabIcon')
      .test(/\.svg$/)
      .include.add(resolve('src/icon'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({ symbolId: 'vab-icon-[name]' })
    config.when(process.env.NODE_ENV === 'development', (config) => {
      config.devtool('source-map')
    })
    config.when(
      process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test',
      (config) => {
        //为了防止忘记配置而造成项目无法打包，请保留以下提示
        if (
          process.env.NODE_ENV === 'production' ||
          process.env.NODE_ENV === 'test'
        ) {
          if (
            process['env'].VUE_GITHUB_USER_NAME == 'test' ||
            process['env'].VUE_APP_SECRET_KEY == 'preview'
          ) {
            console.log(
              `\n\n${pc.red(
                '检测到您的用户名或key未配置，key在购买时通过邮件邀请函发放，如您已购买请仔细阅读文档并进行配置，配置完成后方可打包使用。购买地址：https://vue-admin-beautiful.com/authorization'
              )}\n`
            )
            process.exit()
          }
        }
        config.performance.set('hints', false)
        config.devtool('none')
        config.optimization.splitChunks({
          automaticNameDelimiter: '-',
          chunks: 'all',
          cacheGroups: {
            chunk: {
              name: 'vab-chunk',
              test: /[\\/]node_modules[\\/]/,
              minSize: 131072,
              maxSize: 524288,
              chunks: 'async',
              minChunks: 2,
              priority: 10,
            },
            vue: {
              name: 'vue',
              test: /[\\/]node_modules[\\/](vue(.*)|core-js)[\\/]/,
              chunks: 'initial',
              priority: 20,
            },
            elementUI: {
              name: 'element-ui',
              test: /[\\/]node_modules[\\/]element-ui(.*)[\\/]/,
              priority: 30,
            },
            extra: {
              name: 'vab-extra',
              test: resolve('src/extra'),
              priority: 40,
            },
            // 根据使用模块抽取公共代码
            // echarts: {
            //   name: 'echarts',
            //   test: /[\\/]node_modules[\\/](echarts|zrender|tslib)[\\/]/,
            //   priority: 50,
            // },
          },
        })
        config
          .plugin('banner')
          .use(Webpack.BannerPlugin, [`${webpackBanner}${dateTime}`])
        if (imageCompression)
          config.module
            .rule('images')
            .use('image-webpack-loader')
            .loader('image-webpack-loader')
            .options({
              bypassOnDebug: true,
            })
            .end()
        if (buildGzip)
          config.plugin('compression').use(CompressionWebpackPlugin, [
            {
              filename: '[path][base].gz[query]',
              algorithm: 'gzip',
              test: new RegExp(
                '\\.(' + productionGzipExtensions.join('|') + ')$'
              ),
              threshold: 8192,
              minRatio: 0.8,
            },
          ])
        if (build7z)
          config.plugin('fileManager').use(FileManagerPlugin, [
            {
              events: {
                onEnd: {
                  archive: [
                    {
                      source: `./${outputDir}`,
                      destination: `./${outputDir}/${abbreviation}_${dayjs().unix()}.zip`,
                    },
                  ],
                },
              },
            },
          ])
      }
    )
  },
  runtimeCompiler: true,
  productionSourceMap: false,
  css: {
    requireModuleExtension: true,
    sourceMap: true,
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: 'expanded' },
        additionalData(content, loaderContext) {
          const { resourcePath, rootContext } = loaderContext
          const relativePath = path.relative(rootContext, resourcePath)
          if (
            relativePath.replace(/\\/g, '/') !==
            'src/vab/styles/variables/variables.scss'
          )
            return (
              '@use "sass:math";@import "~@/vab/styles/variables/variables.scss";' +
              content
            )
          return content
        },
      },
    },
  },
}
