/**
 * @description 登录、获取用户信息、退出登录、清除token逻辑，不建议修改
 */
import Vue from 'vue'
import { getUserInfo, login, socialLogin } from '@/api/user'
import { getToken, removeToken, setToken } from '@/utils/token'
import { resetRouter } from '@/router'
import { isArray, isString, isNumber } from '@/utils/validate'
import {
  title,
  tokenName,
  // baseURL
} from '@/config'
import { getUser } from '@/api/project/projectInfo'

// 缓存键名
const USER_LIST_CACHE_KEY = 'user_list_cache'
const PROJECT_ID_CACHE_KEY = 'project_id_cache'

// 从本地缓存获取数据
const getCachedUserList = () => {
  const cachedData = localStorage.getItem(USER_LIST_CACHE_KEY)
  return cachedData ? JSON.parse(cachedData) : []
}

const getCachedProjectId = () => {
  return localStorage.getItem(PROJECT_ID_CACHE_KEY) || ''
}

// 保存数据到本地缓存
const setCachedUserList = (userList) => {
  localStorage.setItem(USER_LIST_CACHE_KEY, JSON.stringify(userList))
}

const setCachedProjectId = (projectId) => {
  localStorage.setItem(PROJECT_ID_CACHE_KEY, projectId)
}

// 清除缓存
const clearUserCache = () => {
  localStorage.removeItem(USER_LIST_CACHE_KEY)
  localStorage.removeItem(PROJECT_ID_CACHE_KEY)
}

const state = () => ({
  token: getToken(),
  username: '游客',
  user: {},
  userId: '',
  projectId: getCachedProjectId(), // 从缓存初始化
  avatar: 'https://i.gtimg.cn/club/item/face/img/2/15922_100.gif',
  userList: getCachedUserList(), // 从缓存初始化
})
const getters = {
  token: (state) => state.token,
  username: (state) => state.username,
  avatar: (state) => state.avatar,
  user: (state) => state.user,
  userId: (state) => state.userId,
  projectId: (state) => state.projectId,
}
const mutations = {
  /**
   * @description 设置token
   * @param {*} state
   * @param {*} token
   */
  setToken(state, token) {
    state.token = token
    setToken(token)
  },
  /**
   * @description 设置用户名
   * @param {*} state
   * @param {*} username
   */
  setUsername(state, username) {
    state.username = username
  },
  /**
   * @description 设置头像
   * @param {*} state
   * @param {*} avatar
   */
  setAvatar(state, avatar) {
    state.avatar = avatar
  },
  /**
   * @description 设置用户
   * @param {*} state
   * @param {*} user
   */ setUser(state, user) {
    state.user = user
  },
  /**
   * @description 设置用户id
   * @param {*} state
   * @param {*} userId
   */
  setUserId(state, userId) {
    state.userId = userId
  },
  /**
   * @description 设置项目id
   * @param {*} state
   * @param {*} projectId
   */
  setProjectId(state, projectId) {
    state.projectId = projectId
    setCachedProjectId(projectId) // 更新缓存
  },
  SET_USER_LIST: (state, userList) => {
    state.userList = userList
    setCachedUserList(userList) // 更新缓存
  },
}
const actions = {
  /**
   * @description 登录拦截放行时，设置虚拟角色
   * @param {*} { commit, dispatch }
   */
  setVirtualRoles({ commit, dispatch }) {
    dispatch('acl/setFull', true, { root: true })
    commit('setAvatar', 'https://i.gtimg.cn/club/item/face/img/2/15922_100.gif')
    commit('setUsername', 'admin')
    commit('setUser', {})
    commit('setUserId', '')
  },
  /**
   * @description 登录
   * @param {*} { commit }
   * @param {*} userInfo
   */
  async login({ commit, dispatch }, userInfo) {
    // 登录时清除缓存
    clearUserCache()

    const {
      data: { code, data, msg },
    } = await login(userInfo)
    if (!data || code != '00000') {
      Vue.prototype.$baseMessage(
        msg || `登录接口异常，未正确返回${tokenName}...`,
        'error'
      )
      return Promise.reject()
    }
    const { access_token, token_type } = data
    if (access_token && token_type) {
      const accessToken = token_type + ' ' + access_token
      commit('setToken', accessToken)
      dispatch('getUserList')
    } else {
      Vue.prototype.$baseMessage(
        msg || `登录接口异常，未正确返回${tokenName}...`,
        'error'
      )
      return Promise.reject()
    }
  },
  /**
   * @description 第三方登录
   * @param {*} {}
   * @param {*} tokenData
   */
  async socialLogin({ commit }, tokenData) {
    const {
      data: { [tokenName]: token },
    } = await socialLogin(tokenData)
    if (token) {
      commit('setToken', token)
      const hour = new Date().getHours()
      const thisTime =
        hour < 8
          ? '早上好'
          : hour <= 11
          ? '上午好'
          : hour <= 13
          ? '中午好'
          : hour < 18
          ? '下午好'
          : '晚上好'
      Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`)
    } else {
      const err = `login核心接口异常，请检查返回JSON格式是否正确，是否正确返回${tokenName}...`
      Vue.prototype.$baseMessage(err, 'error', 'vab-hey-message-error')
      throw err
    }
  },
  /**
   * @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
   * @param {*} { commit, dispatch, state }
   * @returns
   */
  async getUserInfo({ commit, dispatch }) {
    const {
      data: {
        data,
        data: { username, avatar, roles, permissions, userId, perms, clients },
      },
    } = await getUserInfo()
    /**
     * 检验返回数据是否正常，无对应参数，将使用默认用户名,头像,Roles和Permissions
     * username {String}
     * avatar {String}
     * roles {List}
     * ability {List}
     */
    if (
      (username && !isString(username)) ||
      (userId && !isNumber(userId)) ||
      (avatar && !isString(avatar)) ||
      (roles && !isArray(roles)) ||
      (permissions && !isArray(permissions)) ||
      (perms && !isArray(perms)) ||
      (clients && !isArray(clients))
    ) {
      const err = 'getUserInfo核心接口异常，请检查返回JSON格式是否正确'
      Vue.prototype.$baseMessage(err, 'error', 'vab-hey-message-error')
      throw err
    } else {
      // 如不使用username用户名,可删除以下代码
      if (username) commit('setUsername', username)
      if (data) commit('setUser', data)
      if (userId) commit('setUserId', userId)
      // 如不使用avatar头像,可删除以下代码
      if (avatar) commit('setAvatar', avatar)
      // 如不使用roles权限控制,可删除以下代码
      if (roles) dispatch('acl/setRole', roles, { root: true })
      // 如不使用permissions权限控制,可删除以下代码
      if (permissions)
        dispatch('acl/setPermission', permissions, { root: true })
      //设置按钮权限
      if (perms) dispatch('acl/setBtnRole', perms, { root: true })
      //客户端权限
      if (clients) dispatch('acl/setClient', clients, { root: true })
    }
  },
  /**
   * @description 退出登录
   * @param {*} { dispatch }
   */
  async logout({ dispatch }) {
    // await logout()
    await dispatch('resetAll')
  },
  /**
   * @description 重置token、roles、permission、router、tabsBar等
   * @param {*} { commit, dispatch }
   */
  async resetAll({ commit, dispatch }) {
    // 清除缓存
    clearUserCache()

    commit('setUsername', '游客')
    commit('setUserId', '')
    commit('setUser', {})
    commit('setProjectId', '')
    commit('setAvatar', 'https://i.gtimg.cn/club/item/face/img/2/15922_100.gif')
    commit('routes/setRoutes', [], { root: true })
    await dispatch('setToken', '')
    await dispatch('acl/setFull', false, { root: true })
    await dispatch('acl/setRole', [], { root: true })
    await dispatch('acl/setPermission', [], { root: true })
    await dispatch('tabs/delAllVisitedRoutes', null, { root: true })
    await resetRouter()
    removeToken()
  },
  /**
   * @description 设置token
   * @param {*} { commit }
   * @param {*} token
   */
  setToken({ commit }, token) {
    commit('setToken', token)
  },
  /**
   * @description 设置头像
   * @param {*} { commit }
   * @param {*} avatar
   */
  setAvatar({ commit }, avatar) {
    commit('setAvatar', avatar)
  },
  /**
   * @description 设置项目id
   * @param {*} { commit }
   * @param {*} projectId
   */
  setProjectId({ commit }, projectId) {
    commit('setProjectId', projectId)
  },
  // 获取用户列表
  getUserList({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 如果已有数据，直接返回
      if (state.userList && state.userList.length > 0) {
        resolve(state.userList)
        return
      }

      // 否则请求接口
      getUser({
        pageNum: 1,
        pageSize: 9999,
      })
        .then((res) => {
          const userList = res.data.list || []
          commit('SET_USER_LIST', userList)
          resolve(userList)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
}
export default { state, getters, mutations, actions }
