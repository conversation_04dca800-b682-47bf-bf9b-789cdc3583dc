<template>
  <div>
    <div class="drawer-container">
      <div class="drawer-content">
        <el-form
          ref="form"
          label-position="left"
          label-width="80px"
          :model="form"
          :rules="rules"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="人员：" prop="userId">
                {{ form.userName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目信息：" prop="projectId">
                {{ form.projectName }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="岗位：" prop="positionName">
                {{ form.positionName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="级别：" prop="level">
                {{ form.level }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="履职模板：" prop="dutyTemplateId">
                {{ form.dutyTemplateName }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="detail-section">
          <div class="detail-title">
            <span class="required-mark">*</span>
            安全生产责任清单明细
          </div>
          <el-table
            border
            :data="form.userDutyListsInfoDtoList"
            style="max-height: 400px; overflow-y: auto"
          >
            <el-table-column
              align="center"
              label="履职检查项"
              prop="inspectionItemsId"
              width="400px"
            >
              <template #default="scope">
                <template v-if="!form.id">
                  <span v-for="item in dutyList" :key="item.id">
                    <span v-if="scope.row.inspectionItemsId === item.id">
                      {{ item.dutyInspectionItem }}
                    </span>
                    <span v-else-if="scope.row.templatesInfoId === item.id">
                      {{ item.dutyInspectionItem }}
                    </span>
                  </span>
                </template>
                <template v-else>
                  <el-select
                    v-if="
                      scope.row.listStatus == 1 || scope.row.listStatus == 2
                    "
                    v-model="scope.row.inspectionItemsId"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in dutyList"
                      :key="item.id"
                      :label="item.dutyInspectionItem"
                      :value="item.id"
                    />
                  </el-select>
                  <span v-else>
                    {{ scope.row?.dutyInspectionItemCont?.dutyInspectionItem }}
                  </span>
                </template>
              </template>
            </el-table-column>
            <el-table-column align="center" label="类型" prop="type">
              <template #default="scope">
                <el-select
                  v-if="scope.row.listStatus == 1 || scope.row.listStatus == 2"
                  v-model="scope.row.type"
                  placeholder="请选择"
                >
                  <el-option label="自证履职" :value="0" />
                  <el-option label="扫码履职" :value="1" />
                </el-select>
                <span v-else>
                  <span v-if="scope.row.type">扫码履职</span>
                  <span v-else>自证履职</span>
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="工序" prop="process">
              <template #default="scope">
                <el-select
                  v-if="scope.row.listStatus == 1 || scope.row.listStatus == 2"
                  v-model="scope.row.process"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in processList"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
                <span v-else>
                  <span v-for="item in processList" :key="item.id">
                    <span v-if="scope.row.process === item.value">
                      {{ item.name }}
                    </span>
                  </span>
                </span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="履职时间/频率"
              prop="frequency"
            >
              <template #default="scope">
                <el-select
                  v-if="scope.row.listStatus == 1 || scope.row.listStatus == 2"
                  v-model="scope.row.frequency"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in frequencyList"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
                <span v-else>
                  <span v-for="item in frequencyList" :key="item.id">
                    <span v-if="scope.row.frequency === item.value">
                      {{ item.name }}
                    </span>
                  </span>
                </span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="要求频次"
              prop="frequencyCount"
            >
              <template #default="scope">
                <el-input
                  v-if="scope.row.listStatus == 1 || scope.row.listStatus == 2"
                  v-model="scope.row.frequencyCount"
                  min="1"
                  placeholder="请输入"
                  type="number"
                />
                <span v-else>
                  {{ scope.row.frequencyCount }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: '',
    props: {
      formData: {
        type: Object,
        default: () => {},
      },
      processList: {
        type: Array,
        default: () => [],
      },
      frequencyList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {},
      }
    },
    watch: {
      formData(val) {
        this.form = val
      },
    },
    methods: {},
  }
</script>

<style lang="scss" scoped>
  .drawer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .drawer-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .detail-section {
    margin-top: 20px;
  }

  .detail-title {
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 500;

    .required-mark {
      margin-right: 4px;
      color: #f56c6c;
    }
  }

  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    padding: 16px;
    text-align: center;
    background: #fff;
    border-top: 1px solid #e8e8e8;

    .el-button {
      margin-left: 8px;
    }
  }

  // 调整表单样式
  ::v-deep {
    .el-form-item {
      margin-bottom: 22px;
    }

    .el-table {
      margin-bottom: 60px;
    }
  }
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
