<template>
  <div>
    <div class="drawer-container">
      <div class="drawer-content">
        <el-form
          ref="form"
          label-position="left"
          label-width="80px"
          :model="form"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="人员：" prop="userId">
                {{ form.userName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目信息：" prop="projectId">
                {{ form.projectName }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="岗位：" prop="positionName">
                {{ form.positionName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="级别：" prop="level">
                {{ form.level }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="履职模板：" prop="dutyTemplateName">
                {{ form.dutyTemplateName }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="detail-section">
          <div class="detail-title">安全生产责任清单明细</div>
          <el-table
            border
            :data="form.userDutyListsInfoDtoList"
            style="max-height: 400px; overflow-y: auto"
          >
            <el-table-column
              align="center"
              label="履职检查项"
              prop="inspectionItemsId"
            >
              <template #default="scope">
                <span v-for="item in dutyList" :key="item.id">
                  <span v-if="scope.row.inspectionItemsId === item.id">
                    {{ item.dutyInspectionItem }}
                  </span>
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="类型" prop="type">
              <template #default="scope">
                <span v-if="scope.row.type">扫码履职</span>
                <span v-else>自证履职</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="工序" prop="process">
              <template #default="scope">
                <span v-for="item in processList" :key="item.id">
                  <span v-if="scope.row.process === item.value">
                    {{ item.name }}
                  </span>
                </span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="履职时间/频率"
              prop="frequency"
            >
              <template #default="scope">
                <span v-for="item in frequencyList" :key="item.id">
                  <span v-if="scope.row.frequency === item.value">
                    {{ item.name }}
                  </span>
                </span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="要求频次"
              prop="frequencyCount"
            />
            <el-table-column align="center" label="状态" prop="listStatus">
              <template #default="scope">
                <el-tag v-if="scope.row.listStatus == 0" type="info">
                  不改变
                </el-tag>
                <el-tag v-else-if="scope.row.listStatus == 1">修改</el-tag>
                <el-tag v-else-if="scope.row.listStatus == 2" type="success">
                  新增
                </el-tag>
                <el-tag v-else-if="scope.row.listStatus == 3" type="danger">
                  删除
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  export default {
    name: '',
    props: {
      formData: {
        type: Object,
        default: () => {},
      },
      dutyList: {
        type: Array,
        default: () => [],
      },
      processList: {
        type: Array,
        default: () => [],
      },
      frequencyList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          userId: null,
          userName: '',
          projectId: null,
          projectName: '',
          positionId: null,
          positionName: '',
          level: '',
          dutyTemplateId: null,
          dutyTemplateName: '',
          userDutyListsInfoDtoList: [],
        },
        content: '',
        title: '',
        statusList: [
          {
            label: '不改变',
            value: 0,
          },
          {
            label: '修改',
            value: 1,
          },
          {
            label: '新增',
            value: 2,
            disabled: true,
          },
          {
            label: '删除',
            value: 3,
          },
        ],
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
    },
    watch: {
      formData(val) {
        this.form = val
      },
    },
    methods: {},
  }
</script>

<style lang="scss" scoped>
  .drawer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .drawer-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .detail-section {
    margin-top: 20px;
  }

  .detail-title {
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 500;

    .required-mark {
      margin-right: 4px;
      color: #f56c6c;
    }
  }

  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    padding: 16px;
    text-align: center;
    background: #fff;
    border-top: 1px solid #e8e8e8;

    .el-button {
      margin-left: 8px;
    }
  }

  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
