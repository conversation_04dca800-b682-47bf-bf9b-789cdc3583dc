<!-- 履职模板 -->
<!-- 安全检查标准库 -->
<template>
  <div class="app-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item>
        <el-button icon="el-icon-plus" type="success" @click="handleAdd">
          新增
        </el-button>
        <el-button
          :disabled="single"
          icon="el-icon-delete"
          type="danger"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-dropdown
          split-button
          style="margin-left: 0px"
          @command="handleDropdown"
        >
          导入
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                command="handleDownloadTemplate"
                icon="el-icon-download"
              >
                下载模板
              </el-dropdown-item>
              <el-dropdown-item command="showImportDialog" icon="el-icon-top">
                导入数据
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          icon="el-icon-download"
          style="margin-left: 12px"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-form-item>

      <el-form-item label="履职模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          clearable
          placeholder="请输入名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          class="filter-item"
          icon="el-icon-search"
          type="primary"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="dataList"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column
        align="center"
        label="履职模板名称"
        prop="templateName"
      />
      <el-table-column align="center" label="岗位" prop="position" />
      <el-table-column align="center" label="级别" prop="level" />
      <!-- <el-table-column align="center" label="数据状态" prop="deleteStatus">
        <template #default="scope">
          <el-tag :type="scope.row.deleteStatus === 1 ? 'danger' : 'success'"/>
            {{ scope.row.deleteStatus === 1 ? '禁用' : '启用' }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="操作" width="140">
        <template #default="scope">
          <el-button
            circle
            icon="el-icon-edit-outline"
            plain
            type="primary"
            @click.stop="handleUpdate(scope.row)"
          />
          <!-- <el-button
            circle
            icon="el-icon-plus"
            plain
            type="success"
            @click.stop="handleAdd(scope.row)"
          /> -->
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页工具条 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <Details ref="details" @refreshDataList="getList" />

    <!-- 导入 -->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="importDialog.title"
      :visible.sync="importDialog.visible"
      width="600px"
      @close="closeImportDialog"
    >
      <el-form
        ref="importFormRef"
        v-loading="importDialog.loading"
        label-width="80px"
        :model="importFormData"
      >
        <el-form-item label="Excel">
          <el-upload
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            action=""
            :auto-upload="false"
            class="upload-demo"
            drag
            :file-list="excelFilelist"
            :limit="1"
            :on-change="handleExcelChange"
          >
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">xls/xlsx 文件</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitImportForm">确 定</el-button>
          <el-button @click="closeImportDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import Details from './details.vue'
  import {
    getDutyTemplatesList,
    delDutyTemplates,
    importPerformance,
    exportPerformance,
    exportPerformanceList,
  } from '@/api/perform/performanceTemplate'
  export default {
    name: 'SafetyCheck',
    components: {
      Details,
    },
    data() {
      return {
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          templateName: '',
        },
        total: 0,
        dataList: [],
        tableData: [], // 用于存储处理后的表格数据
        loading: false,
        selectionList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        single: true,
        // 导入相关数据
        importFormData: {},
        importDialog: {
          title: '履职模板导入',
          visible: false,
          loading: false,
        },
        excelFile: undefined,
        excelFilelist: [],
        messageBox: undefined,
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      async getList() {
        this.loading = true
        try {
          const res = await getDutyTemplatesList(this.queryParams)
          const { code, data, page } = res
          console.log(code, data, page)

          if (code === 200) {
            this.dataList = data
            this.total = page.totalCount
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      handleAdd() {
        this.$refs.details.showEdit()
      },
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
      handleDelete(row) {
        let idstr = ''
        if (row) {
          idstr = row.id
        } else {
          const ids = this.selectionList.map((item) => item.id)
          if (ids.length === 0) {
            this.$message.warning('请选择要删除的记录')
            return
          }
          idstr = ids.join(',')
        }
        console.log(idstr)

        this.$confirm('确认删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          delDutyTemplates({ ids: idstr }).then(() => {
            this.$message.success('删除成功')
            this.single = true
            this.getList()
          })
        })
      },
      handleQuery() {
        this.getList()
      },
      resetQuery() {
        this.queryParams.pageNum = 1
        this.$refs.queryFormRef.resetFields()
        this.getList()
      },
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length ? false : true
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.handleQuery()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.handleQuery()
      },
      /**
       * 导入下拉框
       */
      handleDropdown(command) {
        if (command == 'handleDownloadTemplate') {
          this.handleDownloadTemplate()
        }
        if (command == 'showImportDialog') {
          this.showImportDialog()
        }
      },
      /**
       * 下载导入模板
       */
      handleDownloadTemplate() {
        exportPerformance().then((response) => {
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
          })
          const a = document.createElement('a')
          const href = window.URL.createObjectURL(blob)
          a.href = href
          a.setAttribute('download', '履职模板.xlsx')
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(href)
        })
      },

      /**
       * 导入表单弹窗
       */
      async showImportDialog() {
        this.importDialog.visible = true
      },
      /**
       * Excel文件change事件
       *
       * @param file
       */
      handleExcelChange(file) {
        if (!/\.(xlsx|xls|XLSX|XLS)$/.test(file.name)) {
          this.$baseMessage('上传Excel只能为xlsx、xls格式', 'warning')
          this.excelFile = undefined
          this.excelFilelist = []
          return false
        }
        this.excelFile = file.raw
      },

      /**
       * Excel文件上传
       */
      submitImportForm() {
        this.importDialog.loading = true
        this.$refs.importFormRef.validate((valid) => {
          if (valid) {
            if (!this.excelFile) {
              this.$baseMessage('上传Excel文件不能为空', 'warning')
              return false
            }
            this.messageBox = this.$loading({
              lock: true,
              text: '上传中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            console.log(this.excelFile)

            importPerformance(this.excelFile)
              .then((response) => {
                console.log(response, 'response')
                this.messageBox.close()
                this.closeInnerVisible()
                this.$message.success('导入成功')
                this.importDialog.loading = false
              })
              .catch((err) => {
                this.messageBox.close()
                this.importDialog.loading = false
                console.log(err, 'err')
              })
          }
        })
      },
      closeInnerVisible() {
        this.errTitle = ''
        this.errData = {}
        this.innerVisible = false
        this.closeImportDialog()
        this.handleQuery()
      },
      /**
       * 关闭导入弹窗
       */
      closeImportDialog() {
        this.importDialog.visible = false
        this.excelFile = undefined
        this.excelFilelist = []
        this.$refs.importFormRef.resetFields()
      },
      // 模板导出
      handleExport() {
        exportPerformanceList(this.queryParams).then((response) => {
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
          })
          const a = document.createElement('a')
          const href = window.URL.createObjectURL(blob)
          a.href = href
          const date = new Date().getTime()
          a.setAttribute('download', `履职模板${date}.xlsx`)
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(href)
        })
      },
    },
  }
</script>

<style lang="scss" scoped></style>
