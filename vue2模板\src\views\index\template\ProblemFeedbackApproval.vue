<template>
  <div class="correction-receipt">
    <!-- 问题信息 -->
    <div class="info-section">
      <div class="section-title">问题信息</div>
      <div class="info-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">问题描述：</span>
              <span class="value">
                {{ formData.issueDescription || '' }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">检查设备/场所：</span>
              <span class="value">
                <span v-for="(item, index) in siteList" :key="index">
                  <span
                    v-if="
                      item.id == formData.dutyDetails.inspectedEquipmentOrSite
                    "
                  >
                    {{ item.name }}
                  </span>
                </span>
              </span>
            </div>
            <div class="info-item">
              <span class="label">问题责任人：</span>
              <span class="value">
                {{ formData.rectifierName || '' }}
              </span>
            </div>
            <!-- <div class="info-item">
              <span class="label">整改审核人：</span>
              <span class="value">
                {{ formData.rectificationReviewerName || '' }}
              </span>
            </div> -->
            <div class="info-item">
              <span class="label">问题生成日期：</span>
              <span class="value">
                {{ formData.issueGenerationDate || '' }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">整改期限：</span>
              <span class="value">
                {{ formData.requiredCompletionTime || '' }}
              </span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <div class="label" style="min-width: 120px">现场照片或视频：</div>
              <div class="value">
                <div
                  v-if="
                    formData.onSitePhotosOrVideosAttach.attachInfoList &&
                    formData.onSitePhotosOrVideosAttach.attachInfoList.length
                  "
                  class="image-content"
                >
                  <div
                    v-for="(item, index) in formData.onSitePhotosOrVideosAttach
                      .attachInfoList"
                    :key="index"
                  >
                    <el-image
                      v-if="
                        ['img', 'jpg', 'jpeg', 'png'].includes(item.platform)
                      "
                      lazy
                      :preview-src-list="[item.url]"
                      :src="item.url"
                      style="width: 50%; max-width: 200px"
                    />
                    <el-tag
                      v-else
                      style="cursor: pointer"
                      @click="handlePreview(item.url)"
                    >
                      {{ item.name }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 整改信息 -->
    <div class="correction-section">
      <div class="section-title">整改信息</div>
      <div class="form-content">
        <el-form ref="form" label-width="140px" :model="form" :rules="rules">
          <el-form-item label="整改措施及效果：" prop="rectificationMeasures">
            {{ form.rectificationMeasures }}
          </el-form-item>
          <el-form-item label="整改审核人：" prop="rectificationReviewerId">
            {{ form.rectificationReviewerName }}
          </el-form-item>
          <el-form-item
            label="整改效果附件："
            prop="rectificationEffectAttachmentsAttach"
          >
            <div
              v-if="
                formData.rectificationEffectAttachmentsAttach.attachInfoList &&
                formData.rectificationEffectAttachmentsAttach.attachInfoList
                  .length
              "
              class="image-content"
            >
              <div
                v-for="(item, index) in formData
                  .rectificationEffectAttachmentsAttach.attachInfoList"
                :key="index"
              >
                <el-image
                  v-if="['img', 'jpg', 'jpeg', 'png'].includes(item.platform)"
                  lazy
                  :preview-src-list="[item.url]"
                  :src="item.url"
                  style="width: 50%; max-width: 200px"
                />
                <el-tag
                  v-else
                  style="cursor: pointer"
                  @click="handlePreview(item.url)"
                >
                  {{ item.name }}
                </el-tag>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
  import { getUser } from '@/api/project/projectInfo'
  import { getEquipmentSitesList } from '@/api/system/facilities'

  export default {
    name: 'CorrectionReceipt',
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      formData: {
        type: Object,
        default: () => ({}),
      },
    },
    data() {
      return {
        dialogVisible: false,
        form: {
          measures: '',
          description: '',
          correctionDate: '',
          images: [],
        },
        rules: {
          measures: [
            { required: true, message: '请输入整改措施', trigger: 'blur' },
          ],
          description: [
            { required: true, message: '请输入整改说明', trigger: 'blur' },
          ],
          correctionDate: [
            { required: true, message: '请选择整改日期', trigger: 'change' },
          ],
        },
        uploadedFiles: [],
        imageFiles: [],
        userList: [],
        siteList: [],
      }
    },
    // watch: {
    //   visible(val) {
    //     this.dialogVisible = val
    //     if (val) {
    //       this.initForm()
    //     }
    //   },
    //   formData: {
    //     handler(val) {
    //       if (val && Object.keys(val).length > 0) {
    //         this.initFormData()
    //       }
    //     },
    //     deep: true,
    //   },
    // },
    watch: {
      formData(val) {
        this.form = val
      },
    },
    mounted() {
      this.goSiteList()
      this.getUserList()
    },
    methods: {
      initForm() {
        this.form = {
          measures: '',
          description: '',
          correctionDate: '',
          images: [],
        }
        this.$nextTick(() => {
          this.$refs.form && this.$refs.form.clearValidate()
        })
      },
      getUserList() {
        getUser({
          pageNum: 1,
          pageSize: 9999,
        }).then((res) => {
          this.userList = res.data.list || []
        })
      },
      goSiteList() {
        getEquipmentSitesList({ pageNum: 1, pageSize: 1000 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.siteList = data
          }
        })
      },
      initFormData() {
        if (this.formData.measures) {
          this.form.measures = this.formData.measures
        }
      },
      handlePersonChange(e) {
        const obj = this.userList.find((item) => item.userId === e)
        if (obj) {
          this.form.rectificationReviewerName = obj.nickname
        }
      },
      handlePreview(url) {
        window.open(url, '_blank')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .correction-receipt {
    .section-title {
      padding-left: 10px;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      border-left: 4px solid #409eff;
    }

    .info-section {
      margin-bottom: 20px;

      .info-content {
        padding: 15px;
        background-color: #f8f8f8;
        border-radius: 4px;

        .info-item {
          display: flex;
          margin-bottom: 10px;

          .label {
            max-width: 120px;
            font-weight: 500;
            color: #606266;
          }

          .value {
            color: #303133;

            .evidence-item {
              width: 100%;
              overflow: hidden;
              border: 1px solid #ebeef5;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

              .evidence-content {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 160px;
                font-size: 14px;
                color: #909399;
                background-color: #f5f7fa;
              }

              .evidence-info {
                display: flex;
                justify-content: space-between;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #fff;
                border-top: 1px solid #ebeef5;

                .location {
                  font-weight: 500;
                  color: #606266;
                }

                .time {
                  color: #909399;
                }
              }
            }
          }
        }
      }
    }

    .correction-section {
      margin-bottom: 20px;

      .form-content {
        padding: 15px;
        background-color: #fff;
        border-radius: 4px;
      }
    }
  }
</style>
