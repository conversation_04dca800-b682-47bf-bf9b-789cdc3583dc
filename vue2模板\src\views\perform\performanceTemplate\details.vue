<template>
  <div>
    <el-drawer
      ref="drawer"
      :close-on-click-modal="false"
      custom-class="demo-drawer"
      direction="rtl"
      size="80%"
      :title="title + '模板'"
      :visible.sync="dialogFormVisible"
      @close="close"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form ref="form" label-width="130px" :model="form" :rules="rules">
            <el-form-item label="履职模板名称：" prop="templateName">
              <el-input
                v-model="form.templateName"
                placeholder="请输入"
                style="width: 60%"
              />
            </el-form-item>
            <el-form-item label="岗位：" prop="positionId">
              <el-select
                v-model="form.positionId"
                clearable
                filterable
                placeholder="请选择"
                style="width: 60%"
                @change="changePosition"
              >
                <el-option
                  v-for="item in positionList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="级别：" prop="level">
              <el-input
                v-model="form.level"
                placeholder="请输入"
                readonly
                style="width: 60%"
              />
              <!-- <el-select
                v-model="form.level"
                clearable
                filterable
                placeholder="请选择"
                style="width: 60%"
                @change="changeLevel"
              >
                <el-option
                  v-for="item in levelList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select> -->
            </el-form-item>
          </el-form>
          <div class="detail-section">
            <el-button style="margin: 10px 0" type="primary" @click="handleAdd">
              添 加
            </el-button>
            <el-table border :data="form.dutyTemplatesInfoDtoList">
              <el-table-column
                align="center"
                label="履职检查项"
                prop="dutyInspectionItemId"
                width="400px"
              >
                <template #default="scope">
                  <el-select
                    v-model="scope.row.dutyInspectionItemId"
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                    @focus="setOptionWidth"
                  >
                    <el-option
                      v-for="item in dutyList"
                      :key="item.id"
                      :label="item.dutyInspectionItem"
                      :style="{ width: selectOptionWidth }"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" label="类型" prop="type">
                <template #default="scope">
                  <el-select v-model="scope.row.type" placeholder="请选择">
                    <el-option label="自证履职" :value="0" />
                    <el-option label="扫码履职" :value="1" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" label="工序" prop="process">
                <template #default="scope">
                  <el-select v-model="scope.row.process" placeholder="请选择">
                    <el-option
                      v-for="item in processList"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" label="频率" prop="frequency">
                <template #default="scope">
                  <el-select v-model="scope.row.frequency" placeholder="请选择">
                    <el-option
                      v-for="item in frequencyList"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="频次"
                prop="frequencyCount"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.frequencyCount"
                    min="0"
                    placeholder="请输入"
                    type="number"
                  />
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作" width="100px">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDelete(scope.$index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="drawer-footer">
          <el-button size="medium" @click="close">取 消</el-button>
          <el-button size="medium" type="primary" @click="save">
            提 交
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import { getDictItems } from '@/api/user'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  import {
    addDutyTemplates,
    updateDutyTemplates,
    getDutyTemplatesInfo,
  } from '@/api/perform/performanceTemplate'
  export default {
    name: '',
    data() {
      return {
        form: {
          templateName: '',
          position: null,
          level: null,
          dutyTemplatesInfoDtoList: [],
        },
        content: '',
        rules: {
          templateName: [
            { required: true, trigger: 'blur', message: '标题不能为空' },
          ],
          positionId: [
            { required: true, trigger: 'change', message: '岗位不能为空' },
          ],
          // level: [{ required: true, trigger: 'change', message: '请输入内容' }],
        },
        title: '',
        dialogFormVisible: false,
        dutyList: [],
        processList: [],
        frequencyList: [],
        positionList: [],
        levelList: [],
        selectOptionWidth: '',
      }
    },
    watch: {
      levelList: {
        handler(val) {
          if (val && val.length && this.form.levelId) {
            const obj = val.find((item) => item.id == this.form.levelId)
            if (obj) {
              this.positionList = obj.children || []
            }
          } else if (val && val.length && this.form.level) {
            const obj = val.find((item) => item.name == this.form.level)
            if (obj) {
              this.positionList = obj.children || []
            }
          }
        },
        deep: true,
        immediate: true,
      },
      'form.levelId': {
        handler(val) {
          if (val && this.levelList && this.levelList.length) {
            const obj = this.levelList.find((item) => item.id == val)
            if (obj) {
              this.positionList = obj.children || []
            }
          }
        },
        immediate: true,
      },
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          this.form = row
          this.getDetails(row.id)
        }
        this.dialogFormVisible = true
        this.goDictItems('project_state', 'process')
        this.goDictItems('project_frequency', 'frequency')
        this.goDictItems('project_position', 'position')
        this.getDutyList()
      },
      getDetails(id) {
        getDutyTemplatesInfo(id).then((res) => {
          this.form = res.data
        })
      },
      setOptionWidth(event) {
        this.$nextTick(() => {
          this.selectOptionWidth = event.srcElement.offsetWidth + 'px'
        })
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save() {
        if (this.form.dutyTemplatesInfoDtoList.length === 0) {
          this.$message.error('请添加检查项')
          return
        }
        this.$refs['form'].validate((valid) => {
          if (valid) {
            if (this.form.id) {
              updateDutyTemplates(this.form).then(() => {
                this.$message.success('修改成功')
                this.$emit('refreshDataList')
                this.close()
              })
            } else {
              addDutyTemplates(this.form).then(() => {
                this.$message.success('添加成功')
                this.$emit('refreshDataList')
                this.close()
              })
            }
          } else {
            return false
          }
        })
      },
      handleAdd() {
        this.form.dutyTemplatesInfoDtoList.push({
          dutyInspectionItemId: '',
          type: null,
          process: null,
          frequency: null,
          frequencyCount: null,
        })
      },
      handleDelete(index) {
        this.form.dutyTemplatesInfoDtoList.splice(index, 1)
      },
      // 获取类型字典
      async goDictItems(type, str) {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: type,
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        if (str === 'process') {
          this.processList = res.data.data.list || []
        } else if (str === 'frequency') {
          this.frequencyList = res.data.data.list || []
        } else if (str === 'position') {
          // this.levelList = res.data.data.list || []
          this.calcPosition(res.data.data.list || [])
        }
      },
      async getDutyList() {
        const params = {
          pageNum: 1,
          pageSize: 9999,
        }
        const res = await getDutyInspectionItemsList(params)
        const { code, data } = res

        if (code === 200) {
          this.dutyList = data
        }
      },
      calcPosition(arr) {
        if (arr.length) {
          arr.forEach((item) => {
            if (item.children && item.children.length > 0) {
              item.children.forEach((child) => {
                child.fatherName = item.name
                child.fatherId = item.id
                this.positionList.push(child)
              })
            }
          })
        }
      },
      changeLevel(e) {
        const obj = this.levelList.find((item) => item.name === e)
        if (obj) {
          this.form.levelId = obj.id
          // this.positionList = obj.children || []
        }
      },
      changePosition(e) {
        const obj = this.positionList.find((item) => item.id === e)
        if (obj) {
          this.form.position = obj.name
          this.form.level = obj.fatherName
          this.form.levelId = obj.fatherId
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .drawer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .drawer-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .detail-section {
    margin-top: 20px;
  }

  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    padding: 16px;
    text-align: center;
    background: #fff;
    border-top: 1px solid #e8e8e8;

    .el-button {
      margin-left: 8px;
    }
  }

  // 调整表单样式
  ::v-deep {
    .el-form-item {
      margin-bottom: 22px;

      .el-form-item__label {
        padding-bottom: 8px;
        line-height: 20px;
      }
    }

    .el-table {
      margin-bottom: 60px;
    }
  }
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
