<!-- 项目履职次数统计 -->
<template>
  <div class="projects-container">
    <!-- 统计卡片区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="stat-card" shadow="hover">
          <div class="card-header">
            <h2>自证次数</h2>
          </div>
          <div class="card-body">
            <div class="title">次数</div>
            <div class="num">{{ selfProofCount }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="stat-card" shadow="hover">
          <div class="card-header">
            <h2>扫码次数</h2>
          </div>
          <div class="card-body">
            <div class="title">次数</div>
            <div class="num">{{ codeCount }}</div>
          </div>
        </el-card>
      </el-col>
      <!-- <el-col :span="8">
        <el-card class="stat-card" shadow="hover">
          <div class="card-header">
            <h2>临时检查次数</h2>
          </div>
          <div class="card-body">
            <div class="title">次数</div>
            <div class="num">{{ tempCheckCount }}</div>
          </div>
        </el-card>
      </el-col> -->
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div class="card-header">
            <h2>自证数量</h2>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <self-proof-chart :chart-data="selfProofData" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div class="card-header">
            <h2>打码数量</h2>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <code-chart :chart-data="codeData" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import SelfProofChart from './charts/SelfProofChart.vue'
  import CodeChart from './charts/CodeChart.vue'
  import {
    getAdminProjectsStatistics,
    getUserProjectsStatistics,
  } from '@/api/statistic/projects'

  export default {
    name: 'ProjectsStatistic',
    components: {
      SelfProofChart,
      CodeChart,
    },
    data() {
      return {
        // 统计数据
        selfProofCount: 0,
        codeCount: 0,
        tempCheckCount: 0,

        // 查询参数
        queryParams: {
          projectId: '',
          dateRange: [],
        },

        // 项目列表
        projectList: [],

        // 图表数据
        selfProofData: [],
        codeData: [],
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        userInfo: (state) => state.user.user,
      }),
    },
    created() {
      this.getProjectList()
      this.goProjectsStatistics()
    },
    methods: {
      // 获取项目列表
      getProjectList() {
        // 这里应该调用API获取项目列表
        this.projectList = [
          { id: 1, projectName: '基地川西北德格阳北地区三维地震勘探项目' },
          { id: 2, projectName: '塔里木盆地车辆加油点建设项目' },
          { id: 3, projectName: '四川地震勘探项目' },
        ]
      },

      goProjectsStatistics() {
        let params = {
          start_time: new Date().getFullYear() + '-01-01' + ' 00:00:00',
          end_time: new Date().getFullYear() + '-12-31' + ' 23:59:59',
        }
        const { roles } = this.userInfo
        const isAdmin = roles.includes('root', 'admin')
        if (isAdmin) {
          getAdminProjectsStatistics(params).then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.selfProofCount = data.self_perform_num
              this.codeCount = data.scan_perform_num
              if (data.self_perform_pro_num_map) {
                this.selfProofData = Object.entries(
                  data.self_perform_pro_num_map
                ).map(([name, value]) => ({
                  name: name,
                  value: value,
                }))
              }
              if (data.scan_perform_pro_num_map) {
                this.codeData = Object.entries(
                  data.scan_perform_pro_num_map
                ).map(([name, value]) => ({
                  name: name,
                  value: value,
                }))
              }
            }
          })
        } else {
          if (!this.storeProjectId) return
          params.project_id = this.storeProjectId
          getUserProjectsStatistics(params).then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.selfProofCount = data.self_perform_num
              this.codeCount = data.scan_perform_num
              if (data.self_perform_date_num_map) {
                this.selfProofData = Object.entries(
                  data.self_perform_date_num_map
                ).map(([name, value]) => ({
                  name: name,
                  value: value,
                }))
              }
              if (data.scan_perform_date_num_map) {
                this.codeData = Object.entries(
                  data.scan_perform_date_num_map
                ).map(([name, value]) => ({
                  name: name,
                  value: value,
                }))
              }
            }
          })
        }
      },

      // 查询数据
      handleQuery() {
        // 这里应该调用API获取统计数据
        this.$message.success('查询成功')
      },

      // 重置查询条件
      resetQuery() {
        this.queryParams = {
          projectId: '',
          dateRange: [],
        }
        this.handleQuery()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .projects-container {
    padding: 20px;

    .stat-card {
      margin-bottom: 20px;
      border-radius: 8px;

      .card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;

        h2 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .card-body {
        padding: 20px;

        .title {
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: 600;
          text-align: center;
        }

        .num {
          font-size: 36px;
          font-weight: bold;
          color: #333;
          text-align: center;
        }
      }
    }

    .chart-card {
      margin-bottom: 20px;
      border-radius: 8px;

      .card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;

        h2 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .card-body {
        padding: 20px;

        .chart-container {
          height: 400px;
        }
      }
    }

    .query-form {
      padding: 20px;
      background-color: #f5f7fa;
      border-radius: 8px;
    }
  }
</style>
