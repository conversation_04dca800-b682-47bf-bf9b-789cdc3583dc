import request from '@/utils/request'

// 获取列表
export function getProjectStatusLogs(params) {
  return request({
    url: '/perform-duties-service/project/projectStatusLogs/list',
    method: 'GET',
    params,
  })
}

// 创建数据
export function creatProjectStatusLogs(data) {
  return request({
    url: '/perform-duties-service/project/projectStatusLogs',
    method: 'post',
    data,
  })
}

// 根据id获取数据
export function getProjectStatusLogsById(id) {
  return request({
    url: `/perform-duties-service/project/projectStatusLogs/${id}`,
    method: 'get',
  })
}

// 修改数据
export function updateProjectStatusLogs(data) {
  return request({
    url: `/perform-duties-service/project/projectStatusLogs/update`,
    method: 'put',
    data,
  })
}

//删除对象组
export function delProjectStatusLogs(params) {
  return request({
    url: '/perform-duties-service/project/projectStatusLogs/delete',
    method: 'delete',
    params,
  })
}
