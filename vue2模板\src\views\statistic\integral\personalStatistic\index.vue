<!-- 积分统计 -->
<template>
  <div class="app-container">
    <!-- 积分概览卡片 -->
    <el-card class="score-overview" shadow="hover">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="score-item current-score">
            <div class="score-value">{{ currentScore }}</div>
            <div class="score-label">当前总积分</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="score-item increase-score">
            <div class="score-value">+{{ increaseScore }}</div>
            <div class="score-label">累计加分</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="score-item decrease-score">
            <div class="score-value">{{ decreaseScore }}</div>
            <div class="score-label">累计扣分</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 积分变动趋势图 -->
    <el-card class="score-chart" shadow="hover">
      <div slot="header" class="clearfix">
        <span>积分变动趋势图</span>
        <el-date-picker
          v-model="dateRange"
          align="right"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          style="float: right; width: 320px"
          type="daterange"
          unlink-panels
          value-format="yyyy-MM-dd"
          @change="handleDateChange"
        />
      </div>
      <div ref="scoreChart" class="chart-container"></div>
    </el-card>

    <!-- 积分变动明细 -->
    <el-card class="score-details" shadow="hover">
      <div slot="header" class="clearfix">
        <span>积分变动明细</span>
      </div>
      <el-table
        v-loading="loading"
        border
        :data="scoreRecords"
        style="width: 100%"
      >
        <el-table-column align="center" label="日期" prop="date" width="120" />
        <el-table-column
          align="center"
          label="项目"
          prop="project"
          width="300"
        />
        <el-table-column align="center" label="事件" prop="event" />
        <el-table-column align="center" label="变动" prop="change" width="100">
          <template slot-scope="scope">
            <span :class="scope.row.change > 0 ? 'increase' : 'decrease'">
              {{ scope.row.change }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="积分" prop="score" width="100" />
      </el-table>

      <!-- 分页 -->
      <el-pagination
        class="pagination"
        :current-page="currentPage"
        layout="total, sizes, prev, pager, next, jumper"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </el-card>
  </div>
</template>

<script>
  import * as echarts from 'echarts'
  import { mapGetters } from 'vuex'
  import { getUserIntegralStatistics } from '@/api/statistic/statistic'
  import moment from 'moment'

  export default {
    name: 'PersonalScoreStatistic',
    data() {
      return {
        loading: false,
        currentScore: 965,
        increaseScore: 1,
        decreaseScore: -36,
        dateRange: [
          new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000),
          new Date(),
        ],
        pickerOptions: {
          shortcuts: [
            {
              text: '最近一周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '最近一个月',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '最近三个月',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                picker.$emit('pick', [start, end])
              },
            },
          ],
        },
        scoreChart: null,
        // 原始数据存储
        allScoreRecords: [],
        // 当前页显示的数据
        scoreRecords: [],
        currentPage: 1,
        pageSize: 10,
        total: 0,
        // 修改：动态生成的图表数据
        chartData: {
          dates: [],
          scores: [],
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        projectId: 'user/projectId',
      }),
    },
    mounted() {
      this.fetchScoreData()
      this.initChart()
    },
    beforeDestroy() {
      if (this.scoreChart) {
        this.scoreChart.dispose()
        this.scoreChart = null
      }
      window.removeEventListener('resize', this.resizeChart)
    },
    methods: {
      goUserProjectPoints() {
        let params = {
          start_time: new Date().getFullYear() + '-01-01' + ' 00:00:00',
          end_time: new Date().getFullYear() + '-12-31' + ' 23:59:59',
        }
        if (this.dateRange && this.dateRange.length === 2) {
          params.start_time =
            moment(this.dateRange[0]).format('YYYY/MM/DD') + '00:00:00'
          params.end_time =
            moment(this.dateRange[1]).format('YYYY/MM/DD') + '23:59:59'
        }
        getUserIntegralStatistics(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.currentScore = data.score_sum
            this.increaseScore = data.score_bonus
            this.decreaseScore = data.score_minus

            // 处理event_list数据，将reason_list展开为列表
            this.processEventListData(data.event_list || [])
          }
        })
      },

      processEventListData(eventList) {
        const processedData = []

        // 按日期倒序排列，最新的在前面
        const sortedEvents = eventList.sort(
          (a, b) => new Date(b.date) - new Date(a.date)
        )

        sortedEvents.forEach((event) => {
          const { date, reason_list } = event

          reason_list.forEach((reason) => {
            processedData.push({
              date: date,
              project: reason.project_name,
              event: reason.reason,
              change: reason.change_score,
              score: reason.current_score,
              change_type: reason.change_type,
              is_appeal: reason.is_appeal,
              issue_id: reason.issue_id,
              user_duty_list_info_id: reason.user_duty_list_info_id,
              user_duty_detail_id: reason.user_duty_detail_id,
            })
          })
        })

        // 存储所有数据
        this.allScoreRecords = processedData
        this.total = processedData.length

        // 生成图表数据
        this.generateChartData(eventList)

        // 更新当前页数据
        this.updateCurrentPageData()
      },

      generateChartData(eventList) {
        if (!eventList || eventList.length === 0) {
          this.chartData = { dates: [], scores: [] }
          return
        }

        const dailyScoreMap = new Map()

        const sortedEvents = eventList.sort(
          (a, b) => new Date(b.date) - new Date(a.date)
        )

        sortedEvents.forEach((event) => {
          const { date, reason_list } = event

          if (!dailyScoreMap.has(date) && reason_list.length > 0) {
            const lastReason = reason_list[reason_list.length - 1]
            dailyScoreMap.set(date, lastReason.current_score)
          }
        })

        // 按日期排序（从早到晚）
        const sortedDates = Array.from(dailyScoreMap.keys()).sort(
          (a, b) => new Date(a) - new Date(b)
        )

        const dates = []
        const scores = []

        sortedDates.forEach((date) => {
          const formattedDate = this.formatDateForChart(date)
          dates.push(formattedDate)

          scores.push(dailyScoreMap.get(date))
        })

        this.chartData = {
          dates: dates,
          scores: scores,
        }

        // 更新图表
        if (this.scoreChart) {
          console.log(this.chartData)
          this.scoreChart.clear()
          this.setChartOption()
        }
      },

      formatDateForChart(dateStr) {
        const date = new Date(dateStr)
        const month = date.getMonth() + 1
        const day = date.getDate()
        return `${month}月${day}日`
      },

      updateCurrentPageData() {
        const startIndex = (this.currentPage - 1) * this.pageSize
        const endIndex = startIndex + this.pageSize
        this.scoreRecords = this.allScoreRecords.slice(startIndex, endIndex)
      },

      initChart() {
        this.$nextTick(() => {
          if (this.scoreChart) {
            this.scoreChart.dispose()
          }
          this.scoreChart = echarts.init(this.$refs.scoreChart)
          window.addEventListener('resize', this.resizeChart)
          this.setChartOption()
        })
      },
      setChartOption() {
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              const data = params[0]
              return `${data.name}<br/>积分: ${data.value}`
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.chartData.dates,
            axisLabel: {
              rotate: this.chartData.dates.length > 10 ? 45 : 0,
            },
          },
          yAxis: {
            type: 'value',
            min: function (value) {
              return Math.floor(value.min * 0.95)
            },
          },
          series: [
            {
              name: '积分',
              type: 'line',
              data: this.chartData.scores,
              itemStyle: {
                color: '#409EFF',
              },
              lineStyle: {
                width: 2,
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(64, 158, 255, 0.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(64, 158, 255, 0.1)',
                    },
                  ],
                },
              },
            },
          ],
        }
        this.scoreChart.setOption(option)
      },
      resizeChart() {
        if (this.scoreChart) {
          this.scoreChart.resize()
        }
      },
      fetchScoreData() {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.goUserProjectPoints()
        }, 500)
      },
      handleDateChange() {
        this.fetchScoreData()
      },
      // 修改：实现本地分页的页码变更
      handleSizeChange(val) {
        this.pageSize = val
        this.currentPage = 1 // 重置到第一页
        this.updateCurrentPageData()
      },
      // 修改：实现本地分页的页面切换
      handleCurrentChange(val) {
        this.currentPage = val
        this.updateCurrentPageData()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 20px;
  }

  .score-overview {
    margin-bottom: 20px;

    .score-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px 0;

      .score-value {
        margin-bottom: 10px;
        font-size: 36px;
        font-weight: bold;
      }

      .score-label {
        font-size: 14px;
        color: #606266;
      }
    }

    .current-score .score-value {
      color: #409eff;
    }

    .increase-score .score-value {
      color: #67c23a;
    }

    .decrease-score .score-value {
      color: #f56c6c;
    }
  }

  .score-chart {
    margin-bottom: 20px;

    .chart-container {
      width: 100%;
      height: 400px;
    }
  }

  .score-details {
    .increase {
      color: #67c23a;
    }

    .decrease {
      color: #f56c6c;
    }

    .pagination {
      margin-top: 20px;
      text-align: right;
    }
  }

  .clearfix {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
