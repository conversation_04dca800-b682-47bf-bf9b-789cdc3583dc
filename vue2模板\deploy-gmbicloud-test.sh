#!/usr/bin/env bash
set -e
npm run build-gmbicloud-test
docker build -f dockerfile_gmbicloud_test . -t harbor.gmbicloud.com/library/perform-duties-gmbicloud-test:0.0.1 --platform linux/x86_64
docker push harbor.gmbicloud.com/library/perform-duties-gmbicloud-test:0.0.1
curl -X PUT \
    -H "Content-Type: application/yaml" \
    -H "Cookie: KuboardUsername=admin; KuboardAccessKey=irs5pe3nc3zz.c2wkrye6decyb7icey2phwm866knhkhn" \
    -d '{"kind":"deployments","namespace":"swisp","name":"duty-web"}' \
    "https://k3s-test.gmbicloud.com/kuboard-api/cluster/default/kind/CICDApi/admin/resource/restartWorkload"
