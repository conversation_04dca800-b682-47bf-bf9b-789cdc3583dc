import request from '@/utils/request'
import { getToken } from '@/utils/token'

// 创建数据
export function addDutyTemplates(data) {
  return request({
    url: '/perform-duties-service/perform/dutyTemplates',
    method: 'post',
    data,
  })
}

// 新增履职详情数据(履职详情单条)
export function createDutyTemplatesInfo(data) {
  return request({
    url: '/perform-duties-service/perform/dutyTemplates/createDutyTemplatesInfo',
    method: 'post',
    data,
  })
}

// 修改履职模板数据(只修改主表)
export function updateDutyTemplates(data) {
  return request({
    url: '/perform-duties-service/perform/dutyTemplates/update',
    method: 'put',
    data,
  })
}

// 查询详情
export function getDutyTemplatesInfo(id) {
  return request({
    url: `/perform-duties-service/perform/dutyTemplates/${id}`,
    method: 'get',
  })
}

// 修改履职模板数据(履职详情单条)
export function updateDutyTemplatesInfo(data) {
  return request({
    url: '/perform-duties-service/perform/dutyTemplates/updateDutyTemplatesInfo',
    method: 'put',
    data,
  })
}

// 分页获取列表
export function getDutyTemplatesList(params) {
  return request({
    url: '/perform-duties-service/perform/dutyTemplates/list',
    method: 'get',
    params,
  })
}

//删除对象组
export function delDutyTemplates(params) {
  return request({
    url: '/perform-duties-service/perform/dutyTemplates/delete',
    method: 'delete',
    params,
  })
}

// 删除履职详情数据(履职详情单条)
export function delDutyTemplatesInfo(params) {
  return request({
    url: '/perform-duties-service/perform/dutyTemplates/deleteDutyTemplatesInfo',
    method: 'delete',
    params,
  })
}

// 导入履职模板
export function importPerformance(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/perform-duties-service/perform/dutyTemplates/dutyTemplateImport',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `${getToken()}`,
    },
  })
}

// 模板导出

export function exportPerformance(params) {
  return request({
    url: '/perform-duties-service/perform/dutyTemplates/downloadExcelTemplate',
    method: 'get',
    params,
    responseType: 'blob',
  })
}

// 导出列表数据
export function exportPerformanceList(params) {
  return request({
    url: '/perform-duties-service/perform/dutyTemplates/dutyTemplateExport',
    method: 'get',
    params,
    responseType: 'blob',
  })
}
