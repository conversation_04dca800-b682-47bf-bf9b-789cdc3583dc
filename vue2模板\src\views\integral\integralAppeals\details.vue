<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '分数申诉'"
      :visible.sync="dialogFormVisible"
      width="1000px"
      @close="close"
    >
      <el-form
        ref="form"
        label-position="top"
        label-width="130px"
        :model="form"
        :rules="rules"
      >
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间：" prop="startDate">
              <el-date-picker
                v-model="form.startDate"
                clearable
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期"
                style="width: 96%"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发起人：" prop="safetyResponsiblePersonArr">
              <el-select
                v-model="form.safetyResponsiblePersonArr"
                clearable
                filterable
                placeholder="请选择"
                style="width: 96%"
                @change="
                  (val) => handlePersonChange(val, 'safetyResponsiblePerson')
                "
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="明细表" prop="riskResponsibilityDtoList">
              <template slot="label">
                <span style="color: red">*</span>
                <span style="margin-left: 5px">明细表</span>
              </template>
              <div class="btn" style="margin-bottom: 10px">
                <el-button size="mini" type="primary" @click="addList">
                  新增
                </el-button>
                <!-- <el-dropdown
                  split-button
                  style="margin: 0px 10px"
                  @command="handleDropdown"
                >
                  导入
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        command="handleDownloadTemplate"
                        icon="el-icon-download"
                      >
                        下载模板
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="showImportDialog"
                        icon="el-icon-top"
                      >
                        导入数据
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown> -->
              </div>
              <el-table border :data="form.scoreRecordData" style="width: 98%">
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column label="选择项" prop="id">
                  <template #default="scope">
                    <el-select
                      v-model="scope.row.id"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                      @change="(val) => handleRiskNameChange(val, scope.row)"
                    >
                      <el-option
                        v-for="item in integralDetailList"
                        :key="item.id"
                        :label="item.label"
                        :value="item.id"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="分值" prop="score">
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.score"
                      disabled
                      placeholder="自动带出"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="说明" prop="describe">
                  <template #default="scope">
                    <el-input
                      v-model="scope.row.describe"
                      placeholder="请输入"
                    />
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="50">
                  <template #default="scope">
                    <el-button
                      circle
                      icon="el-icon-delete"
                      size="mini"
                      type="danger"
                      @click="handleDelete(scope.$index)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="累计分值：">
              <div
                style="
                  width: 98%;
                  padding: 10px;
                  background-color: #eee;
                  border-radius: 5px;
                "
              >
                {{ totalScore }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="说明：" prop="describe">
              <el-input
                v-model="form.describe"
                placeholder="请输入"
                resize="none"
                :rows="4"
                style="width: 98%"
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="审批领导选择：" prop="approverId">
              <el-select
                v-model="form.approverId"
                clearable
                filterable
                placeholder="请选择"
                style="width: 98%"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save(true)">暂 存</el-button>
        <el-button type="primary" @click="save(false)">确 定</el-button>
      </template>

      <!-- 导入 -->
      <el-dialog
        append-to-body
        :close-on-click-modal="false"
        :title="importDialog.title"
        :visible.sync="importDialog.visible"
        width="600px"
        @close="closeImportDialog"
      >
        <el-form ref="importFormRef" label-width="80px" :model="importFormData">
          <el-form-item label="Excel">
            <el-upload
              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              action=""
              :auto-upload="false"
              class="upload-demo"
              drag
              :file-list="excelFilelist"
              :limit="1"
              :on-change="handleExcelChange"
            >
              <el-icon class="el-icon--upload">
                <upload-filled />
              </el-icon>
              <div class="el-upload__text">
                <em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">xls/xlsx 文件</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitImportForm">
              确 定
            </el-button>
            <el-button @click="closeImportDialog">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import { getBaseProjectList, getImportExcel } from '@/api/project/projectInfo'
  import {
    getPointsDetails,
    createAppeal,
    updateAppeal,
    getAppealDetail,
  } from '@/api/integral/integralList'
  export default {
    name: '',
    data() {
      return {
        form: {
          scoreRecordData: [],
        },
        content: '',
        rules: {
          safetyResponsiblePersonArr: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          projectAdminArr: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          startDate: [{ required: true, trigger: 'change', message: '请选择' }],
          describe: [{ required: true, trigger: 'blur', message: '请输入' }],
          approverId: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
        },
        title: '',
        dialogFormVisible: false,
        baseProjectList: [],
        importFormData: {},
        // 导入
        importDialog: {
          title: '项目重点生产安全风险防控责任清单导入',
          visible: false,
        },
        totalScore: 0,
        integralDetailList: [], // 积分明细列表数据
      }
    },
    computed: {
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
    },
    watch: {
      'form.scoreRecordData': {
        handler(newVal) {
          if (newVal && newVal.length > 0) {
            this.totalScore = newVal.reduce((total, item) => {
              return total + (item.score * 1 || 0)
            }, 0)
          } else {
            this.totalScore = 0
          }
        },
        deep: true,
      },
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          // this.form = row
          this.getDetails(row.id)
        }
        this.getBaseProjectList()
        this.getIntegralDetailList()
        this.dialogFormVisible = true
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
      },
      async getDetails(id) {
        try {
          const res = await getAppealDetail({ id })
          if (!res.data) return
          this.form = res.data
          this.form.approverId = this.form.approverId
            ? this.form.approverId
            : res.data?.firstApproval?.userid
        } catch (error) {
          this.$message.error('获取详情失败')
        }
      },
      async getIntegralDetailList() {
        const params = {
          pageNum: 1,
          pageSize: 9999,
        }
        const { code, data } = await getPointsDetails(params)

        if (code === 200) {
          if (data.list && data.list.length > 0) {
            data.list.forEach((item) => {
              item.label =
                item.username +
                '-' +
                item.scoreCreateDate +
                '-' +
                item.departmentName
            })
          }
          this.integralDetailList = data.list
        }
      },
      handleRiskNameChange(val, row) {
        const detail = this.integralDetailList.find((item) => item.id === val)
        if (detail) {
          row.score = detail.score
        } else {
          row.score = ''
        }
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save(type) {
        if (type) {
          const form = {
            ...this.form,
            tempStorage: type,
          }

          if (form.id) {
            updateAppeal(form).then(() => {
              this.$emit('refreshDataList')
              this.close()
            })
          } else {
            createAppeal(form).then(() => {
              this.$emit('refreshDataList')
              this.close()
            })
          }
        } else {
          this.$refs['form'].validate((valid) => {
            if (valid) {
              if (this.form.scoreRecordData.length === 0) {
                this.$message.warning('请添加明细')
                return
              }
              const form = {
                ...this.form,
                tempStorage: type,
              }

              if (form.id) {
                updateAppeal(form).then(() => {
                  this.$emit('refreshDataList')
                  this.close()
                })
              } else {
                createAppeal(form).then(() => {
                  this.$emit('refreshDataList')
                  this.close()
                })
              }
            } else {
              return false
            }
          })
        }
      },
      addList() {
        this.form.scoreRecordData.push({
          id: '',
          score: '',
          describe: '',
        })
      },
      handleDelete(index) {
        this.form.scoreRecordData.splice(index, 1)
      },
      delList() {
        const selection = this.$refs.table.selection
        selection.forEach((item) => {
          const index = this.form.scoreRecordData.indexOf(item)
          if (index > -1) {
            this.form.scoreRecordData.splice(index, 1)
          }
        })
      },
      changeProject(e) {
        const obj = this.baseProjectList.find((item) => item.projectCode === e)
        if (obj) {
          this.form.projectName = obj.projectName
        }
      },
      getBaseProjectList() {
        getBaseProjectList({
          pageNum: 1,
          pageSize: 1000,
        }).then((res) => {
          this.baseProjectList = res.data.list || []
        })
      },
      /**
       * 导入下拉框
       */
      handleDropdown(command) {
        if (command == 'handleDownloadTemplate') {
          this.handleDownloadTemplate()
        }
        if (command == 'showImportDialog') {
          this.showImportDialog()
        }
      },
      /**
       * 下载导入模板
       */
      handleDownloadTemplate() {
        fetch(
          '/perform-duties-service/project/projectRiskResponsibility/exportTemplate',
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          }
        )
          .then((response) => {
            if (!response.ok) {
              throw new Error('网络响应异常')
            }
            return response.blob()
          })
          .then((blob) => {
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.download = '项目重点生产安全风险防控责任清单导入模板.xlsx'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
          })
          .catch((error) => {
            console.error('下载模板失败:', error)
            this.$message.error('下载模板失败')
          })
      },

      /**
       * 导入表单弹窗
       */
      async showImportDialog() {
        this.importDialog.visible = true
        this.$set(this.importFormData, 'clientId', this.clientId)
      },
      /**
       * Excel文件change事件
       *
       * @param file
       */
      handleExcelChange(file) {
        if (!/\.(xlsx|xls|XLSX|XLS)$/.test(file.name)) {
          this.$baseMessage('上传Excel只能为xlsx、xls格式', 'warning')
          this.excelFile = undefined
          this.excelFilelist = []
          return false
        }
        this.excelFile = file.raw
      },

      /**
       * Excel文件上传
       */
      submitImportForm() {
        this.$refs.importFormRef.validate((valid) => {
          if (valid) {
            if (!this.excelFile) {
              this.$baseMessage('上传Excel文件不能为空', 'warning')
              return false
            }
            this.messageBox = this.$loading({
              lock: true,
              text: '上传中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            const clientId = this.importFormData.clientId
            getImportExcel(this.excelFile, clientId, this.dictCode)
              .then((response) => {
                this.messageBox.close()
                if (response.data == null) response.data = []
                this.errTitle = response.msg
                this.errData = response.data
                if (response.data.length > 0) {
                  this.innerVisible = true
                } else {
                  this.$alert(response.msg, '提示', {
                    confirmButtonText: '确定',
                    type: 'success',
                    callback: () => {
                      this.closeImportDialog()
                      this.handleQuery({ clientId: this.clientId })
                    },
                  })
                }
              })
              .catch((err) => {
                this.messageBox.close()
                if (err.data == null) err.data = []
                this.errTitle = err.msg
                this.errData = err.data
                if (err.data.length) {
                  this.innerVisible = true
                } else {
                  this.$alert(err.msg, '提示', {
                    confirmButtonText: '确定',
                    type: 'error',
                    callback: () => {
                      this.closeImportDialog()
                      this.handleQuery({ clientId: this.clientId })
                    },
                  })
                }
              })
          }
        })
      },
      closeInnerVisible() {
        this.errTitle = ''
        this.errData = {}
        this.innerVisible = false
        this.closeImportDialog()
        this.handleQuery({ clientId: this.clientId })
      },
      /**
       * 关闭导入弹窗
       */
      closeImportDialog() {
        this.importDialog.visible = false
        this.excelFile = undefined
        this.excelFilelist = []
        this.$refs.importFormRef.resetFields()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
