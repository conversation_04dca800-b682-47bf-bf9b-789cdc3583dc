<!-- 积分申诉 -->
<template>
  <div class="integralList-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item>
        <el-button icon="el-icon-plus" type="success" @click="handleAdd">
          新增
        </el-button>
        <el-button
          :disabled="!selectedIds.length"
          icon="el-icon-delete"
          type="danger"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" prop="id" />
      <el-table-column align="center" label="申诉人" prop="creator" />
      <el-table-column align="center" label="申诉总积分" prop="totalScore" />
      <el-table-column align="center" label="申诉说明" prop="describe" />
      <el-table-column align="center" label="创建日期" prop="createDate" />
      <el-table-column align="center" label="审批人" prop="approverName" />
      <el-table-column align="center" label="审批状态" prop="status">
        <template #default="{ row }">
          <el-tag v-if="row.status === 1" type="info">草稿</el-tag>
          <el-tag v-if="row.status === 2">审批中</el-tag>
          <el-tag v-if="row.status === 3" type="success">已通过</el-tag>
          <el-tag v-if="row.status === 4" type="danger">已驳回</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="140">
        <template #default="{ row }">
          <el-button
            circle
            icon="el-icon-edit"
            plain
            type="primary"
            @click="handleUpdate(row)"
          />
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="total"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <Details ref="details" @refreshDataList="getList" />
  </div>
</template>
<script>
  import { mapGetters } from 'vuex'
  import {
    getAppealRecord,
    deleteAppealRecord,
  } from '@/api/integral/integralList'
  import Details from './details.vue'
  export default {
    name: 'IntegralList',
    components: {
      Details,
    },

    data() {
      return {
        loading: false,
        selectedIds: [],
        total: 0,
        tableData: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          projectId: undefined,
          userId: undefined,
        },
      }
    },

    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
    },

    mounted() {
      // this.queryParams.userId = this.userId
      // this.queryParams.projectId = this.storeProjectId
      this.getList()
    },

    methods: {
      async getList() {
        // if (!this.storeProjectId) return

        this.loading = true
        try {
          const { code, data } = await getAppealRecord(this.queryParams)

          if (code === 200) {
            this.tableData = data.list
            this.total = data.total
          }
        } catch (error) {
          console.error('Failed to fetch points:', error)
        } finally {
          this.loading = false
        }
      },

      handleSelectionChange(selection) {
        this.selectedIds = selection.map((item) => item.id)
      },

      async handleDelete(row) {
        const ids = row ? [row.id] : this.selectedIds
        if (!ids.length) {
          this.$message.warning('请选择要删除的记录')
          return
        }

        try {
          await this.$confirm('确认删除所选记录?', '提示', {
            type: 'warning',
          })

          await deleteAppealRecord({ id: ids })
          this.$message.success('删除成功')
          this.getList()
        } catch (error) {
          // User canceled or API error
        }
      },

      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.getList()
      },

      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.getList()
      },

      handleDropdown() {
        // Implement dropdown actions
      },

      handleDownloadTemplate() {
        // Implement template download
      },

      showImportDialog() {
        // Implement import dialog
      },

      handleExport() {
        // Implement export
      },

      handleAdd() {
        this.$refs.details.showEdit()
      },
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
    },
  }
</script>

<style scoped>
  .ml-3 {
    margin-left: 12px;
  }
</style>
