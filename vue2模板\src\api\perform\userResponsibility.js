import request from '@/utils/request'

// 用户自证履职
export function selfCertificationOfDuties(data) {
  return request({
    url: '/perform-duties-service/userDutyDetails/selfCertificationOfDuties',
    method: 'post',
    data,
  })
}

// 用户自证履职
export function scanCodeOfDuties(data) {
  return request({
    url: '/perform-duties-service/userDutyDetails/scanCodeOfDuties',
    method: 'post',
    data,
  })
}

// 根据ID获取
export function getUserDutyDetailsById(id) {
  return request({
    url: `/perform-duties-service/userDutyDetails/${id}`,
    method: 'get',
  })
}

// 根据ID获取暂存详情
export function getUserDutyDetailsTempById(id) {
  return request({
    url: `/perform-duties-service/userDutyDetails/queryTemporaryStorageData/${id}`,
    method: 'get',
  })
}
