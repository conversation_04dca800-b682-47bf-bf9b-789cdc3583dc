<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '自证履职检查单'"
      :visible.sync="dialogFormVisible"
      width="1000px"
      @close="close"
    >
      <el-form ref="form" label-width="140px" :model="form" :rules="rules">
        <el-row>
          <el-col :span="12">
            <el-form-item label="人员：" prop="userId">
              <el-select
                v-model="form.userId"
                disabled
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称：" prop="projectId">
              <el-select
                v-model="form.projectId"
                disabled
                placeholder="请选择"
                style="width: 96%"
                @change="changeResponItem"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.id"
                  :label="item.projectName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="岗位：" prop="positionName">
              <el-input
                v-model="form.positionName"
                placeholder="请输入"
                readonly
                style="width: 96%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="级别：" prop="level">
              <el-input
                v-model="form.level"
                placeholder="请输入"
                readonly
                style="width: 96%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="履职检查项：" prop="templatesInfoId">
              <el-select
                v-model="form.templatesInfoId"
                disabled
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in dutyArray"
                  :key="item.id"
                  :label="item.dutyInspectionItem"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="履职日期：" prop="dutyDate">
              <el-date-picker
                v-model="form.dutyDate"
                placeholder="选择履职日期时间"
                readonly
                style="width: 96%"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查类型：" prop="inspectionType">
              <el-select
                v-model="form.inspectionType"
                clearable
                filterable
                placeholder="请选择检查类型"
                style="width: 96%"
                @change="changeDutyTemplate"
              >
                <el-option
                  v-for="item in safeDictList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审批人：" prop="approverId">
              <el-select
                v-model="form.approverId"
                clearable
                filterable
                placeholder="请选择"
                style="width: 96%"
                @change="handlePersonChange"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item class="logo" label="图片：" prop="images">
              <image-uploader
                ref="imageUploader"
                v-model="imageFiles"
                :limit="10"
                :multiple="true"
                @on-remove="handleImageRemove"
                @on-success="handleImageSuccess"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="logo" label="附件：" prop="attachments">
              <file-uploader
                ref="fileUploader"
                v-model="uploadedFiles"
                @on-remove="handleFileRemove"
                @on-success="handleFileSuccess"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="完成情况描述：" prop="completionDescription">
          <el-input
            v-model="form.completionDescription"
            :rows="4"
            style="width: 98%"
            type="textarea"
          />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否合格：" prop="qualified">
              <el-radio-group v-model="form.qualified">
                <el-radio :label="true">合格</el-radio>
                <el-radio :label="false">不合格</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="form.qualified === false" :span="12">
            <el-form-item label="要求整改时间：" prop="requiredCompletionTime">
              <el-date-picker
                v-model="form.requiredCompletionTime"
                placeholder="选择日期时间"
                style="width: 96%"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.qualified === false">
          <el-col :span="12">
            <el-form-item label="检查问题描述：" prop="issueDescription">
              <el-input v-model="form.issueDescription" style="width: 96%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改人：" prop="rectifierId">
              <el-select
                v-model="form.rectifierId"
                clearable
                filterable
                placeholder="请选择"
                style="width: 96%"
                @change="handlePersonChange2"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.qualified === false">
          <el-col :span="12">
            <el-form-item
              label="隐患违章问题："
              prop="hiddenDangersViolationsNum"
            >
              <el-select
                v-model="form.hiddenDangersViolationsNum"
                clearable
                filterable
                placeholder="请选择检查类型"
                style="width: 96%"
                @change="changeHiddenDanger"
              >
                <el-option
                  v-for="item in hiddenList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="因素级别：" prop="facotrLevel">
              <el-select
                v-model="form.facotrLevel"
                clearable
                disabled
                filterable
                placeholder="请选择检查类型"
                style="width: 96%"
                @change="changeFactorLevel"
              >
                <el-option
                  v-for="item in factorsList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.qualified === false">
          <el-col :span="24">
            <el-form-item label="具体描述：" prop="specificDesc">
              <el-input
                v-model="form.specificDesc"
                :rows="4"
                style="width: 98%"
                type="textarea"
              />
              <div>
                ，违反了《东方地球物理公司HSE违章行为管理规定》第&nbsp;{{
                  form.hiddenDangersViolationsNum || ' '
                }}&nbsp;条
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.qualified === false">
          <el-col :span="12">
            <el-form-item
              class="logo"
              label="现场照片或视频："
              prop="onSitePhotosOrVideos"
            >
              <file-uploader
                ref="fileUploader"
                v-model="uploadedFiles2"
                @on-remove="handleFileRemove2"
                @on-success="handleFileSuccess2"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" />
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save(true)">暂 存</el-button>
        <el-button type="primary" @click="save(false)">提 交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { baseURL, buketName } from '@/config'
  import { getToken } from '@/utils/token'
  import { getDictItems } from '@/api/user'
  import { getProjectList } from '@/api/project/projectInfo'
  import {
    getDutyTemplatesList,
    getDutyTemplatesInfo,
  } from '@/api/perform/performanceTemplate'
  import {
    selfCertificationOfDuties,
    getUserDutyDetailsTempById,
  } from '@/api/perform/userResponsibility'
  import moment from 'moment'
  import FileUploader from '@/components/FileUploader'
  import ImageUploader from '@/components/ImageUploader'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  export default {
    name: '',
    components: {
      FileUploader,
      ImageUploader,
    },
    props: {
      dutyList: {
        type: Array,
        default: () => [],
      },
      processList: {
        type: Array,
        default: () => [],
      },
      frequencyList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {},
        content: '',
        rules: {
          userId: [{ required: true, trigger: 'change', message: '请选择' }],
          projectId: [{ required: true, trigger: 'change', message: '请选择' }],
          templatesInfoId: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          approverId: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          completionDescription: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          inspectionType: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          hiddenDangersViolationsNum: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          facotrLevel: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
        },
        title: '',
        dialogFormVisible: false,
        //上传
        uploadUrl: `${baseURL}swisp-base-service/api/v1/oss/ali/upload?subPath=perform-file/${moment().format(
          'YYYY/MM/DD'
        )}&buketName=${buketName}`,
        headerObj: {
          Authorization: `${getToken()}`,
        },
        projectList: [],
        safeDictList: [],
        templatesList: [],
        uploadedFiles: [],
        uploadedFiles2: [],
        imageFiles: [],
        dutyArray: [],
        hiddenList: [],
        factorsList: [],
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
        userInfo: 'user/user',
      }),
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
    },
    watch: {
      dutyList: {
        handler(val) {
          if (val && val.length) {
            this.dutyArray = val
          } else {
            this.getDutyList()
          }
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      async showEdit(row) {
        this.title = ''
        this.uploadedFiles = []
        this.uploadedFiles2 = []
        this.imageFiles = []
        if (row) {
          await this.goDetails(row)
        }
        this.dialogFormVisible = true
        this.goProjectList()
        this.goDictItems()
        this.goHiddenProblem()
        this.goFactorLevel()
      },
      goDetails(row) {
        try {
          getUserDutyDetailsTempById(row.listId).then((res) => {
            this.form = res.data || row
            this.form = {
              ...this.form,
              ...this.form.dutyListInfo,
              ...this.form.issueFeedbackAndRectificationDto,
              inspectionType:
                this.form.inspectionType || this.form.inspectionType === 0
                  ? this.form.inspectionType + ''
                  : null,
              dutyDate:
                this.form.dutyDate || moment().format('YYYY-MM-DD HH:mm:ss'),
              templatesInfoId: this.form.dutyInspectionItemCont?.id,
              issueFeedbackAndRectificationDtoId:
                this.form.issueFeedbackAndRectificationDto?.id || undefined,
              hiddenDangersViolationsNum:
                this.form.issueFeedbackAndRectificationDto &&
                this.form.issueFeedbackAndRectificationDto
                  .hiddenDangersViolationsNum &&
                this.form.issueFeedbackAndRectificationDto
                  .hiddenDangersViolationsNum + '',
            }
            if (
              this.form.attachmentsAttach &&
              this.form.attachmentsAttach.attachInfoList &&
              this.form.attachmentsAttach.attachInfoList.length
            ) {
              this.uploadedFiles = this.form.attachmentsAttach.attachInfoList
            }
            if (
              this.form.imagesAttach &&
              this.form.imagesAttach.attachInfoList &&
              this.form.imagesAttach.attachInfoList.length
            ) {
              this.imageFiles = this.form.imagesAttach.attachInfoList
            }
            if (
              this.form.onSitePhotosOrVideosAttach &&
              this.form.onSitePhotosOrVideosAttach.attachInfoList &&
              this.form.onSitePhotosOrVideosAttach.attachInfoList.length
            ) {
              this.uploadedFiles2 =
                this.form.onSitePhotosOrVideosAttach.attachInfoList
            }
          })
        } catch (error) {
          this.form = row
          this.form.inspectionType =
            this.form.inspectionType || this.form.inspectionType === 0
              ? this.form.inspectionType + ''
              : null
          this.form.dutyDate = moment().format('YYYY-MM-DD HH:mm:ss')
        }
      },
      async getDutyList() {
        const res = await getDutyInspectionItemsList({
          pageNum: 1,
          pageSize: 9999,
        })
        const { code, data } = res

        if (code === 200) {
          this.dutyArray = data
        }
      },

      close() {
        this.uploadedFiles = []
        this.uploadedFiles2 = []
        this.imageFiles = []
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save(flag) {
        if (!flag) {
          // if (
          //   !this.form.attachmentsAttach ||
          //   !this.form.attachmentsAttach.attachInfoList ||
          //   !this.form.attachmentsAttach.attachInfoList.length
          // ) {
          //   this.$message({
          //     type: 'warning',
          //     message: '请上传附件',
          //   })
          //   return
          // }
          if (
            !this.form.imagesAttach ||
            !this.form.imagesAttach.attachInfoList ||
            !this.form.imagesAttach.attachInfoList.length
          ) {
            this.$message({
              type: 'warning',
              message: '请上传图片',
            })
            return
          }
          if (
            (!this.form.onSitePhotosOrVideosAttach ||
              !this.form.onSitePhotosOrVideosAttach.attachInfoList ||
              !this.form.onSitePhotosOrVideosAttach.attachInfoList.length) &&
            this.form.qualified === false
          ) {
            this.$message({
              type: 'warning',
              message: '请上传现场照片或视频',
            })
            return
          }
        }
        this.form.projectId = this.form.projectId || this.storeProjectId
        this.form.qualified = this.form.qualified || false
        this.form.allocateUserId = this.userId
        const formData = {
          ...this.form,
          issueFeedbackAndRectificationDto: {
            requiredCompletionTime: this.form.requiredCompletionTime,
            issueDescription: this.form.issueDescription,
            rectifierId: this.form.rectifierId,
            rectifierName: this.form.rectifierName,
            feedbackPersonId: this.userId,
            feedbackPersonName: this.userInfo.nickname,
            rectificationReviewerName: this.form.rectificationReviewerName,
            onSitePhotosOrVideosAttach:
              this.form.onSitePhotosOrVideosAttach || {},
            hiddenDangersViolationsNum: this.form.hiddenDangersViolationsNum,
            hiddenDangersViolations: this.form.hiddenDangersViolations,
            facotrLevel: this.form.facotrLevel,
            facotrLevelDesc: this.form.facotrLevelDesc,
            specificDesc: this.form.specificDesc,
            id: this.form.issueFeedbackAndRectificationDtoId,
          },
          attachmentsAttach: this.form.attachmentsAttach || {},
          imagesAttach: this.form.imagesAttach || {},
          id: undefined,
          issueFeedbackAndRectificationDtoId: undefined,
          requiredCompletionTime: undefined,
          issueDescription: undefined,
          rectifierId: undefined,
          onSitePhotosOrVideosAttach: undefined,
          rectificationReviewerName: undefined,
          dataStatus: flag ? false : true,
          responsibilityListName: undefined,
          hiddenDangersViolationsNum: undefined,
          hiddenDangersViolations: undefined,
          facotrLevel: undefined,
          facotrLevelDesc: undefined,
          specificDesc: undefined,
          userId: undefined,
          userName: undefined,
          projectName: undefined,
          positionId: undefined,
          positionName: undefined,
          level: undefined,
          dutyTemplateId: undefined,
          dutyTemplateName: undefined,
          dutyStatus: undefined,
          allocateUserId: undefined,
          allocateUserName: undefined,
          allocateTime: undefined,
          userConfirmationStatus: undefined,
          rejectCause: undefined,
          userConfirmationTime: undefined,
          dutyTemplatesInfoDtoList: undefined,
          approverConfirmationStatus: undefined,
          approverRejectCause: undefined,
          approverConfirmationTime: undefined,
          type: undefined,
          process: undefined,
          frequency: undefined,
          frequencyCount: undefined,
          templatesInfoId: undefined,
          isFirst: undefined,
          rowspan: undefined,
          // idCardAttach: null,
          userDutyListsInfoDtoList: undefined,
        }
        if (flag) {
          selfCertificationOfDuties(formData).then(() => {
            this.$emit('refreshDataList')
            this.close()
          })
        } else {
          this.$refs['form'].validate((valid) => {
            if (valid) {
              selfCertificationOfDuties(formData).then(() => {
                this.$message.success('自证履职成功')
                this.$emit('refreshDataList')
                this.close()
              })
            } else {
              return false
            }
          })
        }
      },
      handleAdd() {
        this.form.dutyTemplatesInfoDtoList.push({
          inspectionItem: '',
          inspectionRequirements: '',
        })
      },
      handleDelete(index) {
        this.form.dutyTemplatesInfoDtoList.splice(index, 1)
      },
      handleAvatarSuccess(res) {
        this.formData.clientLogo = res.data.path
        this.formData.clientLogoUrl = res.data.file
      },
      beforeAvatarUpload(file) {
        const isLt20M = file.size / 1024 / 1024 < 50
        if (!isLt20M) {
          this.$message.error('上传附件大小不能超过 50MB!')
        }
        return isLt20M
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      goTemplatesList() {
        getDutyTemplatesList().then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.templatesList = data
          }
        })
      },
      goDutyTemplatesInfo(id) {
        getDutyTemplatesInfo(id).then((res) => {
          this.form.dutyTemplatesInfoDtoList =
            res.data?.dutyTemplatesInfoDtoList
        })
      },
      handlePersonChange(e) {
        const obj = this.storeUserList.find((item) => item.userId === e)
        if (obj) {
          this.form.approverName = obj.nickname
        }
      },
      handlePersonChange2(e) {
        const obj = this.storeUserList.find((item) => item.userId === e)
        if (obj) {
          this.form.rectifierName = obj.nickname
        }
      },
      changeResponItem(e) {
        const obj = this.projectList.find((item) => item.id === e)
        if (obj) {
          this.form.projectName = obj.projectName
        }
      },
      changeDutyTemplate(e) {
        const obj = this.safeDictList.find((item) => item.id === e)
        if (obj) {
          this.form.inspectionContent = obj.name
        }
      },
      changeHiddenDanger(e) {
        const obj = this.hiddenList.find((item) => item.value === e)
        if (obj) {
          this.form.hiddenDangersViolations = obj.name
          if (obj.remark) {
            const obj2 = this.factorsList.find(
              (item) => item.name == obj.remark
            )
            if (obj2) {
              this.$set(this.form, 'facotrLevel', obj2.id)
              this.$set(this.form, 'facotrLevelDesc', obj2.name)
            }
          }
        }
      },
      changeFactorLevel(e) {
        const obj = this.factorsList.find((item) => item.id === e)
        if (obj) {
          this.form.facotrLevelDesc = obj.name
        }
      },
      // 获取类型字典
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'safe_check_type',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.safeDictList = res.data.data.list || []
      },
      // 隐患问题列表
      async goHiddenProblem() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'hse_violation_level',
          clientId: 'hse-supervision',
        }
        const res = await getDictItems(params)
        this.hiddenList = res.data.data.list || []
      },
      // 因素级别列表
      async goFactorLevel() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'yh_level',
          clientId: 'hse-supervision',
        }
        const res = await getDictItems(params)
        this.factorsList = res.data.data.list || []
      },
      handleFileSuccess(fileInfo) {
        if (!this.form.attachmentsAttach) {
          this.form.attachmentsAttach = {
            code: '',
            attachInfoList: [],
          }
        }

        this.form.attachmentsAttach.attachInfoList = [...this.uploadedFiles]

        this.$message.success(`文件 ${fileInfo.name} 上传成功`)
      },
      handleFileRemove(file) {
        if (this.form.attachmentsAttach) {
          this.form.attachmentsAttach.attachInfoList = [...this.uploadedFiles]
        }

        this.$message.info(`文件 ${file.name} 已移除`)
      },
      handleImageSuccess(fileInfo) {
        if (!this.form.imagesAttach) {
          this.form.imagesAttach = {
            code: '',
            attachInfoList: [],
          }
        }

        this.form.imagesAttach.attachInfoList = [...this.imageFiles]

        this.$message.success(`图片 ${fileInfo.name} 上传成功`)
      },

      handleImageRemove(file) {
        if (this.form.imagesAttach) {
          this.form.imagesAttach.attachInfoList = [...this.imageFiles]
        }

        this.$message.info(`图片 ${file.name} 已移除`)
      },
      handleFileSuccess2(fileInfo) {
        if (!this.form.onSitePhotosOrVideosAttach) {
          this.form.onSitePhotosOrVideosAttach = {
            code: '',
            attachInfoList: [],
          }
        }

        this.form.onSitePhotosOrVideosAttach.attachInfoList = [
          ...this.uploadedFiles2,
        ]

        this.$message.success(`文件 ${fileInfo.name} 上传成功`)
      },
      handleFileRemove2(file) {
        if (this.form.onSitePhotosOrVideosAttach) {
          this.form.onSitePhotosOrVideosAttach.attachInfoList = [
            ...this.uploadedFiles2,
          ]
        }

        this.$message.info(`文件 ${file.name} 已移除`)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
