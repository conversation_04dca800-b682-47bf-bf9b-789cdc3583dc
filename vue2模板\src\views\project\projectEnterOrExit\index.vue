<!-- 项目人员 -->
<template>
  <div class="app-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item label="项目名称" prop="projectId">
        <el-select
          v-model="queryParams.projectId"
          clearable
          filterable
          placeholder="请选择"
          @change="goUserByProject"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="userByProjectList && userByProjectList.length"
        label="人员"
        prop="employeeId"
      >
        <el-select
          v-model="queryParams.employeeId"
          clearable
          filterable
          placeholder="请选择"
          style="width: 200px"
        >
          <el-option
            v-for="item in userByProjectList"
            :key="item.employeeId"
            :label="item.employeeName"
            :value="item.employeeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          class="filter-item"
          icon="el-icon-search"
          type="primary"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column align="center" label="人员" prop="employeeName" />
      <el-table-column align="center" label="项目名称" prop="projectName" />
      <el-table-column align="center" label="部门" prop="deptName" />
      <el-table-column align="center" label="级别" prop="level" />
      <el-table-column align="center" label="岗位" prop="position" />
      <el-table-column align="center" label="人员状态" prop="employmentStatus">
        <template #default="scope">
          <span v-if="scope.row.employmentStatus === 'On_DUTY'">在岗</span>
          <span v-else-if="scope.row.employmentStatus === 'TEMPORARY'">
            暂离
          </span>
          <span v-else-if="scope.row.employmentStatus === 'TRANSFER'">
            调离
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="审批人" prop="approver">
        <template slot-scope="scope">
          <span v-for="(item, index) in scope.row.approver" :key="item.id">
            {{ index ? '、' + item.name : item.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="审批时间" prop="approvalTime" />
      <el-table-column align="center" label="审批记录" prop="">
        <template #default="scope">
          <el-button type="text" @click.stop="handleApproval(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="流程状态" prop="approvalStatus">
        <template #default="scope">
          <el-tag v-if="scope.row.approvalStatus === 'IN_APPROVAL'">
            审批中
          </el-tag>
          <el-tag
            v-else-if="scope.row.approvalStatus === 'PASS'"
            type="success"
          >
            通过
          </el-tag>
          <el-tag
            v-else-if="scope.row.approvalStatus === 'REJECT'"
            type="danger"
          >
            驳回
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="140">
        <template #default="scope">
          <el-button
            circle
            icon="el-icon-edit-outline"
            plain
            type="primary"
            @click.stop="handleUpdate(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页工具条 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <el-dialog
      :before-close="handleClose"
      title="人员状态变更"
      :visible.sync="dialog.visible"
      width="600px"
    >
      <el-form
        ref="formRef"
        label-width="80px"
        :model="dialog.formData"
        :rules="dialog.rules"
      >
        <el-form-item label="姓名" prop="employeeName">
          {{ dialog.formData.employeeName }}
        </el-form-item>
        <el-form-item label="人员状态" prop="employmentStatus">
          <el-select
            v-model="dialog.formData.employmentStatus"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in personStatus"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="出入时间" prop="changeTime">
          <el-date-picker
            v-model="dialog.formData.changeTime"
            placeholder="请选择"
            style="width: 100%"
            type="date"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="审批人" prop="approverId">
          <el-select
            v-model="dialog.formData.approverId"
            clearable
            filterable
            multiple
            placeholder="请选择"
            style="width: 100%"
            @change="handlePersonChange"
          >
            <el-option
              v-for="item in storeUserList"
              :key="item.userId"
              :label="item.nickname"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 审批记录弹窗 -->
    <approval-record-dialog
      :loading="approvalDialog.loading"
      :record-list="approvalRecordList"
      :title="approvalDialog.title"
      :visible.sync="approvalDialog.visible"
      @close="approvalRecordList = []"
    />
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import {
    entryAndExitProjectExamineRecord,
    getProjectRelationsList,
    getChangeStatus,
  } from '@/api/project/projectPerson'
  import { getDictItems } from '@/api/user'
  import { getProjectList, getUserByProject } from '@/api/project/projectInfo'
  export default {
    name: 'ProjectApproval',
    data() {
      return {
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          employeeId: undefined,
          projectName: undefined,
          projectId: undefined,
        },
        total: 0,
        dataList: [],
        tableData: [], // 用于存储处理后的表格数据
        loading: false,
        selectionList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        single: true,
        dialog: {
          title: '添加',
          visible: false,
          formData: {},
          rules: {
            employmentStatus: [
              {
                required: true,
                trigger: 'change',
                message: '请选择',
              },
            ],
            changeTime: [
              {
                required: true,
                trigger: 'change',
                message: '请选择',
              },
            ],
            approverId: [
              {
                required: true,
                trigger: 'change',
                message: '请选择',
              },
            ],
          },
        },
        // 审批记录弹窗数据
        approvalDialog: {
          visible: false,
          loading: false,
          title: '审批记录',
        },
        personStatus: [
          {
            value: 'On_DUTY',
            name: '在岗',
          },
          {
            value: 'TEMPORARY',
            name: '暂离',
          },
          {
            value: 'TRANSFER',
            name: '调离',
          },
        ],
        dictList: [],
        projectList: [],
        userByProjectList: [],
        approvalRecordList: null,
      }
    },
    computed: {
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
      projectStage() {
        return (value) => {
          return this.dictList.find((item) => item.value == value)?.name || ''
        }
      },
    },
    mounted() {
      this.getList()
      this.goDictItems()
      this.goProjectList()
    },
    methods: {
      async getList() {
        this.loading = true
        try {
          const res = await getProjectRelationsList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            this.tableData = data
            this.total = page.totalCount
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      save() {
        this.$refs['formRef'].validate((valid) => {
          if (valid) {
            const {
              employeeId,
              projectId,
              employmentStatus,
              approver,
              changeTime,
            } = this.dialog.formData
            const form = {
              employeeId,
              projectId,
              employmentStatus,
              approver,
              changeTime: changeTime + ' 00:00:00',
            }
            getChangeStatus(form).then((res) => {
              if (res.code === 200) {
                this.$message.success('操作成功')
                this.dialog.visible = false
                this.getList()
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            return false
          }
        })
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      handleApproval(row) {
        const params = {
          id: row.id,
          pageNum: 1,
          pageSize: 1000,
          employeeId: row.employeeId,
        }
        entryAndExitProjectExamineRecord(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.approvalRecordList = data || []
            this.approvalDialog.visible = true
            this.approvalDialog.title = `${row.employeeName} - 审批记录`
          }
        })
      },
      // 获取类型字典
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'project_state',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.dictList = res.data.data.list || []
      },
      goUserByProject() {
        this.queryParams.employeeId = null
        if (!this.queryParams.projectId) {
          this.userByProjectList = []
          return
        }
        getUserByProject({
          projectId: this.queryParams.projectId,
          pageNum: 1,
          pageSize: 9999,
        }).then((res) => {
          const { code, data } = res
          if (code === 200 && data.relationship !== 2) {
            this.userByProjectList = data
          } else {
            this.userByProjectList = []
          }
        })
      },
      handlePersonChange(ids) {
        this.dialog.formData['approver'] = []
        if (ids?.length) {
          this.dialog.formData['approver'] = ids.map((id) => {
            const user = this.storeUserList.find((item) => item.userId === id)
            return user
              ? {
                  id: user.userId,
                  name: user.nickname,
                }
              : null
          })
        }
      },
      changeProject(e) {
        const obj = this.projectList.find((item) => item.id === e)
        if (obj) {
          this.dialog.formData.projectName = obj.projectName
        }
      },
      handleUpdate(row) {
        this.dialog.visible = true
        if (this.$refs.formRef) {
          this.$refs.formRef.resetFields()
        }
        this.dialog.formData = row
      },
      handleClose() {
        this.$refs.formRef.resetFields()
        this.dialog.visible = false
      },
      handleQuery() {
        this.getList()
      },
      resetQuery() {
        this.userByProjectList = []
        this.$refs.queryFormRef.resetFields()
        this.getList()
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.handleQuery()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.handleQuery()
      },
    },
  }
</script>

<style lang="scss" scoped></style>
