<!-- 积分统计 -->
<template>
  <div class="app-container">
    <!-- 项目平均分排名 -->
    <el-card class="chart-container" shadow="hover">
      <div slot="header" class="clearfix">
        <span>项目平均分排名图</span>
        <div class="filter-container">
          <el-select
            v-model="projectTimeRange"
            placeholder="全部时间"
            size="small"
            @change="handleProjectScoreRankQuery"
          >
            <el-option label="全部时间" value="all" />
            <el-option label="本周" value="week" />
            <el-option label="近30天" value="month" />
            <el-option label="本月" value="currentMonth" />
          </el-select>
          <!-- <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            @click="exportProjectData"
          >
            导出数据
          </el-button> -->
        </div>
      </div>
      <div ref="projectRankChart" class="rank-chart"></div>
    </el-card>

    <!-- 部门平均分统计 -->
    <el-card class="chart-container" shadow="hover">
      <div slot="header" class="clearfix">
        <span>部门平均分统计</span>
        <div class="filter-container">
          <el-select
            v-model="deptTimeRange"
            placeholder="全部时间"
            size="small"
            @change="handleDeptScoreRankQuery"
          >
            <el-option label="全部时间" value="all" />
            <el-option label="本周" value="week" />
            <el-option label="近30天" value="month" />
            <el-option label="本月" value="currentMonth" />
          </el-select>
          <!-- <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            @click="exportDeptData"
          >
            导出数据
          </el-button> -->
        </div>
      </div>
      <div ref="deptRankChart" class="rank-chart"></div>
    </el-card>

    <!-- 各层级得分排名 -->
    <el-card class="chart-container" shadow="hover">
      <div slot="header" class="clearfix">
        <span>各层级得分排名</span>
        <div class="filter-container">
          <el-select
            v-model="queryParams.timeRange"
            placeholder="全部时间"
            size="small"
            @change="handleLevelTimeChange"
          >
            <el-option label="全部时间" value="all" />
            <el-option label="本周" value="week" />
            <el-option label="近30天" value="month" />
            <el-option label="本月" value="currentMonth" />
          </el-select>
          <!-- <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            @click="exportLevelData"
          >
            导出数据
          </el-button> -->
        </div>
      </div>

      <!-- 各层级排名图表 -->
      <div class="level-charts">
        <div class="level-chart-item">
          <div class="level-title">物探队（中心）领导得分排名</div>
          <div class="level-chart-bg">
            <div ref="centerLevelChart" class="level-chart"></div>
          </div>
        </div>

        <div class="level-chart-item">
          <div class="level-title">工程队领导得分排名</div>
          <div class="level-chart-bg">
            <div ref="teamLevelChart" class="level-chart"></div>
          </div>
        </div>

        <div class="level-chart-item">
          <div class="level-title">班组长得分排名</div>
          <div class="level-chart-bg">
            <div ref="groupLevelChart" class="level-chart"></div>
          </div>
        </div>

        <div class="level-chart-item">
          <div class="level-title">员工得分排名</div>
          <div class="level-chart-bg">
            <div ref="employeeLevelChart" class="level-chart"></div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 项目分数看板 -->
    <el-card class="score-board" shadow="hover">
      <div slot="header" class="clearfix">
        <span>项目分数看板</span>
        <div class="filter-container">
          <el-cascader
            v-model="queryParams.department_id"
            clearable
            filterable
            :options="deptOptions"
            placeholder="所属部门"
            :show-all-levels="false"
            style="margin-right: 10px"
            @change="changeDept"
          />
          <el-select
            v-model="queryParams.level_id"
            clearable
            filterable
            placeholder="全部级别"
            size="small"
          >
            <el-option
              v-for="item in levelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-select
            v-model="queryParams.projectId"
            clearable
            filterable
            placeholder="全部项目"
            size="small"
          >
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.projectName"
              :value="item.id"
            />
          </el-select>
          <el-select
            v-model="queryParams.timeRange"
            placeholder="全部时间"
            size="small"
          >
            <el-option label="全部时间" value="all" />
            <el-option label="本周" value="week" />
            <el-option label="近30天" value="month" />
            <el-option label="本月" value="currentMonth" />
          </el-select>
          <el-button
            icon="el-icon-search"
            size="small"
            type="primary"
            @click="handleBoardQuery"
          >
            查询
          </el-button>
          <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            @click="exportBoardData"
          >
            导出数据
          </el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        border
        :data="scoreList"
        style="width: 100%"
      >
        <el-table-column align="center" label="排名" prop="rank" width="80" />
        <el-table-column align="center" label="姓名" prop="name" />
        <el-table-column align="center" label="项目" prop="project" />
        <el-table-column
          align="center"
          label="部门"
          prop="department"
          width="150"
        />
        <el-table-column align="center" label="级别" prop="level" />
        <el-table-column
          align="center"
          label="当前积分"
          prop="score"
          width="120"
        />
        <el-table-column align="center" label="变动" prop="change">
          <template slot-scope="scope">
            <span
              :class="
                scope.row.change > 0
                  ? 'increase'
                  : scope.row.change < 0
                  ? 'decrease'
                  : ''
              "
            >
              {{
                scope.row.change > 0 ? '+' + scope.row.change : scope.row.change
              }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        class="pagination"
        :current-page="pagination.currentPage"
        layout="total, sizes, prev, pager, next, jumper"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </el-card>
  </div>
</template>

<script>
  import * as echarts from 'echarts'
  import { mapGetters } from 'vuex'
  import { getProjectList } from '@/api/project/projectInfo'
  import {
    getProjectMemberList,
    getDepartmentAvgScore,
    getProjectAvgScore,
    exportProjectMemberList,
  } from '@/api/statistic/statistic'
  import { listSelectDepartments } from '@/api/system/dept'
  import { getDictItems } from '@/api/user'

  export default {
    name: 'SystemScoreStatistic',
    data() {
      return {
        loading: false,
        // 统一查询参数
        queryParams: {
          timeRange: 'all',
          projectId: '',
          department_id: '',
          level_id: '',
        },
        // 项目平均分排名
        projectTimeRange: 'all',
        projectRankChart: null,
        // 部门平均分统计
        deptTimeRange: 'all',
        deptRankChart: null,
        // 各层级得分排名
        levelTimeRange: 'all',
        centerLevelChart: null,
        teamLevelChart: null,
        groupLevelChart: null,
        employeeLevelChart: null,
        // 项目分数看板
        boardDepartment: '',
        boardLevel: '',
        boardProject: '',
        boardTimeRange: 'all',
        // 项目列表
        projectList: [],
        // 积分列表
        scoreList: [],
        // 分页
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 25,
        },
        // 图表数据
        chartData: {
          // 项目平均分排名数据
          project: {
            names: [],
            scores: [],
          },
          // 部门平均分统计数据
          department: {
            names: [],
            scores: [],
          },
          // 各层级得分排名数据
          level: {
            center: {
              names: [],
              scores: [],
            },
            team: {
              names: [],
              scores: [],
            },
            group: {
              names: [],
              scores: [],
            },
            employee: {
              names: [],
              scores: [],
            },
          },
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
      }),
    },
    async mounted() {
      await this.goProjectList()
      this.fetchData()
      this.handleProjectScoreRankQuery()
      this.handleDeptScoreRankQuery()
      this.loadDeptOptions()
      this.goDictItems()
      this.initCharts()
    },
    beforeDestroy() {
      this.disposeCharts()
      window.removeEventListener('resize', this.resizeCharts)
    },
    methods: {
      // 初始化所有图表
      initCharts() {
        this.$nextTick(() => {
          this.initLevelCharts()
          window.addEventListener('resize', this.resizeCharts)
        })
      },

      // 初始化项目平均分排名图表
      initProjectRankChart() {
        this.projectRankChart = echarts.init(this.$refs.projectRankChart)
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: this.chartData.project.names,
            axisTick: {
              alignWithLabel: true,
            },
          },
          yAxis: {
            type: 'value',
            min: function (value) {
              return Math.floor(value.min * 0.95)
            },
          },
          series: [
            {
              name: '项目平均分',
              type: 'bar',
              barWidth: '33%',
              data: this.chartData.project.scores,
              itemStyle: {
                color: '#4FC08D',
                borderRadius: [8, 8, 0, 0],
              },
            },
          ],
        }
        this.projectRankChart.setOption(option)
      },

      // 初始化部门平均分统计图表
      initDeptRankChart() {
        this.deptRankChart = echarts.init(this.$refs.deptRankChart)
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: this.chartData.department.names,
            axisTick: {
              alignWithLabel: true,
            },
          },
          yAxis: {
            type: 'value',
            min: function (value) {
              return Math.floor(value.min * 0.95)
            },
          },
          series: [
            {
              name: '部门平均分',
              type: 'bar',
              barWidth: '33%',
              data: this.chartData.department.scores,
              itemStyle: {
                color: '#8E77ED',
                borderRadius: [8, 8, 0, 0],
              },
            },
          ],
        }
        this.deptRankChart.setOption(option)
      },

      // 初始化各层级得分排名图表
      initLevelCharts() {
        // 物探队（中心）领导得分排名
        this.centerLevelChart = echarts.init(this.$refs.centerLevelChart)
        const centerOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: this.chartData.level.center.names,
            axisTick: {
              alignWithLabel: true,
            },
          },
          yAxis: {
            type: 'value',
            min: function (value) {
              return Math.floor(value.min * 0.95)
            },
          },
          series: [
            {
              name: '积分',
              type: 'bar',
              barWidth: 30,
              data: this.chartData.level.center.scores,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#A0C4FF' },
                  { offset: 1, color: '#6495ED' },
                ]),
                borderRadius: [8, 8, 0, 0],
              },
            },
          ],
        }
        this.centerLevelChart.setOption(centerOption)

        // 工程队领导得分排名
        this.teamLevelChart = echarts.init(this.$refs.teamLevelChart)
        const teamOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: this.chartData.level.team.names,
            axisTick: {
              alignWithLabel: true,
            },
          },
          yAxis: {
            type: 'value',
            min: function (value) {
              return Math.floor(value.min * 0.95)
            },
          },
          series: [
            {
              name: '积分',
              type: 'bar',
              barWidth: 30,
              data: this.chartData.level.team.scores,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#D1B3FF' },
                  { offset: 1, color: '#9370DB' },
                ]),
                borderRadius: [8, 8, 0, 0],
              },
            },
          ],
        }
        this.teamLevelChart.setOption(teamOption)

        // 班组长得分排名
        this.groupLevelChart = echarts.init(this.$refs.groupLevelChart)
        const groupOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: this.chartData.level.group.names,
            axisTick: {
              alignWithLabel: true,
            },
          },
          yAxis: {
            type: 'value',
            min: function (value) {
              return Math.floor(value.min * 0.95)
            },
          },
          series: [
            {
              name: '积分',
              type: 'bar',
              barWidth: 30,
              data: this.chartData.level.group.scores,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#B9FBC0' },
                  { offset: 1, color: '#3CB371' },
                ]),
                borderRadius: [8, 8, 0, 0],
              },
            },
          ],
        }
        this.groupLevelChart.setOption(groupOption)

        // 员工得分排名
        this.employeeLevelChart = echarts.init(this.$refs.employeeLevelChart)
        const employeeOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: this.chartData.level.employee.names,
            axisTick: {
              alignWithLabel: true,
            },
          },
          yAxis: {
            type: 'value',
            min: function (value) {
              return Math.floor(value.min * 0.95)
            },
          },
          series: [
            {
              name: '积分',
              type: 'bar',
              barWidth: 30,
              data: this.chartData.level.employee.scores,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#FFD6A5' },
                  { offset: 1, color: '#FF8C00' },
                ]),
                borderRadius: [8, 8, 0, 0],
              },
            },
          ],
        }
        this.employeeLevelChart.setOption(employeeOption)
      },

      // 调整图表大小
      resizeCharts() {
        if (this.projectRankChart) this.projectRankChart.resize()
        if (this.deptRankChart) this.deptRankChart.resize()
        if (this.centerLevelChart) this.centerLevelChart.resize()
        if (this.teamLevelChart) this.teamLevelChart.resize()
        if (this.groupLevelChart) this.groupLevelChart.resize()
        if (this.employeeLevelChart) this.employeeLevelChart.resize()
      },

      // 销毁图表
      disposeCharts() {
        if (this.projectRankChart) {
          this.projectRankChart.dispose()
          this.projectRankChart = null
        }
        if (this.deptRankChart) {
          this.deptRankChart.dispose()
          this.deptRankChart = null
        }
        if (this.centerLevelChart) {
          this.centerLevelChart.dispose()
          this.centerLevelChart = null
        }
        if (this.teamLevelChart) {
          this.teamLevelChart.dispose()
          this.teamLevelChart = null
        }
        if (this.groupLevelChart) {
          this.groupLevelChart.dispose()
          this.groupLevelChart = null
        }
        if (this.employeeLevelChart) {
          this.employeeLevelChart.dispose()
          this.employeeLevelChart = null
        }
      },

      // 获取图表配置
      getChartOption(names, scores, colorType) {
        let gradient
        switch (colorType) {
          case 'center':
            gradient = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#A0C4FF' },
              { offset: 1, color: '#6495ED' },
            ])
            break
          case 'team':
            gradient = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#D1B3FF' },
              { offset: 1, color: '#9370DB' },
            ])
            break
          case 'group':
            gradient = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#B9FBC0' },
              { offset: 1, color: '#3CB371' },
            ])
            break
          case 'employee':
            gradient = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#FFD6A5' },
              { offset: 1, color: '#FF8C00' },
            ])
            break
          default:
            gradient = colorType
        }
        return {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: names,
            axisTick: {
              alignWithLabel: true,
            },
          },
          yAxis: {
            type: 'value',
            min: function (value) {
              return Math.floor(value.min * 0.95)
            },
          },
          series: [
            {
              name: '积分',
              type: 'bar',
              barWidth: 30,
              data: scores,
              itemStyle: {
                color: gradient,
                borderRadius: [8, 8, 0, 0],
              },
            },
          ],
        }
      },

      // 获取数据
      fetchData() {
        this.loading = true

        // 构建请求参数
        const params = {
          ...this.queryParams,
          department_id:
            this.queryParams.department_id &&
            this.queryParams.department_id.length > 0
              ? this.queryParams.department_id[
                  this.queryParams.department_id.length - 1
                ]
              : undefined,
          project_id: this.queryParams.projectId,
          page_num: this.pagination.currentPage,
          page_size: this.pagination.pageSize,
        }

        // 根据时间范围设置开始和结束时间
        const currentDate = new Date()
        const currentYear = currentDate.getFullYear()
        let weekStart, monthStart, currMonthStart

        // 设置时间范围
        switch (this.queryParams.timeRange) {
          case 'week':
            // 本周
            weekStart = new Date(currentDate)
            weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1) // 周一
            weekStart.setHours(0, 0, 0, 0)

            params.start_time =
              weekStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'month':
            // 近30天
            monthStart = new Date(currentDate)
            monthStart.setDate(currentDate.getDate() - 29)
            monthStart.setHours(0, 0, 0, 0)

            params.start_time =
              monthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'currentMonth':
            // 本月
            currMonthStart = new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              1
            )
            currMonthStart.setHours(0, 0, 0, 0)

            params.start_time =
              currMonthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'all':
          default:
            // 全部时间（本年）
            params.start_time = `${currentYear}-01-01 00:00:00`
            params.end_time = `${currentYear}-12-31 23:59:59`
            break
        }

        // 调用接口获取数据
        getProjectMemberList(params)
          .then((res) => {
            this.loading = false
            const { code, data } = res

            if (code === 200) {
              // 更新分页信息
              this.pagination.total = data.total

              // 处理成员列表数据
              if (data.member_list && data.member_list.length > 0) {
                // 按积分排序
                const sortedList = [...data.member_list].sort(
                  (a, b) => b.score - a.score
                )

                // 添加排名
                this.scoreList = sortedList.map((item, index) => {
                  return {
                    rank: index + 1,
                    name: item.username,
                    project: item.project_name || '-',
                    department: item.department,
                    level: item.level,
                    score: item.score.toLocaleString(),
                    change: ((item.score || 0) - 1000).toFixed(0),
                  }
                })

                // 更新图表数据
                this.updateChartData(sortedList)
              } else {
                this.scoreList = []
                // 更新图表数据为空
                this.updateChartData([])
              }
            } else {
              this.$message.error('获取数据失败')
            }
          })
          .catch((error) => {
            this.loading = false
            console.error('获取数据出错:', error)
            this.$message.error('获取数据出错')
          })
      },

      // 更新图表数据
      updateChartData(memberList) {
        // 按级别分组
        const centerLeaders = memberList.filter(
          (item) => item.level === '物探队（中心）领导'
        )
        const teamLeaders = memberList.filter(
          (item) => item.level === '工程队领导'
        )
        const groupLeaders = memberList.filter(
          (item) => item.level === '班组长'
        )
        const employees = memberList.filter((item) => item.level === '员工')

        // 更新中心领导图表数据
        if (centerLeaders.length > 0) {
          const topCenterLeaders = centerLeaders.slice(0, 5)
          this.chartData.level.center = {
            names: topCenterLeaders.map((item) => item.username),
            scores: topCenterLeaders.map((item) => item.score),
          }
          if (this.centerLevelChart) {
            this.centerLevelChart.setOption(
              this.getChartOption(
                this.chartData.level.center.names,
                this.chartData.level.center.scores,
                'center'
              )
            )
          }
        } else {
          this.chartData.level.center = {
            names: [],
            scores: [],
          }
          if (this.centerLevelChart) {
            this.centerLevelChart.clear()
          }
        }

        // 更新工程队领导图表数据
        if (teamLeaders.length > 0) {
          const topTeamLeaders = teamLeaders.slice(0, 5)
          this.chartData.level.team = {
            names: topTeamLeaders.map((item) => item.username),
            scores: topTeamLeaders.map((item) => item.score),
          }
          if (this.teamLevelChart) {
            this.teamLevelChart.setOption(
              this.getChartOption(
                this.chartData.level.team.names,
                this.chartData.level.team.scores,
                'team'
              )
            )
          }
        } else {
          this.chartData.level.team = {
            names: [],
            scores: [],
          }
          if (this.teamLevelChart) {
            this.teamLevelChart.clear()
          }
        }

        // 更新班组长图表数据
        if (groupLeaders.length > 0) {
          const topGroupLeaders = groupLeaders.slice(0, 5)
          this.chartData.level.group = {
            names: topGroupLeaders.map((item) => item.username),
            scores: topGroupLeaders.map((item) => item.score),
          }
          if (this.groupLevelChart) {
            this.groupLevelChart.setOption(
              this.getChartOption(
                this.chartData.level.group.names,
                this.chartData.level.group.scores,
                'group'
              )
            )
          }
        } else {
          this.chartData.level.group = {
            names: [],
            scores: [],
          }
          if (this.groupLevelChart) {
            this.groupLevelChart.clear()
          }
        }

        // 更新员工图表数据
        if (employees.length > 0) {
          const topEmployees = employees.slice(0, 5)
          this.chartData.level.employee = {
            names: topEmployees.map((item) => item.username),
            scores: topEmployees.map((item) => item.score),
          }
          if (this.employeeLevelChart) {
            this.employeeLevelChart.setOption(
              this.getChartOption(
                this.chartData.level.employee.names,
                this.chartData.level.employee.scores,
                'employee'
              )
            )
          }
        } else {
          this.chartData.level.employee = {
            names: [],
            scores: [],
          }
          if (this.employeeLevelChart) {
            this.employeeLevelChart.clear()
          }
        }
      },

      // 获取项目列表
      goProjectList() {
        try {
          getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.projectList = data
              this.$set(
                this.queryParams,
                'projectId',
                data[0].id || this.projectId
              )
            }
          })
        } catch (error) {
          console.error('获取项目列表出错:', error)
        }
      },

      // 加载部门选项
      async loadDeptOptions() {
        listSelectDepartments().then((response) => {
          this.deptOptions = response.data
        })
      },

      // 获取字典项
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'project_position',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.levelList = res.data.data.list || []
      },

      // 部门变化处理
      changeDept(e) {
        console.log(e)
      },

      // 各层级得分排名时间范围变化
      handleLevelTimeChange() {
        this.fetchData()
      },

      // 项目分数看板查询
      handleBoardQuery() {
        this.fetchData()
      },

      // 项目平均分排名
      handleProjectScoreRankQuery() {
        this.chartData.project = {
          names: [],
          scores: [],
        }
        const params = {
          pageNum: 1,
          pageSize: 1000,
        }
        const currentDate = new Date()
        const currentYear = currentDate.getFullYear()
        let weekStart, monthStart, currMonthStart
        switch (this.projectTimeRange) {
          case 'week':
            // 本周
            weekStart = new Date(currentDate)
            weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1) // 周一
            weekStart.setHours(0, 0, 0, 0)

            params.start_time =
              weekStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'month':
            // 近30天
            monthStart = new Date(currentDate)
            monthStart.setDate(currentDate.getDate() - 29)
            monthStart.setHours(0, 0, 0, 0)

            params.start_time =
              monthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'currentMonth':
            // 本月
            currMonthStart = new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              1
            )
            currMonthStart.setHours(0, 0, 0, 0)

            params.start_time =
              currMonthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'all':
          default:
            // 全部时间（本年）
            params.start_time = `${currentYear}-01-01 00:00:00`
            params.end_time = `${currentYear}-12-31 23:59:59`
            break
        }
        getProjectAvgScore(params).then((res) => {
          const { code, data } = res
          if (
            code === 200 &&
            data.project_list &&
            data.project_list.length > 0
          ) {
            data.project_list.forEach((item) => {
              this.chartData.project.names.push(item.project_name)
              this.chartData.project.scores.push(item.project_average_score)
            })
            this.initProjectRankChart()
          }
        })
      },

      // 部门平均分排名
      handleDeptScoreRankQuery() {
        this.chartData.department = {
          names: [],
          scores: [],
        }
        const params = {
          pageNum: 1,
          pageSize: 1000,
        }
        const currentDate = new Date()
        const currentYear = currentDate.getFullYear()
        let weekStart, monthStart, currMonthStart
        switch (this.deptTimeRange) {
          case 'week':
            // 本周
            weekStart = new Date(currentDate)
            weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1) // 周一
            weekStart.setHours(0, 0, 0, 0)

            params.start_time =
              weekStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'month':
            // 近30天
            monthStart = new Date(currentDate)
            monthStart.setDate(currentDate.getDate() - 29)
            monthStart.setHours(0, 0, 0, 0)

            params.start_time =
              monthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'currentMonth':
            // 本月
            currMonthStart = new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              1
            )
            currMonthStart.setHours(0, 0, 0, 0)

            params.start_time =
              currMonthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'all':
          default:
            // 全部时间（本年）
            params.start_time = `${currentYear}-01-01 00:00:00`
            params.end_time = `${currentYear}-12-31 23:59:59`
            break
        }
        getDepartmentAvgScore(params).then((res) => {
          const { code, data } = res
          if (
            code === 200 &&
            data.department_list &&
            data.department_list.length > 0
          ) {
            data.department_list.forEach((item) => {
              this.chartData.department.names.push(item.department_name)
              this.chartData.department.scores.push(
                item.department_average_score
              )
            })
            this.initDeptRankChart()
          }
        })
      },
      exportBoardData() {
        const params = {
          ...this.queryParams,
          department_id:
            this.queryParams.department_id &&
            this.queryParams.department_id.length > 0
              ? this.queryParams.department_id[
                  this.queryParams.department_id.length - 1
                ]
              : undefined,
          project_id: this.queryParams.projectId,
          page_num: this.pagination.currentPage,
          page_size: this.pagination.pageSize,
          export: true,
        }
        // 根据时间范围设置开始和结束时间
        const currentDate = new Date()
        const currentYear = currentDate.getFullYear()
        let weekStart, monthStart, currMonthStart

        // 设置时间范围
        switch (this.queryParams.timeRange) {
          case 'week':
            // 本周
            weekStart = new Date(currentDate)
            weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1) // 周一
            weekStart.setHours(0, 0, 0, 0)

            params.start_time =
              weekStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'month':
            // 近30天
            monthStart = new Date(currentDate)
            monthStart.setDate(currentDate.getDate() - 29)
            monthStart.setHours(0, 0, 0, 0)

            params.start_time =
              monthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'currentMonth':
            // 本月
            currMonthStart = new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              1
            )
            currMonthStart.setHours(0, 0, 0, 0)

            params.start_time =
              currMonthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'all':
          default:
            // 全部时间（本年）
            params.start_time = `${currentYear}-01-01 00:00:00`
            params.end_time = `${currentYear}-12-31 23:59:59`
            break
        }
        exportProjectMemberList(params).then((response) => {
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
          })
          const a = document.createElement('a')
          const href = window.URL.createObjectURL(blob)
          a.href = href
          const date = new Date().getTime()
          a.setAttribute('download', `成员积分${date}.xlsx`)
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(href)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 20px;
  }

  .chart-container {
    margin-bottom: 20px;

    .filter-container {
      float: right;

      .el-select {
        margin-right: 10px;
      }
    }

    .rank-chart {
      width: 100%;
      height: 400px;
    }
  }

  .level-charts {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .level-chart-item {
      .level-title {
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .level-chart-bg {
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: 4px;

        .level-chart {
          width: 100%;
          height: 300px;
        }
      }
    }
  }

  .score-board {
    .filter-container {
      float: right;

      .el-select {
        margin-right: 10px;
      }
    }

    .increase {
      color: #67c23a;
    }

    .decrease {
      color: #f56c6c;
    }

    .pagination {
      margin-top: 20px;
      text-align: right;
    }
  }
  .clearfix {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
