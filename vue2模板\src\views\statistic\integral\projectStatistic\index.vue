<!-- 积分统计 -->
<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-form class="filter-container" :inline="true" :model="queryParams">
      <el-form-item label="时间范围">
        <el-select v-model="queryParams.timeRange" placeholder="请选择时间范围">
          <el-option label="全部时间" value="all" />
          <el-option label="本周" value="week" />
          <el-option label="近30天" value="month" />
          <el-option label="本月" value="currentMonth" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目">
        <el-select
          v-model="queryParams.projectId"
          clearable
          filterable
          placeholder="请选择项目"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">
          查询
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 积分概览卡片 -->
    <el-row class="score-overview" :gutter="20">
      <el-col :span="6">
        <el-card class="score-card" shadow="hover">
          <div class="score-item total-score">
            <div class="score-value">{{ projectScoreData.totalScore }}</div>
            <div class="score-label">项目总积分</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="score-card" shadow="hover">
          <div class="score-item avg-score">
            <div class="score-value">{{ projectScoreData.avgScore }}</div>
            <div class="score-label">项目平均积分</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="score-card" shadow="hover">
          <div class="score-item highest-score">
            <div class="score-value">{{ projectScoreData.highestScore }}</div>
            <div class="score-label">最高员工积分</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="score-card" shadow="hover">
          <div class="score-item lowest-score">
            <div class="score-value">{{ projectScoreData.lowestScore }}</div>
            <div class="score-label">最低员工积分</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 成员积分排名图表 -->
    <el-card class="chart-container" shadow="hover">
      <div slot="header" class="clearfix">
        <span>成员积分排名图</span>
        <div class="chart-tabs">
          <!-- <el-radio-group
            v-model="activeChartTab"
            size="small"
            @change="handleChartTabChange"
          >
            <el-radio-button label="week">本周</el-radio-button>
            <el-radio-button label="month">近30天</el-radio-button>
            <el-radio-button label="all">本月</el-radio-button>
          </el-radio-group> -->
        </div>
      </div>

      <!-- 各层级排名图表 -->
      <div class="level-charts">
        <div class="level-chart-item">
          <div class="level-title">物探队（中心）领导得分排名</div>
          <div class="level-chart-bg">
            <div ref="centerRankChart" class="level-chart"></div>
          </div>
        </div>

        <div class="level-chart-item">
          <div class="level-title">工程队领导得分排名</div>
          <div class="level-chart-bg">
            <div ref="teamRankChart" class="level-chart"></div>
          </div>
        </div>

        <div class="level-chart-item">
          <div class="level-title">班组长得分排名</div>
          <div class="level-chart-bg">
            <div ref="groupRankChart" class="level-chart"></div>
          </div>
        </div>

        <div class="level-chart-item">
          <div class="level-title">员工得分排名</div>
          <div class="level-chart-bg">
            <div ref="employeeRankChart" class="level-chart"></div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 成员积分详情表格 -->
    <el-card class="score-board" shadow="hover">
      <div slot="header" class="clearfix">
        <span>成员积分详情</span>
        <div class="filter-container">
          <el-cascader
            v-model="queryParams.department_id"
            clearable
            filterable
            :options="deptOptions"
            placeholder="所属部门"
            :show-all-levels="false"
            style="margin-right: 10px"
            @change="changeDept"
          />
          <el-select
            v-model="queryParams.level_id"
            clearable
            filterable
            placeholder="全部级别"
            size="small"
          >
            <el-option
              v-for="item in levelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-select
            v-model="queryParams.projectId"
            clearable
            filterable
            placeholder="全部项目"
            size="small"
          >
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.projectName"
              :value="item.id"
            />
          </el-select>
          <el-select
            v-model="queryParams.timeRange"
            placeholder="全部时间"
            size="small"
          >
            <el-option label="全部时间" value="all" />
            <el-option label="本周" value="week" />
            <el-option label="近30天" value="month" />
            <el-option label="本月" value="currentMonth" />
          </el-select>
          <el-button
            icon="el-icon-search"
            size="small"
            type="primary"
            @click="handleTableQuery"
          >
            查询
          </el-button>
          <el-button
            icon="el-icon-download"
            size="small"
            type="primary"
            @click="exportBoardData"
          >
            导出数据
          </el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        border
        :data="memberScoreList"
        style="width: 100%"
      >
        <el-table-column align="center" label="排名" prop="rank" width="80" />
        <el-table-column align="center" label="姓名" prop="name" />
        <el-table-column
          align="center"
          label="部门"
          prop="department"
          width="150"
        />
        <el-table-column align="center" label="级别" prop="level" />
        <el-table-column
          align="center"
          label="当前积分"
          prop="score"
          width="120"
        />
        <el-table-column align="center" label="变动" prop="change">
          <template slot-scope="scope">
            <span
              :class="
                scope.row.change > 0
                  ? 'increase'
                  : scope.row.change < 0
                  ? 'decrease'
                  : ''
              "
            >
              {{
                scope.row.change > 0 ? '+' + scope.row.change : scope.row.change
              }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        class="pagination"
        :current-page="pagination.currentPage"
        layout="total, sizes, prev, pager, next, jumper"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </el-card>
  </div>
</template>

<script>
  import * as echarts from 'echarts'
  import { mapGetters } from 'vuex'
  import { getProjectList } from '@/api/project/projectInfo'
  import {
    getProjectMemberList,
    exportProjectMemberList,
  } from '@/api/statistic/statistic'
  import { listSelectDepartments } from '@/api/system/dept'
  import { getDictItems } from '@/api/user'

  export default {
    name: 'ProjectScoreStatistic',
    data() {
      return {
        loading: false,
        // 查询参数
        queryParams: {
          timeRange: 'all',
          projectId: '',
        },
        // 表格查询参数
        tableQueryParams: {
          department: '',
          level: '',
        },
        // 项目列表
        projectList: [],
        deptOptions: [],
        levelList: [],
        // 项目积分数据
        projectScoreData: {
          totalScore: '24,850',
          avgScore: '994',
          highestScore: '1,015',
          lowestScore: '925',
        },
        // 图表相关
        activeChartTab: 'month',
        currentRankType: 'center',
        centerRankChart: null,
        teamRankChart: null,
        groupRankChart: null,
        employeeRankChart: null,
        // 成员积分列表
        memberScoreList: [],
        // 分页
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 5,
        },
        // 图表数据
        chartData: {
          center: {
            names: [],
            scores: [],
          },
          team: {
            names: [],
            scores: [],
          },
          group: {
            names: [],
            scores: [],
          },
          employee: {
            names: [],
            scores: [],
          },
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        projectId: 'user/projectId',
      }),
    },
    async mounted() {
      await this.goProjectList()
      this.fetchProjectData()
      this.loadDeptOptions()
      this.goDictItems()
      this.initCharts()
    },
    beforeDestroy() {
      this.disposeCharts()
      window.removeEventListener('resize', this.resizeCharts)
    },
    methods: {
      // 初始化所有图表
      initCharts() {
        this.$nextTick(() => {
          this.initCenterRankChart()
          this.initTeamRankChart()
          this.initGroupRankChart()
          this.initEmployeeRankChart()
          window.addEventListener('resize', this.resizeCharts)
        })
      },

      // 初始化物探队（中心）领导排名图表
      initCenterRankChart() {
        this.centerRankChart = echarts.init(this.$refs.centerRankChart)
        const option = this.getChartOption(
          this.chartData.center.names,
          this.chartData.center.scores,
          'center'
        )
        this.centerRankChart.setOption(option)
      },

      // 初始化工程队领导排名图表
      initTeamRankChart() {
        this.teamRankChart = echarts.init(this.$refs.teamRankChart)
        const option = this.getChartOption(
          this.chartData.team.names,
          this.chartData.team.scores,
          'team'
        )
        this.teamRankChart.setOption(option)
      },

      // 初始化班组长排名图表
      initGroupRankChart() {
        this.groupRankChart = echarts.init(this.$refs.groupRankChart)
        const option = this.getChartOption(
          this.chartData.group.names,
          this.chartData.group.scores,
          'group'
        )
        this.groupRankChart.setOption(option)
      },

      // 初始化员工排名图表
      initEmployeeRankChart() {
        this.employeeRankChart = echarts.init(this.$refs.employeeRankChart)
        const option = this.getChartOption(
          this.chartData.employee.names,
          this.chartData.employee.scores,
          'employee'
        )
        this.employeeRankChart.setOption(option)
      },

      // 获取图表配置
      getChartOption(names, scores, colorType) {
        // colorType: 'center' | 'team' | 'group' | 'employee'
        let gradient
        switch (colorType) {
          case 'center':
            gradient = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#A0C4FF' },
              { offset: 1, color: '#6495ED' },
            ])
            break
          case 'team':
            gradient = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#D1B3FF' },
              { offset: 1, color: '#9370DB' },
            ])
            break
          case 'group':
            gradient = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#B9FBC0' },
              { offset: 1, color: '#3CB371' },
            ])
            break
          case 'employee':
            gradient = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#FFD6A5' },
              { offset: 1, color: '#FF8C00' },
            ])
            break
          default:
            gradient = colorType
        }
        return {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: names,
            axisTick: {
              alignWithLabel: true,
            },
          },
          yAxis: {
            type: 'value',
            min: function (value) {
              return Math.floor(value.min * 0.95)
            },
          },
          series: [
            {
              name: '积分',
              type: 'bar',
              barWidth: 30,
              data: scores,
              itemStyle: {
                color: gradient,
                borderRadius: [8, 8, 0, 0],
              },
            },
          ],
        }
      },

      // 调整图表大小
      resizeCharts() {
        if (this.centerRankChart) this.centerRankChart.resize()
        if (this.teamRankChart) this.teamRankChart.resize()
        if (this.groupRankChart) this.groupRankChart.resize()
        if (this.employeeRankChart) this.employeeRankChart.resize()
      },

      // 销毁图表
      disposeCharts() {
        if (this.centerRankChart) {
          this.centerRankChart.dispose()
          this.centerRankChart = null
        }
        if (this.teamRankChart) {
          this.teamRankChart.dispose()
          this.teamRankChart = null
        }
        if (this.groupRankChart) {
          this.groupRankChart.dispose()
          this.groupRankChart = null
        }
        if (this.employeeRankChart) {
          this.employeeRankChart.dispose()
          this.employeeRankChart = null
        }
      },

      // 获取项目数据
      fetchProjectData() {
        this.loading = true

        // 构建请求参数
        const params = {
          ...this.queryParams,
          department_id:
            this.queryParams.department_id &&
            this.queryParams.department_id.length > 0
              ? this.queryParams.department_id[
                  this.queryParams.department_id.length - 1
                ]
              : undefined,
          project_id: this.queryParams.projectId,
          page_num: this.pagination.currentPage,
          page_size: this.pagination.pageSize,
        }

        // 根据时间范围设置开始和结束时间
        const currentDate = new Date()
        const currentYear = currentDate.getFullYear()
        let weekStart, monthStart, currMonthStart

        // 设置时间范围
        switch (this.queryParams.timeRange) {
          case 'week':
            // 本周
            weekStart = new Date(currentDate)
            weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1) // 周一
            weekStart.setHours(0, 0, 0, 0)

            params.start_time =
              weekStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'month':
            // 近30天
            monthStart = new Date(currentDate)
            monthStart.setDate(currentDate.getDate() - 29)
            monthStart.setHours(0, 0, 0, 0)

            params.start_time =
              monthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'currentMonth':
            // 本月
            currMonthStart = new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              1
            )
            currMonthStart.setHours(0, 0, 0, 0)

            params.start_time =
              currMonthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'all':
          default:
            // 全部时间（本年）
            params.start_time = `${currentYear}-01-01 00:00:00`
            params.end_time = `${currentYear}-12-31 23:59:59`
            break
        }

        // 调用接口获取数据
        getProjectMemberList(params)
          .then((res) => {
            this.loading = false
            const { code, data } = res

            if (code === 200) {
              // 更新分页信息
              this.pagination.total = data.total

              // 处理成员列表数据
              if (data.member_list && data.member_list.length > 0) {
                // 按积分排序
                const sortedList = [...data.member_list].sort(
                  (a, b) => b.score - a.score
                )

                // 添加排名
                this.memberScoreList = sortedList.map((item, index) => {
                  return {
                    rank: index + 1,
                    name: item.username,
                    department: item.department,
                    level: item.level,
                    score: item.score.toLocaleString(),
                    change: ((item.score || 0) - 1000).toFixed(0),
                  }
                })

                // 更新图表数据
                this.updateChartData(sortedList)

                // 更新项目积分概览数据
                this.updateProjectScoreData(sortedList)
              } else {
                this.memberScoreList = []
                // 更新图表数据
                this.updateChartData([])
                // 更新项目积分概览数据
                this.updateProjectScoreData([])
              }
            } else {
              this.$message.error('获取数据失败')
            }
          })
          .catch((error) => {
            this.loading = false
            console.error('获取数据出错:', error)
            this.$message.error('获取数据出错')
          })
      },

      // 更新图表数据
      updateChartData(memberList) {
        // 按级别分组
        const centerLeaders = memberList.filter(
          (item) => item.level === '物探队（中心）领导'
        )
        const teamLeaders = memberList.filter(
          (item) => item.level === '工程队领导'
        )
        const groupLeaders = memberList.filter(
          (item) => item.level === '班组长'
        )
        const employees = memberList.filter((item) => item.level === '员工')

        // 更新中心领导图表数据
        if (centerLeaders.length > 0) {
          const topCenterLeaders = centerLeaders.slice(0, 5)
          this.chartData.center = {
            names: topCenterLeaders.map((item) => item.username),
            scores: topCenterLeaders.map((item) => item.score),
          }
          if (this.centerRankChart) {
            this.centerRankChart.setOption(
              this.getChartOption(
                this.chartData.center.names,
                this.chartData.center.scores,
                'center'
              )
            )
          }
        } else {
          this.chartData.level.center = {
            names: [],
            scores: [],
          }
          if (this.centerLevelChart) {
            this.centerLevelChart.clear()
          }
        }

        // 更新工程队领导图表数据
        if (teamLeaders.length > 0) {
          const topTeamLeaders = teamLeaders.slice(0, 5)
          this.chartData.team = {
            names: topTeamLeaders.map((item) => item.username),
            scores: topTeamLeaders.map((item) => item.score),
          }
          if (this.teamRankChart) {
            this.teamRankChart.setOption(
              this.getChartOption(
                this.chartData.team.names,
                this.chartData.team.scores,
                'team'
              )
            )
          }
        } else {
          this.chartData.level.team = {
            names: [],
            scores: [],
          }
          if (this.teamLevelChart) {
            this.teamLevelChart.clear()
          }
        }

        // 更新班组长图表数据
        if (groupLeaders.length > 0) {
          const topGroupLeaders = groupLeaders.slice(0, 5)
          this.chartData.group = {
            names: topGroupLeaders.map((item) => item.username),
            scores: topGroupLeaders.map((item) => item.score),
          }
          if (this.groupRankChart) {
            this.groupRankChart.setOption(
              this.getChartOption(
                this.chartData.group.names,
                this.chartData.group.scores,
                'group'
              )
            )
          }
        } else {
          this.chartData.level.group = {
            names: [],
            scores: [],
          }
          if (this.groupLevelChart) {
            this.groupLevelChart.clear()
          }
        }

        // 更新员工图表数据
        if (employees.length > 0) {
          const topEmployees = employees.slice(0, 5)
          this.chartData.employee = {
            names: topEmployees.map((item) => item.username),
            scores: topEmployees.map((item) => item.score),
          }
          if (this.employeeRankChart) {
            this.employeeRankChart.setOption(
              this.getChartOption(
                this.chartData.employee.names,
                this.chartData.employee.scores,
                'employee'
              )
            )
          }
        } else {
          this.chartData.level.employee = {
            names: [],
            scores: [],
          }
          if (this.employeeLevelChart) {
            this.employeeLevelChart.clear()
          }
        }
      },

      // 更新项目积分概览数据
      updateProjectScoreData(memberList) {
        if (memberList.length === 0) return

        // 计算总积分
        const totalScore = memberList.reduce((sum, item) => sum + item.score, 0)

        // 计算平均积分
        const avgScore = Math.round(totalScore / memberList.length)

        // 获取最高和最低积分
        const highestScore = memberList[0].score
        const lowestScore = memberList[memberList.length - 1].score

        // 更新数据
        this.projectScoreData = {
          totalScore: totalScore.toLocaleString(),
          avgScore: avgScore.toLocaleString(),
          highestScore: highestScore.toLocaleString(),
          lowestScore: lowestScore.toLocaleString(),
        }
      },

      // 处理查询
      handleQuery() {
        this.fetchProjectData()
      },

      // 处理表格查询
      handleTableQuery() {
        this.pagination.currentPage = 1
        this.fetchProjectData()
      },

      // 处理图表标签切换
      handleChartTabChange(tab) {
        console.log(tab)
        // 根据选择的时间范围更新图表数据
        this.fetchProjectData()

        // 重新创建所有图表
        this.$nextTick(() => {
          // 先销毁当前图表
          this.disposeCharts()
          // 重新初始化所有图表
          this.initCenterRankChart()
          this.initTeamRankChart()
          this.initGroupRankChart()
          this.initEmployeeRankChart()
        })
      },

      // 处理排名类型切换
      handleRankTypeChange() {
        this.$nextTick(() => {
          // 先销毁当前图表
          this.disposeCharts()

          // 根据当前选择的类型重新初始化对应图表
          switch (this.currentRankType) {
            case 'center':
              this.initCenterRankChart()
              break
            case 'team':
              this.initTeamRankChart()
              break
            case 'group':
              this.initGroupRankChart()
              break
            case 'employee':
              this.initEmployeeRankChart()
              break
            default:
              break
          }
        })
      },

      // 导出数据
      exportData() {
        this.$message({
          message: '数据导出功能正在开发中',
          type: 'info',
        })
      },

      // 分页大小变化
      handleSizeChange(val) {
        this.pagination.pageSize = val
        this.fetchProjectData()
      },

      // 当前页变化
      handleCurrentChange(val) {
        this.pagination.currentPage = val
        this.fetchProjectData()
      },

      goProjectList() {
        try {
          getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.projectList = data
              this.$set(
                this.queryParams,
                'projectId',
                data[0].id || this.storeProjectId
              )
              this.goProjectCount()
            } else {
              this.goProjectCount()
            }
          })
        } catch (error) {
          this.goProjectCount()
        }
      },
      async loadDeptOptions() {
        listSelectDepartments().then((response) => {
          this.deptOptions = response.data
        })
      },
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'project_position',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.levelList = res.data.data.list || []
      },
      changeDept(e) {
        console.log(e)
      },
      exportBoardData() {
        const params = {
          ...this.queryParams,
          department_id:
            this.queryParams.department_id &&
            this.queryParams.department_id.length > 0
              ? this.queryParams.department_id[
                  this.queryParams.department_id.length - 1
                ]
              : undefined,
          project_id: this.queryParams.projectId,
          page_num: this.pagination.currentPage,
          page_size: this.pagination.pageSize,
          export: true,
        }
        // 根据时间范围设置开始和结束时间
        const currentDate = new Date()
        const currentYear = currentDate.getFullYear()
        let weekStart, monthStart, currMonthStart

        // 设置时间范围
        switch (this.queryParams.timeRange) {
          case 'week':
            // 本周
            weekStart = new Date(currentDate)
            weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1) // 周一
            weekStart.setHours(0, 0, 0, 0)

            params.start_time =
              weekStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'month':
            // 近30天
            monthStart = new Date(currentDate)
            monthStart.setDate(currentDate.getDate() - 29)
            monthStart.setHours(0, 0, 0, 0)

            params.start_time =
              monthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'currentMonth':
            // 本月
            currMonthStart = new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              1
            )
            currMonthStart.setHours(0, 0, 0, 0)

            params.start_time =
              currMonthStart.toISOString().split('T')[0] + ' 00:00:00'
            params.end_time =
              currentDate.toISOString().split('T')[0] + ' 23:59:59'
            break

          case 'all':
          default:
            // 全部时间（本年）
            params.start_time = `${currentYear}-01-01 00:00:00`
            params.end_time = `${currentYear}-12-31 23:59:59`
            break
        }
        exportProjectMemberList(params).then((response) => {
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
          })
          const a = document.createElement('a')
          const href = window.URL.createObjectURL(blob)
          a.href = href
          const date = new Date().getTime()
          a.setAttribute('download', `成员积分${date}.xlsx`)
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(href)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 20px;
  }

  .score-overview {
    margin-bottom: 20px;

    .score-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 120px;

      .score-item {
        text-align: center;

        .score-value {
          margin-bottom: 10px;
          font-size: 28px;
          font-weight: bold;
        }

        .score-label {
          font-size: 14px;
          color: #606266;
        }
      }

      .total-score .score-value {
        color: #409eff;
      }

      .avg-score .score-value {
        color: #67c23a;
      }

      .highest-score .score-value {
        color: #e6a23c;
      }

      .lowest-score .score-value {
        color: #f56c6c;
      }
    }
  }

  .chart-container {
    margin-bottom: 20px;

    .chart-tabs {
      float: right;
    }

    .level-charts {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .level-chart-item {
        .level-title {
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: bold;
          color: #303133;
        }

        .level-chart-bg {
          padding: 10px;
          background-color: #f9f9f9;
          border-radius: 4px;

          .level-chart {
            width: 100%;
            height: 300px;
          }
        }
      }
    }
  }

  .rank-type-tabs {
    margin-bottom: 20px; /* 改为底部margin */
  }

  .rank-chart-container {
    margin-top: 20px;

    .rank-title {
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }

    .rank-chart {
      width: 100%;
      height: 400px;
    }
  }

  .score-board {
    .filter-container {
      float: right;

      .el-select {
        margin-right: 10px;
      }
    }

    .increase {
      color: #67c23a;
    }

    .decrease {
      color: #f56c6c;
    }

    .pagination {
      margin-top: 20px;
      text-align: right;
    }
  }

  .clearfix {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
  }
</style>
