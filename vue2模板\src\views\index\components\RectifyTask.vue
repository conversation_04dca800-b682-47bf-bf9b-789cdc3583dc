<template>
  <div class="rectify-task">
    <div
      class="rectify-container"
      style="background: #f7f9fb; border-radius: 16px"
    >
      <div>
        <div
          v-if="list && list.length !== 0"
          class="table-body"
          style="height: 290px; overflow-x: auto; overflow-y: hidden"
        >
          <el-table
            v-loading="loading"
            :data="list"
            :show-header="false"
            style="width: 100%"
          >
            <el-table-column
              label="标题"
              prop="issueDescription"
              show-overflow-tooltip
            />
            <el-table-column label="创建时间" prop="createdAt" width="180" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="text"
                  @click="handleRectify(scope.row)"
                >
                  整改
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div
          v-else
          class="empty-container"
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 290px;
          "
        >
          <div
            class="text item"
            style="
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;
            "
          >
            <el-image
              class="vab-data-emptys"
              :src="require('@/assets/empty_images/data_empty.png')"
              style="max-width: 200px; max-height: 200px"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 整改详情 -->
    <CorrectionReceipt ref="correctionReceipt" @refreshDataList="getList" />
  </div>
</template>

<script>
  import CorrectionReceipt from '@/views/question/issuesList/components/CorrectionReceipt.vue'
  import { getRectificationById } from '@/api/question/issuesList'
  export default {
    name: 'RectifyTask',
    components: {
      CorrectionReceipt,
    },
    props: {
      loading: {
        type: Boolean,
        default: false,
      },
      list: {
        type: Array,
        default: () => [],
      },
    },
    methods: {
      handleRectify(row) {
        getRectificationById(row.id).then((res) => {
          if (res.code === 200) {
            this.$refs.correctionReceipt.showReceipt(res.data)
          }
        })
        // this.$emit('rectify', row)
      },
      moreRectify() {
        this.$router.push({
          path: '/question/issuesList',
          query: { type: 'pending' },
        })
      },
    },
  }
</script>

<style scoped>
  .rectify-container {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .table-body /deep/ .el-table__row {
    cursor: pointer;
  }
</style>
