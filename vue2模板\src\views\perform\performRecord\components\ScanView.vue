<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '扫码履职检查单详情'"
      :visible.sync="dialogFormVisible"
      width="1000px"
      @close="close"
    >
      <el-form ref="form" disabled label-width="140px" :model="form">
        <el-row>
          <el-col :span="12">
            <el-form-item label="身份认证：" prop="idCard">
              <div
                v-if="
                  form.idcardImgsAttach &&
                  form.idcardImgsAttach.attachInfoList &&
                  form.idcardImgsAttach.attachInfoList.length
                "
              >
                <div
                  v-for="(item, index) in form.idcardImgsAttach.attachInfoList"
                  :key="index"
                >
                  <el-image
                    v-if="['img', 'jpg', 'jpeg', 'png'].includes(item.platform)"
                    lazy
                    :preview-src-list="[item.url]"
                    :src="item.url"
                    style="width: 50%"
                  />
                  <el-tag v-else @click="handlePreview(item.url)">
                    {{ item.name }}
                  </el-tag>
                </div>
              </div>
              <div v-else>无身份认证图片</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定位：" prop="location">
              {{ form.location }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="人员：" prop="userId">
              {{ form.userName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称：" prop="projectId">
              <el-select
                v-model="form.projectId"
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.id"
                  :label="item.projectName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="岗位：" prop="positionName">
              {{ form.positionName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="级别：" prop="level">
              {{ form.level }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="履职检查项：" prop="templatesInfoId">
              <el-select
                v-model="form.templatesInfoId"
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in dutyList"
                  :key="item.id"
                  :label="item.dutyInspectionItem"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="履职日期：" prop="dutyDate">
              {{ form.dutyDate }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="检查设备/场所："
              prop="inspectedEquipmentOrSite"
            >
              <el-select
                v-model="form.inspectedEquipmentOrSite"
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in siteList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="设备/场所负责人："
              prop="equipmentOrSiteResponsiblePerson"
            >
              <el-select
                v-model="form.equipmentOrSiteResponsiblePerson"
                placeholder="请选择"
                style="width: 96%"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查类型：" prop="inspectionType">
              <el-select
                v-model="form.inspectionType"
                placeholder="请选择检查类型"
                style="width: 96%"
              >
                <el-option
                  v-for="item in safeDictList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="检查内容：" prop="inspectionContent">
              <el-input
                v-model="form.inspectionContent"
                placeholder="检查内容"
                style="width: 96%"
              />
            </el-form-item> -->
            <el-form-item label="检查内容：" prop="inspectionContent">
              <el-select
                v-model="form.inspectionContent"
                clearable
                disabled
                filterable
                placeholder="请选择"
                style="width: 96%"
                @change="changeInspection"
              >
                <el-option
                  v-for="item in inspectionContentData"
                  :key="item.id"
                  :label="item.inspectionContent"
                  :value="item.id"
                />
              </el-select>
              <!-- <el-input v-model="form.inspectionContent" disabled /> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <div class="inspectionContent" style="margin: 10px 0">
            检查内容明细
          </div>
          <el-table
            border
            :data="inspectionContentList"
            style="margin-bottom: 20px"
          >
            <el-table-column
              align="center"
              label="明细要求"
              prop="inspectionRequirements"
            />
            <el-table-column
              align="center"
              label="检查项"
              prop="inspectionItem"
            />
          </el-table>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否合格：" prop="qualified">
              {{ form.qualified ? '合格' : '不合格' }}
            </el-form-item>
          </el-col>
          <el-col v-if="!form.qualified" :span="12">
            <el-form-item label="要求整改时间：" prop="requiredCompletionTime">
              {{
                form?.issueFeedbackAndRectificationDto?.requiredCompletionTime
              }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!form.qualified">
          <el-col :span="12">
            <el-form-item label="检查问题描述：" prop="issueDescription">
              {{ form?.issueFeedbackAndRectificationDto?.issueDescription }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改人：" prop="feedbackPersonId">
              {{ form?.issueFeedbackAndRectificationDto?.feedbackPersonName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!form.qualified">
          <el-col :span="12">
            <el-form-item
              class="logo"
              label="现场照片或视频："
              prop="onSitePhotosOrVideos"
            >
              <div
                v-if="
                  form.issueFeedbackAndRectificationDto &&
                  form.issueFeedbackAndRectificationDto
                    .onSitePhotosOrVideosAttach &&
                  form.issueFeedbackAndRectificationDto
                    .onSitePhotosOrVideosAttach.attachInfoList &&
                  form.issueFeedbackAndRectificationDto
                    .onSitePhotosOrVideosAttach.attachInfoList.length
                "
              >
                <div
                  v-for="(item, index) in form.issueFeedbackAndRectificationDto
                    .onSitePhotosOrVideosAttach.attachInfoList"
                  :key="index"
                >
                  <el-image
                    v-if="['img', 'jpg', 'jpeg', 'png'].includes(item.platform)"
                    lazy
                    :preview-src-list="[item.url]"
                    :src="item.url"
                    style="width: 50%"
                  />
                  <el-tag v-else @click="handlePreview(item.url)">
                    {{ item.name }}
                  </el-tag>
                </div>
              </div>
              <div v-else>无现场照片或视频</div>
            </el-form-item>
          </el-col>
          <el-col :span="12" />
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="close">关 闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { getDictItems } from '@/api/user'
  import { getProjectList } from '@/api/project/projectInfo'
  import { getEquipmentSitesList } from '@/api/system/facilities'
  import { getUserDutyDetailsById } from '@/api/perform/userResponsibility'
  import { getSafetyInspectionStandardsList } from '@/api/perform/safetyCheck'
  export default {
    name: 'ScanView',
    props: {
      dutyList: {
        type: Array,
        default: () => [],
      },
      processList: {
        type: Array,
        default: () => [],
      },
      frequencyList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          qualified: true,
        },
        inspectionContentList: [],
        title: '',
        dialogFormVisible: false,
        projectList: [],
        safeDictList: [],
        siteList: [],
        inspectionContentData: [],
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
    },
    watch: {
      inspectionContentList: {
        handler(val) {
          if (val.length > 0 && this.form.inspectionContent) {
            const obj = this.inspectionContentList.find(
              (item) => item.id == this.form.inspectionContent
            )
            if (obj) {
              this.inspectionInfoDtoList = obj.safetyInspectionInfoDtoList || []
            }
          }
        },
        immediate: true,
      },
    },
    methods: {
      showView(row) {
        this.title = ''
        if (row) {
          this.getDateils(row)
        }
        this.dialogFormVisible = true
        this.goProjectList()
        this.goDictItems()
        this.goSiteList()
      },
      getDateils(row) {
        getUserDutyDetailsById(row.id).then((res) => {
          if (res.data.dutyListInfo) {
            res.data.dutyListInfo.id = undefined
          }
          this.form = {
            ...res.data,
            ...res.data.dutyListInfo,
            templatesInfoId: this.form.dutyInspectionItemCont?.id,
          }
          this.form.inspectionType = res.data.inspectionType
            ? res.data.inspectionType + ''
            : ''
          this.form.inspectionContent = res.data.inspectionContent
            ? res.data.inspectionContent * 1
            : null
          this.form.inspectedEquipmentOrSite = res.data.inspectedEquipmentOrSite
            ? res.data.inspectedEquipmentOrSite * 1
            : null
          this.form.equipmentOrSiteResponsiblePerson = res.data
            .equipmentOrSiteResponsiblePerson
            ? res.data.equipmentOrSiteResponsiblePerson * 1
            : null
          try {
            if (
              this.form.safetyInspectionInfoDtoList &&
              typeof this.form.safetyInspectionInfoDtoList === 'string'
            ) {
              this.inspectionContentList = JSON.parse(
                this.form.safetyInspectionInfoDtoList
              )
            } else if (Array.isArray(this.form.safetyInspectionInfoDtoList)) {
              this.inspectionContentList = this.form.safetyInspectionInfoDtoList
            } else {
              this.inspectionContentList = []
            }
            console.log('检查内容列表:', this.inspectionContentList)
          } catch (e) {
            console.error('解析检查内容列表失败:', e)
            this.inspectionContentList = []
          }
          if (res.data.inspectionType) {
            this.changeDutyTemplate(res.data.inspectionType)
          }
        })
      },
      close() {
        this.dialogFormVisible = false
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      goSiteList() {
        getEquipmentSitesList({ pageNum: 1, pageSize: 1000 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.siteList = data
          }
        })
      },
      // 获取类型字典
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'safe_check_type',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.safeDictList = res.data.data.list || []
      },
      async changeDutyTemplate(e) {
        try {
          const res = await getSafetyInspectionStandardsList({
            pageNum: 1,
            pageSize: 9999,
            inspectionType: e,
          })
          if (res.code === 200) {
            this.inspectionContentData = res.data || []
          }
        } catch (error) {
          console.error('获取检查内容列表失败:', error)
          this.$message.error('获取检查内容列表失败')
        }
      },
      isImage(filename) {
        if (!filename) return false
        const ext = filename.split('.').pop().toLowerCase()
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)
      },
      // 新的标签页
      handlePreview(url) {
        window.open(url, '_blank')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .file-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .preview-image {
      width: 80px;
      height: 80px;
      margin-right: 10px;
      object-fit: cover;
    }

    a {
      margin-left: 10px;
      color: #409eff;
    }
  }
</style>
