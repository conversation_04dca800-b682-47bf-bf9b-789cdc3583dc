.DS_Store
node_modules
node_modules.nosync
/dist

# local env files
.env.local
.env.*.local

# Log files
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Lock files
yarn.lock
pnpm-lock.yaml
package-lock.json

# Yarn v2 not using using Zero-Installs
.yarn/*
#!.yarn/cache
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Vab
public/video
*.zip
*.7z
*.rar
src/vab/styles/themes/red.scss
/src/vab/styles/themes/red-black.scss
/src/vab/styles/themes/red-white.scss
/.history