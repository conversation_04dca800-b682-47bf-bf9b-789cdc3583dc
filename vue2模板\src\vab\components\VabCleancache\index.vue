<template>
  <vab-icon icon="brush-3-line" title="清除缓存" @click="cleancache" />
</template>

<script>
  import { cleanCache } from '@/api/systemConfig'
  export default {
    name: 'VabCleancache',
    computed: {},
    methods: {
      async cleancache() {
        await cleanCache()
        this.$baseMessage('操作成功！', 'success')
        this.$baseEventBus.$emit('reload-router-view')
      },
    },
  }
</script>
