<!-- 安全检查标准库 -->
<template>
  <div class="app-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item>
        <el-button icon="el-icon-plus" type="success" @click="handleAdd">
          新增
        </el-button>
        <el-button
          :disabled="single"
          icon="el-icon-delete"
          type="danger"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-form-item>
      <!-- <el-form-item>
        <el-dropdown
          split-button
          style="margin-left: 0px"
          @command="handleDropdown"
        >
          导入
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                command="handleDownloadTemplate"
                icon="el-icon-download"
              >
                下载模板
              </el-dropdown-item>
              <el-dropdown-item command="showImportDialog" icon="el-icon-top">
                导入数据
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-form-item> -->
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column
        align="center"
        label="履职检查项"
        prop="dutyInspectionItem"
      />
      <el-table-column align="center" label="创建时间" prop="createdAt" />
      <el-table-column align="center" label="是否停用" prop="disabled">
        <template #default="scope">
          <el-tag v-if="scope.row.disabled" type="danger">是</el-tag>
          <el-tag v-else type="success">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="140">
        <template #default="scope">
          <el-button
            circle
            icon="el-icon-edit-outline"
            plain
            type="primary"
            @click.stop="handleUpdate(scope.row)"
          />
          <!-- <el-button
            circle
            icon="el-icon-plus"
            plain
            type="success"
            @click.stop="handleAdd(scope.row)"
          /> -->
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页工具条 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <Details ref="details" @refreshDataList="getList" />
  </div>
</template>

<script>
  import Details from './details.vue'
  import {
    getDutyInspectionItemsList,
    delDutyInspectionItems,
  } from '@/api/perform/performItems'
  export default {
    name: 'PerformItems',
    components: {
      Details,
    },
    data() {
      return {
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          // name: '',
        },
        total: 0,
        dataList: [],
        tableData: [], // 用于存储处理后的表格数据
        loading: false,
        selectionList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        single: true,
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      async getList() {
        this.loading = true
        try {
          const res = await getDutyInspectionItemsList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            // this.dataList = data
            this.tableData = data
            this.total = page.totalCount
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      handleAdd() {
        this.$refs.details.showEdit()
      },
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
      handleDelete(row) {
        let idstr = ''
        if (row) {
          idstr = row.id
        } else {
          const ids = this.selectionList.map((item) => item.id)
          if (ids.length === 0) {
            this.$message.warning('请选择要删除的记录')
            return
          }
          idstr = ids.join(',')
        }
        console.log(idstr)

        this.$confirm('确认删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          delDutyInspectionItems({ ids: idstr }).then(() => {
            this.$message.success('删除成功')
            this.single = true
            this.getList()
          })
        })
      },
      handleQuery() {
        this.getList()
      },
      resetQuery() {
        this.$refs.queryFormRef.resetFields()
        this.getList()
      },
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length ? false : true
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.handleQuery()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.handleQuery()
      },
    },
  }
</script>

<style lang="scss" scoped></style>
