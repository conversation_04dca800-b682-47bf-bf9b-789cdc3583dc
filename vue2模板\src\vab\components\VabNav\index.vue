<template>
  <div class="vab-nav">
    <el-row :gutter="15">
      <el-col :lg="9" :md="9" :sm="9" :xl="9" :xs="4">
        <div class="left-panel">
          <vab-fold v-if="layout !== 'float'" />
          <el-tabs
            v-if="layout === 'comprehensive'"
            v-model="extra.first"
            tab-position="top"
            @tab-click="handleTabClick"
          >
            <template v-for="(route, index) in handleRoutes">
              <el-tab-pane :key="index + route.name" :name="route.name">
                <span slot="label">
                  <vab-icon
                    v-if="route.meta.icon"
                    :icon="route.meta.icon"
                    :is-custom-svg="route.meta.isCustomSvg"
                    style="min-width: 16px"
                  />
                  {{ translateTitle(route.meta.title) }}
                </span>
              </el-tab-pane>
            </template>
          </el-tabs>
          <vab-breadcrumb v-else class="hidden-xs-only" />
        </div>
      </el-col>
      <el-col :lg="6" :md="6" :sm="6" :xl="6" :xs="4">
        <div class="center-panel">
          <el-select
            v-show="isSelected"
            v-model="projectId"
            placeholder="请选择项目"
            style="width: 100%"
            @change="handleProjectChange"
          >
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.projectName"
              :value="item.id"
            />
          </el-select>
        </div>
      </el-col>
      <el-col :lg="9" :md="9" :sm="9" :xl="9" :xs="16">
        <div class="right-panel">
          <vab-error-log />
          <vab-search />

          <vab-cleancache />
          <!-- <vab-notice /> -->
          <vab-full-screen />
          <vab-language />
          <vab-theme />
          <vab-refresh />
          <vab-avatar />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { translateTitle } from '@/utils/i18n'
  import { mapGetters, mapActions } from 'vuex'
  import { openFirstMenu } from '@/config'
  import { getProjectList } from '@/api/project/projectInfo'

  export default {
    name: 'VabNav',
    props: {
      layout: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        firstMenu: '',
        projectList: [],
        isSelected: false,
        routerLIst: [
          // '/perform/userResponsibility',
          // '/perform/performRecord',
          // '/question/issuesList',
        ],
      }
    },
    computed: {
      ...mapGetters({
        extra: 'settings/extra',
        routes: 'routes/routes',
        storeProjectId: 'user/projectId',
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
      handleRoutes() {
        return this.routes.filter(
          (route) => route.meta && route.meta.hidden !== true
        )
      },
      handleActiveMenu() {
        return this.routes.find((route) => route.name === this.extra.first)
      },
      handlePartialRoutes() {
        const activeMenu = this.handleActiveMenu
        return activeMenu ? activeMenu.children : []
      },
    },
    watch: {
      $route: {
        handler(route) {
          const firstMenu = route.matched[0].name
          if (this.extra.first !== firstMenu) {
            this.extra.first = firstMenu
            this.handleTabClick(true)
          }
          if (this.routerLIst.includes(route.fullPath)) {
            this.isSelected = true
            this.goProjectList()
          } else {
            this.isSelected = false
          }
        },
        immediate: true,
      },
    },
    methods: {
      translateTitle,
      ...mapActions({
        setProjectId: 'user/setProjectId',
      }),
      handleTabClick(handler) {
        if (handler !== true && openFirstMenu) {
          const { redirect } = this.handleActiveMenu
          this.$router.push(redirect ? redirect : this.handleActiveMenu)
        }
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data || []
            if (!this.projectId && this.projectList.length > 0) {
              this.projectId = data[0].id
              this.setProjectId(this.projectId)
            }
          }
        })
      },
      handleProjectChange(val) {
        this.setProjectId(val)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .vab-nav {
    position: relative;
    height: $base-nav-height;
    padding-right: $base-padding;
    padding-left: $base-padding;
    overflow: hidden;
    user-select: none;
    background: $base-color-white;
    box-shadow: $base-box-shadow;

    .left-panel {
      display: flex;
      align-items: center;
      justify-items: center;
      height: $base-nav-height;

      :deep() {
        .fold-unfold {
          margin-right: $base-margin;
        }

        .el-tabs {
          width: 100%;
          margin-left: $base-margin;

          .el-tabs__header {
            margin: 0;

            > .el-tabs__nav-wrap {
              display: flex;
              align-items: center;

              .el-icon-arrow-left,
              .el-icon-arrow-right {
                font-weight: 600;
                color: $base-color-grey;
              }
            }
          }

          .el-tabs__item {
            text-align: center;

            > div {
              display: flex;
              align-items: center;

              i {
                margin-right: 3px;
              }
            }
          }
        }

        .el-tabs__nav-wrap::after {
          display: none;
        }
      }
    }

    .center-panel {
      display: flex;
      align-items: center;
      justify-content: center;
      height: $base-nav-height;
    }

    .right-panel {
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: flex-end;
      height: $base-nav-height;

      :deep() {
        [class*='ri-'] {
          margin-left: $base-margin;
          color: $base-color-grey;
          cursor: pointer;
        }

        button {
          [class*='ri-'] {
            margin-left: 0;
            color: $base-color-white;
            cursor: pointer;
          }
        }
      }
    }
  }
</style>
