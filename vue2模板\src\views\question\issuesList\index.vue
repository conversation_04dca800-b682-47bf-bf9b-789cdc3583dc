<!-- 履职反馈 -->
<template>
  <div class="app-container">
    <!-- 查询区域 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item label="项目名称" prop="associatedProjectId">
        <el-select
          v-model="queryParams.associatedProjectId"
          clearable
          filterable
          placeholder="请选择"
          @change="goUserByProject"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="userByProjectList && userByProjectList.length && isAdmin"
        label="问题责任人"
        prop="rectifierId"
      >
        <el-select
          v-model="queryParams.rectifierId"
          clearable
          filterable
          placeholder="请选择"
          style="width: 200px"
          @change="changeUser"
        >
          <el-option
            v-for="item in userByProjectList"
            :key="item.employeeId"
            :label="item.employeeName"
            :value="item.employeeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="整改审核人" prop="rectificationReviewerId">
        <el-select
          v-model="queryParams.rectificationReviewerId"
          clearable
          filterable
          placeholder="请选择"
          style="width: 200px"
          @change="changeUser"
        >
          <el-option
            v-for="item in storeUserList"
            :key="item.userId"
            :label="item.nickname"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="部门" prop="deptIds">
        <el-cascader
          v-model="deptIdArray"
          clearable
          filterable
          :options="deptList"
          placeholder="所属部门"
          :props="{ checkStrictly: true, multiple: true, emitPath: false }"
          :show-all-levels="false"
          style="width: 96%"
          @change="handleDeptChange"
        />
      </el-form-item>
      <el-form-item label="问题描述" prop="issueDescription">
        <el-input
          v-model="queryParams.issueDescription"
          clearable
          placeholder="请输入问题描述"
          style="width: 200px"
        />
      </el-form-item>
      <!-- <el-form-item label="时间段" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          style="width: 300px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item> -->
      <el-form-item label="状态" prop="currentStatus">
        <el-select
          v-model="queryParams.currentStatus"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option label="暂存" :value="-1" />
          <el-option label="待处理" :value="0" />
          <el-option label="待审核" :value="1" />
          <el-option label="已解决" :value="2" />
          <el-option label="已驳回" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">
          查询
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button
          icon="el-icon-download"
          style="margin-left: 12px"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格筛选按钮 -->
    <div class="table-operations">
      <el-dropdown @command="handleColumnFilter">
        <el-tag style="cursor: pointer">
          <i class="el-icon-s-operation"></i>
          筛选
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-tag>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="item in columnOptions"
            :key="item.prop"
            :command="item.prop"
          >
            <el-checkbox v-model="item.visible" @click.stop>
              {{ item.label }}
            </el-checkbox>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      row-key="id"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="getColumnVisible('dutyListName')"
        align="center"
        label="清单名称"
        prop="dutyListName"
        width="260px"
      />
      <el-table-column
        v-if="getColumnVisible('issueDescription')"
        align="center"
        label="问题描述"
        prop="issueDescription"
      />
      <el-table-column
        v-if="getColumnVisible('rectifierName')"
        align="center"
        label="问题责任人"
        prop="rectifierName"
      />
      <el-table-column
        v-if="getColumnVisible('rectificationReviewerName')"
        align="center"
        label="整改审核人"
        prop="rectificationReviewerName"
      />
      <!-- <el-table-column align="center" label="部门" prop="" /> -->
      <el-table-column
        v-if="getColumnVisible('associatedProject')"
        align="center"
        label="项目"
        prop="associatedProject"
      />
      <el-table-column
        v-if="getColumnVisible('issueGenerationDate')"
        align="center"
        label="问题生成日期"
        prop="issueGenerationDate"
      />
      <el-table-column
        v-if="getColumnVisible('requiredCompletionTime')"
        align="center"
        label="截止日期"
        prop="requiredCompletionTime"
      />
      <el-table-column
        v-if="getColumnVisible('currentStatus')"
        align="center"
        label="状态"
        prop="currentStatus"
      >
        <template #default="scope">
          <el-tag v-if="scope.row.currentStatus === 0" type="warning">
            待处理
          </el-tag>
          <el-tag v-if="scope.row.currentStatus === 1">待审核</el-tag>
          <el-tag v-else-if="scope.row.currentStatus === 2" type="success">
            已解决
          </el-tag>
          <el-tag v-else-if="scope.row.currentStatus === 3" type="danger">
            已驳回
          </el-tag>
          <el-tag v-else-if="scope.row.currentStatus === -1" type="info">
            暂存
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" prop="type">
        <template #default="scope">
          <el-button type="text" @click="handleUpdate(scope.row)">
            查看
          </el-button>
          <el-button
            v-if="
              userId == scope.row.rectificationReviewerId &&
              scope.row.currentStatus === 1
            "
            type="text"
            @click="goHome(scope.row)"
          >
            首页审批
          </el-button>
          <el-button
            v-if="
              scope.row.currentStatus === 0 || scope.row.currentStatus === 3
            "
            type="text"
            @click="handleReceipt(scope.row)"
          >
            整改
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <Details ref="details" @refreshDataList="getList" />
    <!-- 整改详情 -->
    <CorrectionReceipt ref="correctionReceipt" @refreshDataList="getList" />
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { getRectifierList } from '@/api/question/issuesList'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  import { getDictItems } from '@/api/user'
  import Details from './details.vue'
  import CorrectionReceipt from './components/CorrectionReceipt.vue'
  import {
    getRectificationById,
    exportIssuesList,
  } from '@/api/question/issuesList'
  import { listSelectDepartments } from '@/api/system/dept'
  import { getProjectList, getUserByProject } from '@/api/project/projectInfo'
  export default {
    name: 'IssuesList',
    components: {
      Details,
      CorrectionReceipt,
    },
    data() {
      return {
        // 遮罩层
        loading: false,
        // 选中数组
        selectionList: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 责任清单表格数据
        dataList: [],
        tableData: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          dutyListName: undefined,
          associatedProjectId: undefined,
          issueDescription: undefined,
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          dataTitle: [
            { required: true, message: '数据标题不能为空', trigger: 'blur' },
          ],
          deptName: [
            { required: true, message: '人员单位不能为空', trigger: 'blur' },
          ],
          projectName: [
            { required: true, message: '项目信息不能为空', trigger: 'blur' },
          ],
        },
        // 添加列筛选配置
        columnOptions: [
          {
            label: '清单名称',
            prop: 'dutyListName',
            visible: true,
          },
          {
            label: '问题描述',
            prop: 'issueDescription',
            visible: true,
          },
          {
            label: '问题责任人',
            prop: 'rectifierName',
            visible: true,
          },
          {
            label: '整改审核人',
            prop: 'rectificationReviewerName',
            visible: true,
          },
          {
            label: '项目',
            prop: 'associatedProject',
            visible: true,
          },
          {
            label: '问题生成日期',
            prop: 'issueGenerationDate',
            visible: true,
          },
          {
            label: '截止日期',
            prop: 'requiredCompletionTime',
            visible: true,
          },
          {
            label: '状态',
            prop: 'currentStatus',
            visible: true,
          },
        ],
        dutyList: [],
        processList: [],
        frequencyList: [],
        projectList: [],
        userByProjectList: [],
        isAdmin: false,
        timeRange: [], // 时间范围
        deptIdArray: [], // 用于存储级联选择器的值
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        userInfo: (state) => state.user.user,
        storeUserList: (state) => state.user.userList,
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
    },
    // watch: {
    //   storeProjectId: {
    //     handler(newVal) {
    //       this.queryParams.associatedProjectId = newVal
    //       this.getList()
    //     },
    //   },
    // },
    async mounted() {
      const { roles } = this.userInfo
      const isAdmin = roles.includes('root', 'admin')
      this.isAdmin = isAdmin
      this.$set(
        this.queryParams,
        'rectifierId',
        isAdmin ? undefined : undefined
      )
      // this.queryParams.associatedProjectId = this.storeProjectId
      await this.goProjectList()
      this.getList()
      this.getDutyList()
      this.getDeptList()
      this.goDictItems('project_state', 'process')
      this.goDictItems('project_frequency', 'frequency')
    },
    methods: {
      /** 查询责任清单列表 */
      async getList() {
        this.loading = true
        // this.queryParams.associatedProjectId = this.storeProjectId
        // this.queryParams.rectifierId = this.userId
        try {
          // if (!this.queryParams.associatedProjectId) return

          const res = await getRectifierList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            this.tableData = data
            this.total = page.totalCount
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      getDeptList() {
        // 从API获取部门列表
        listSelectDepartments().then((response) => {
          this.deptList = response.data || []
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        // 处理时间范围
        if (this.timeRange && this.timeRange.length === 2) {
          this.queryParams.queryStartTime = this.timeRange[0] + ' 00:00:00'
          this.queryParams.queryEndTime = this.timeRange[1] + ' 23:59:59'
        } else {
          this.queryParams.queryStartTime = undefined
          this.queryParams.queryEndTime = undefined
        }
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.$refs.queryFormRef.resetFields()
        if (this.isAdmin) {
          this.queryParams.rectifierId = undefined
        } else {
          this.queryParams.rectifierId = undefined
        }
        this.timeRange = []
        this.userByProjectList = []
        this.deptIdArray = []
        this.queryParams.rectifierDeptIdArray = undefined
        // this.queryParams.associatedProjectId = this.storeProjectId
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      // 查看
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
      // 首页审批
      goHome(row) {
        this.$router.push({
          path: '/index',
          query: {
            id: row.id,
          },
        })
      },
      // 整改
      handleReceipt(row) {
        getRectificationById(row.id).then((res) => {
          if (res.code === 200) {
            this.$refs.correctionReceipt.showReceipt(res.data)
          }
        })
      },
      // 获取类型字典
      async goDictItems(type, str) {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: type,
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        if (str === 'process') {
          this.processList = res.data.data.list || []
        } else {
          this.frequencyList = res.data.data.list || []
        }
      },
      async getDutyList() {
        const res = await getDutyInspectionItemsList({
          pageNum: 1,
          pageSize: 9999,
        })
        const { code, data } = res

        if (code === 200) {
          this.dutyList = data
        }
      },
      handleColumnFilter(command) {
        console.log(command)
        return
      },
      getColumnVisible(prop) {
        const column = this.columnOptions.find((item) => item.prop === prop)
        return column ? column.visible : true
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
            // this.queryParams.projectId =
            //   this.storeProjectId || data[0].projectId
          }
        })
      },
      goUserByProject() {
        this.queryParams.rectifierId = null
        if (!this.queryParams.associatedProjectId) {
          this.userByProjectList = []
          return
        }
        getUserByProject({
          projectId: this.queryParams.associatedProjectId,
          pageNum: 1,
          pageSize: 9999,
        }).then((res) => {
          const { code, data } = res
          if (code === 200 && data.relationship !== 2) {
            this.userByProjectList = data
          } else {
            this.userByProjectList = []
          }
        })
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.getList()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.getList()
      },
      handleDeptChange(value) {
        if (value && value.length > 0) {
          this.queryParams.rectifierDeptIdArray = value.join(',')
        } else {
          this.queryParams.rectifierDeptIdArray = undefined
        }
      },
      handleExport() {
        exportIssuesList(this.queryParams).then((response) => {
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
          })
          const a = document.createElement('a')
          const href = window.URL.createObjectURL(blob)
          a.href = href
          const date = new Date().getTime()
          a.setAttribute('download', `问题清单${date}.xlsx`)
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(href)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .mb8 {
    margin-bottom: 8px;
  }
  .table-operations {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }
</style>
