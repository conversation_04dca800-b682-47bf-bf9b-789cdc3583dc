<template>
  <div class="perform-duty">
    <div
      class="duty-container"
      style="background: #f7f9fb; border-radius: 16px"
    >
      <div>
        <div
          v-if="list && list.length !== 0"
          class="table-body"
          style="height: 290px; overflow-x: auto; overflow-y: hidden"
        >
          <el-table
            v-loading="loading"
            :data="list"
            :show-header="false"
            style="width: 100%"
          >
            <el-table-column label="标题" prop="title" show-overflow-tooltip>
              <template slot-scope="scope">
                <span style="color: #525151">
                  {{ scope.row?.dutyInspectionItemCont?.dutyInspectionItem }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createdAt" width="180" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button
                  size="small"
                  type="text"
                  @click="handlePerform(scope.row)"
                >
                  跳转履职
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div
          v-else
          class="empty-container"
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 290px;
          "
        >
          <div
            class="text item"
            style="
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;
            "
          >
            <el-image
              class="vab-data-emptys"
              :src="require('@/assets/empty_images/data_empty.png')"
              style="max-width: 200px; max-height: 200px"
            />
          </div>
        </div>
      </div>
    </div>

    <SelfDetails
      ref="selfDetails"
      :duty-list="dutyList"
      :frequency-list="frequencyList"
      :process-list="processList"
      @refreshDataList="refreshData"
    />
    <ScanDetails
      ref="scanDetails"
      :duty-list="dutyList"
      :frequency-list="frequencyList"
      :process-list="processList"
      @refreshDataList="refreshData"
    />
  </div>
</template>

<script>
  import SelfDetails from '../../perform/userResponsibility/selfDetails.vue'
  import ScanDetails from '../../perform/userResponsibility/scanDetails.vue'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  export default {
    name: 'PerformDuty',
    components: {
      SelfDetails,
      ScanDetails,
    },
    props: {
      loading: {
        type: Boolean,
        default: false,
      },
      list: {
        type: Array,
        default: () => [],
      },
    },
    created() {
      this.getDutyList()
    },
    methods: {
      morePerform() {
        this.$router.push({
          path: '/perform/userResponsibility',
        })
      },
      async getDutyList() {
        const res = await getDutyInspectionItemsList({
          pageNum: 1,
          pageSize: 9999,
        })
        const { code, data } = res

        if (code === 200) {
          this.dutyList = data
        }
      },
      handlePerform(row) {
        row.listId = row.id
        const form = {
          ...row,
          ...row.dutyListsDto,
          // listId: row.dutyListsDto.id,
        }
        if (row.type) {
          this.$refs.scanDetails.showEdit(form)
        } else {
          this.$refs.selfDetails.showEdit(form)
        }
      },
      refreshData() {
        this.$emit('refreshDataList')
      },
    },
  }
</script>

<style scoped>
  .duty-container {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .table-body /deep/ .el-table__row {
    cursor: pointer;
  }
</style>
