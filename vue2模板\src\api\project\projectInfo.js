import request from '@/utils/request'
import { getToken } from '@/utils/token'

// 获取用户
export function getUser(params) {
  return request({
    url: '/swisp-base-service/api/v1/users/client/hse-pd-perform-duty',
    method: 'GET',
    params,
  })
}

// 创建数据
export function creatProject(data) {
  return request({
    url: '/perform-duties-service/project/projects',
    method: 'post',
    data,
  })
}

// 根据id获取数据
export function getProjectById(id) {
  return request({
    url: `/perform-duties-service/project/projects/${id}`,
    method: 'get',
  })
}

// 修改数据
export function updateProject(data) {
  return request({
    url: `/perform-duties-service/project/projects/update`,
    method: 'put',
    data,
  })
}

// 分页获取列表
export function getProjectList(params) {
  return request({
    url: '/perform-duties-service/project/projects/list',
    method: 'get',
    params,
  })
}

//删除对象组
export function delProject(params) {
  return request({
    url: '/perform-duties-service/project/projects/delete',
    method: 'delete',
    params,
  })
}

// 获取base项目列表
export function getBaseProjectList(params) {
  return request({
    url: '/swisp-base-service/api/v1/projects',
    method: 'get',
    params,
  })
}

// 查询项目进度审核记录
export function getLogsExamineRecord(params) {
  return request({
    url: '/perform-duties-service/project/projectStatusLogs/logsExamineRecord',
    method: 'get',
    params,
  })
}

// 导出模板
export function getExportTemplate() {
  return request({
    url: '/perform-duties-service/project/projectRiskResponsibility/exportTemplate',
    method: 'get',
    responseType: 'blob',
  })
}

// Excel导入转换数据
export function getImportExcel(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/perform-duties-service/project/projectRiskResponsibility/importConvert',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `${getToken()}`,
    },
  })
}

// 根据项目获取人员信息
export function getUserByProject(params) {
  return request({
    url: '/perform-duties-service/project/employeeProjectRelations/selectUserByProjectId',
    method: 'get',
    params,
  })
}
