import { isArray } from '@/utils/validate'
/* 页面水印 */
const watermark = function ({
  container = document.body,
  width = '300px',
  height = '180px',
  textAlign = 'left',
  textBaseline = 'middle',
  font = '12px Microsoft Yahei',
  fillStyle = 'rgba(184, 184, 184, 0.25)',
  content = '',
  rotate = '20',
  zIndex = 9999,
  lineHeight = 20,
} = {}) {
  const canvas = document.createElement('canvas')

  canvas.setAttribute('width', width)
  canvas.setAttribute('height', height)
  const ctx = canvas.getContext('2d')
  ctx.textAlign = textAlign
  ctx.textBaseline = textBaseline
  ctx.font = font
  ctx.fillStyle = fillStyle
  ctx.translate(width / 2, height / 2)
  ctx.rotate(-(Math.PI / 180) * rotate)
  ctx.translate(-width / 2, -height / 2)
  var totalHeight = lineHeight
  var startY = (parseFloat(height) - totalHeight) / 2
  if (isArray(content)) {
    // 计算总体需要的高度（文字数量 * 行高）
    totalHeight = content.length * lineHeight
    // 计算开始位置, 垂直居中整体文字
    startY = (parseFloat(height) - totalHeight) / 2
    for (var i = 0; i < content.length; i++) {
      ctx.fillText(content[i], 20 + 15 * i, startY + lineHeight * i)
    }
  } else {
    ctx.fillText(content, 20, startY + lineHeight)
  }

  const base64Url = canvas.toDataURL()
  const __wm = document.querySelector('.__wm')

  const watermarkDiv = __wm || document.createElement('div')
  const styleStr = `
          position:absolute;
          top:0;
          left:0;
          width:100%;
          height:100%;
          z-index:${zIndex};
          pointer-events:none;
          background-repeat:repeat;
          background-image:url('${base64Url}')`

  watermarkDiv.setAttribute('style', styleStr)
  watermarkDiv.classList.add('__wm')

  if (!__wm) {
    container.style.position = 'relative'
    container.insertBefore(watermarkDiv, container.firstChild)
  }

  const MutationObserver =
    window.MutationObserver || window.WebKitMutationObserver
  if (MutationObserver) {
    let mo = new MutationObserver(function () {
      const __wm = document.querySelector('.__wm')
      // 只在__wm元素变动才重新调用 __canvasWM
      if ((__wm && __wm.getAttribute('style') !== styleStr) || !__wm) {
        // watermarkDiv.setAttribute('class', '__wm')
        // 避免一直触发
        // mo.disconnect()
        // mo = null
        // window.__canvasWM(JSON.parse(JSON.stringify(args)))
        // document.body.removeChild(__wm)
        // watermark({
        //   content: content,
        // })
      }
    })

    mo.observe(container, {
      attributes: true,
      subtree: true,
      childList: true,
    })
  }
}
export default { watermark }
