import request from '@/utils/request'

export function getAccount(id) {
  return request({
    url: `/system/uniroleUser/${id}`,
    method: 'get',
  })
}

export function getAccountsList(params) {
  return request({
    url: '/system/uniroleUser/list',
    method: 'get',
    params,
  })
}

export function putUser(data) {
  return request({
    url: `/system/uniroleUser/update`,
    method: 'put',
    data,
  })
}

export function postUser(data) {
  return request({
    url: `/system/uniroleUser`,
    method: 'post',
    data,
  })
}
export function delUser(params) {
  return request({
    url: `/system/uniroleUser/delete`,
    method: 'delete',
    params,
  })
}
//用户绑定岗位信息
export function postUsertoJob(data) {
  return request({
    url: `/system/uniroleUser/settingActor`,
    method: 'post',
    data,
  })
}
//获取用户绑定岗位信息
export function getUsertoJob(params) {
  return request({
    url: `/system/uniroleUser/getJob`,
    method: 'get',
    params,
  })
}
//删除用户绑定岗位信息
export function delUsertoJob(params) {
  return request({
    url: `/system/uniroleUser/removeJob`,
    method: 'delete',
    params,
  })
}

export function resetAccountPassword(id) {
  return request({
    url: `/system/uniroleUser/resetPassword/${id}`,
    method: 'post',
  })
}
//获取检验岗人员信息
export function getCheckPersons(params) {
  return request({
    url: `/system/uniroleUser/checkPersons`,
    method: 'get',
    params,
  })
}

// getOtherUserInfo
export function getOtherUserInfo(id) {
  return request({
    url: `/system/uniroleUser/${id}`,
    method: 'get',
  })
}

//获取用户某机构下设置的岗位
export function getUserOrganRole(params) {
  return request({
    url: `/system/uniroleJob/getJob`,
    method: 'get',
    params,
  })
}
