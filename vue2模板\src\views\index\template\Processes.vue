<template>
  <el-form ref="formRef" label-width="80px" :model="form">
    <el-form-item label="项目名称：" prop="projectId">
      {{ form.projectName }}
    </el-form-item>
    <el-row>
      <el-col :span="12">
        <el-form-item label="项目阶段：" prop="projectStage">
          <span v-for="(item, index) in dictList" :key="index">
            <span v-if="item.value === form.projectStage">
              {{ item.name }}
            </span>
          </span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工序状态：" prop="processStatus">
          <span v-if="form.processStatus == 1">结束</span>
          <span v-else-if="form.processStatus == 0">开始</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="发起人：" prop="initiatorPerson">
          {{ initiatorPerson?.name }}
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  export default {
    props: {
      formData: {
        type: Object,
        default: () => {},
      },
      initiatorPerson: {
        type: Object,
        default: () => {},
      },
      dictList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {},
      }
    },
    watch: {
      formData(val) {
        this.form = val
      },
    },
  }
</script>
