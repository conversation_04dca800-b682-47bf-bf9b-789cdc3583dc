import '@/vab'
import Vue from 'vue'
import App from './App'
import i18n from './i18n'
import router from './router'
import store from './store'
import ApprovalRecordDialog from '@/components/ApprovalRecordDialog'

/**
 * @description 正式环境默认使用mock，正式项目记得注释后再打包
 */
import { pwa } from './config'

if (pwa) require('./registerServiceWorker')

Vue.config.productionTip = false
new Vue({
  el: '#app',
  i18n,
  store,
  router,
  render: (h) => h(App),
})

// 注册全局组件
Vue.component('ApprovalRecordDialog', ApprovalRecordDialog)
