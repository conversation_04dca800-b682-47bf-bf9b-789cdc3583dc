<template>
  <vue-ueditor-wrap
    v-model="value"
    :config="myConfig"
    :destroy="false"
    style="margin-bottom: 20px"
    @ready="ready"
  />
</template>

<script>
  import {
    // baseURL,
    serverURL,
  } from '@/config'
  import store from '@/store'
  import moment from 'moment'
  import VueUeditorWrap from 'vue-ueditor-wrap'
  export default {
    components: { VueUeditorWrap },
    props: {
      values: {
        type: String,
        default: () => {
          return ''
        },
      },
      params: {
        type: Object,
        default: () => {
          return {}
        },
      },
      toolbars: {
        type: Array,
        default: () => {
          return null
        },
      },
      initialFrameHeight: {
        type: Number,
        default: () => 260,
      },
    },
    data() {
      return {
        value: this.values,
        myConfig: {
          serverUrl: `${serverURL}/swisp-base-service/api/v1/oss/ali/upload?subPath=attachments/${moment().format(
            'YYYY/MM/DD'
          )}&buketName=${'hse-pd-perform-duty'}&`,
          headers: {
            Authorization: store.getters['user/token'],
          },
          // 你的UEditor资源存放的路径,相对于打包后的index.html
          UEDITOR_HOME_URL: '/UEditor/',
          // 操作区toolbars
          // 编辑器自动被内容撑高
          autoHeightEnabled: true,
          // 工具栏是否可以浮动
          autoFloatEnabled: false,
          // 初始容器高度
          initialFrameHeight: 260,
          // 初始容器宽度
          initialFrameWidth: '100%',
          // 关闭自动保存
          enableAutoSave: false,
          //自动保存间隔时间， 单位ms
          saveInterval: 0,
          autosave: false,
          // 最大字数
          maximumwords: 600,
          //关闭字数统计
          wordCount: true,
          // 图片上传方式  base64 或 url
          imgtype: 'url',
        },
      }
    },
    watch: {
      values(newVal) {
        this.value = newVal
      },
      value: {
        handler(newVal) {
          var params = {
            data: newVal,
          }
          if (this.params) {
            params.params = this.params
          }
          this.$emit('returnEdit', params)
        },
        deep: true,
      },
    },
    created() {
      if (this.toolbars) {
        this.myConfig.toolbars = this.toolbars
      }
      if (this.initialFrameHeight) {
        this.myConfig.initialFrameHeight = this.initialFrameHeight
      }
    },
    methods: {
      ready(editorInstance) {
        editorInstance.setContent(this.value ? this.value : '', true)
      },
    },
  }
</script>

<style></style>
