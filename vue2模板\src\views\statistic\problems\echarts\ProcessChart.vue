<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: '<PERSON><PERSON><PERSON>',
    props: {
      chartData: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        chart: null,
      }
    },
    watch: {
      chartData: {
        handler() {
          this.initChart()
        },
        deep: true,
      },
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart()
      })
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    methods: {
      initChart() {
        if (!this.chart) {
          this.chart = echarts.init(this.$refs.chartRef)
        }

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
          },
          legend: {
            center: 'center',
          },
          series: [
            {
              name: '工序待整改问题',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold',
                },
              },
              labelLine: {
                show: false,
              },
              data: this.chartData,
              itemStyle: {
                color: function (params) {
                  const colorList = ['#4e73df', '#ff6b6b', '#36b9cc', '#f6c23e']
                  return colorList[params.dataIndex]
                },
              },
            },
          ],
        }

        this.chart.setOption(option)
      },
      handleResize() {
        if (this.chart) {
          this.chart.resize()
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;
    height: 100%;
  }
</style>
