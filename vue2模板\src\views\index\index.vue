<template>
  <div class="index-container">
    <el-row class="app-row" :gutter="20">
      <el-col :span="12">
        <!-- 待履职模块 -->
        <el-card class="module-card half-height" shadow="never">
          <div slot="header" class="card-header">
            <span>待履职</span>
            <div
              class="more-btn"
              style="
                float: right;
                width: 80px;
                height: 40px;
                font-size: 12px;
                font-weight: 600;
                line-height: 40px;
                color: #525151;
                text-align: center;
                cursor: pointer;
              "
              @click="moreTodo('performDuty')"
            >
              更多
            </div>
          </div>
          <div class="module-content">
            <perform-duty
              :list="performList"
              @perform="goToPerform"
              @refreshDataList="getPerformList"
            />
          </div>
        </el-card>

        <!-- 待整改模块 -->
        <el-card class="module-card half-height" shadow="never">
          <div slot="header" class="card-header">
            <span>待整改</span>
            <div
              class="more-btn"
              style="
                float: right;
                width: 80px;
                height: 40px;
                font-size: 12px;
                font-weight: 600;
                line-height: 40px;
                color: #525151;
                text-align: center;
                cursor: pointer;
              "
              @click="moreTodo('rectifyTask')"
            >
              更多
            </div>
          </div>
          <div class="module-content">
            <rectify-task :list="rectifyList" @rectify="goToRectify" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <!-- 待办任务模块 -->
        <el-card class="module-card half-height" shadow="never">
          <div slot="header" class="card-header">
            <span class="title">
              待办任务
              <span v-if="todoNum" class="num">{{ todoNum }}</span>
            </span>

            <div
              class="more-btn"
              style="
                float: right;
                width: 80px;
                height: 40px;
                font-size: 12px;
                font-weight: 600;
                line-height: 40px;
                color: #525151;
                text-align: center;
                cursor: pointer;
              "
              @click="moreTodo('todo')"
            >
              更多
            </div>
          </div>
          <todo :todo-list="todoList" @refresh="getMyTodoReportData" />
        </el-card>

        <!-- 已办任务模块 -->
        <el-card class="module-card half-height" shadow="never">
          <div slot="header" class="card-header">
            <span>已办任务</span>
            <div
              class="more-btn"
              style="
                float: right;
                width: 80px;
                height: 40px;
                font-size: 12px;
                font-weight: 600;
                line-height: 40px;
                color: #525151;
                text-align: center;
                cursor: pointer;
              "
              @click="moreTodo('doneTask')"
            >
              更多
            </div>
          </div>
          <div class="module-content">
            <done-task :todo-list="doneList" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import Todo from './components/todo.vue'
  import PerformDuty from './components/PerformDuty.vue'
  import RectifyTask from './components/RectifyTask.vue'
  import DoneTask from './components/DoneTask.vue'
  import { getToDoListByUser, getToDoList } from '@/api/todo'
  import { getRectifierQueryList } from '@/api/question/issuesList'
  import { mapGetters } from 'vuex'

  export default {
    name: 'Index',
    components: {
      Todo,
      PerformDuty,
      RectifyTask,
      DoneTask,
    },
    data() {
      return {
        user: this.$store.getters.user,
        currentDate: new Date().toLocaleString(),
        // 待履职数据
        performList: [],
        performLoading: false,
        // 待整改数据
        rectifyList: [],
        rectifyLoading: false,
        // 已办任务数据
        doneList: [],
        doneLoading: false,
        // 待办任务数据
        todoList: [],
        todoLoading: false,
        todoTimer: null,
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
        userInfo: 'user/user',
      }),
    },
    mounted() {
      // this.resetTimerAndRefreshData()
      this.fetchData()
    },
    beforeDestroy() {
      this.clearRefreshTimer()
    },
    methods: {
      fetchData() {
        this.getMyTodoReportData()
        this.getPerformList()
        this.getRectifyList()
        this.getDoneList()
      },
      // 获取待履职列表
      getPerformList() {
        this.performLoading = true
        getToDoListByUser({
          queryProjectId: this.storeProjectId || 14,
          pageSize: 5,
          pageNum: 1,
        })
          .then((res) => {
            if (res.code === 200) {
              this.performList = res.data
            }
            this.performLoading = false
          })
          .catch(() => {
            this.performLoading = false
          })
      },
      // 获取待整改列表
      getRectifyList() {
        this.rectifyLoading = true
        getRectifierQueryList({
          // associatedProjectId: this.storeProjectId || 14,
          rectifierId: this.userId,
          pageSize: 5,
          pageNum: 1,
        })
          .then((res) => {
            if (res.code === 200) {
              this.rectifyList = res.data
            }
            this.rectifyLoading = false
          })
          .catch(() => {
            this.rectifyLoading = false
          })
      },
      moreTodo(type) {
        switch (type) {
          case 'performDuty': // 待履职
            this.$router.push({
              path: '/perform/userResponsibility',
            })
            break
          case 'rectifyTask': // 待整改
            this.$router.push({
              path: '/question/issuesList',
            })
            break
          case 'todo': // 待办任务
            this.$router.push({
              name: 'MyAgentMatters',
            })
            break
          case 'doneTask': // 已办任务
            this.$router.push({
              name: 'MyAgentMatters',
            })
            break
          default:
            break
        }
      },
      // 获取待办任务列表
      async getMyTodoReportData() {
        this.todoLoading = true
        try {
          const { roles } = this.userInfo
          const isAdmin =
            roles.includes('ADMIN') || roles.includes('root', 'admin')
          const params = {
            approver: isAdmin ? undefined : this.userId,
            approvalStatus: 'IN_APPROVAL',
            pageSize: 6,
            pageNum: 1,
          }
          const { data, page } = await getToDoList(params)

          this.todoList = data || []
          this.todoNum = page?.totalCount || 0
        } catch (error) {
          console.error('获取待办任务失败:', error)
          // this.$message.error('获取待办任务失败')
        } finally {
          this.todoLoading = false
        }
      },
      // 获取已办任务列表
      async getDoneList() {
        this.doneLoading = true
        try {
          const params = {
            approvalDataPerson: this.userId,
            pageSize: 6,
            pageNum: 1,
          }
          const { data } = await getToDoList(params)

          this.doneList = data || []
        } catch (error) {
          console.error('获取已办任务失败:', error)
          // this.$message.error('获取已办任务失败')
        } finally {
          this.doneLoading = false
        }
      },
      // 跳转到履职页面
      goToPerform(row) {
        this.$router.push({
          path: '/perform/basic',
          query: { id: row.id },
        })
      },
      // 跳转到整改页面
      goToRectify(row) {
        this.$router.push({
          path: '/question/issuesList',
          query: { id: row.id },
        })
      },
      // 清除定时器
      clearRefreshTimer() {
        if (this.todoTimer) {
          clearInterval(this.todoTimer)
          this.todoTimer = null
        }
      },

      // 重置定时器并刷新数据
      resetTimerAndRefreshData() {
        this.clearRefreshTimer()

        // 立即刷新数据
        this.fetchData()

        // 重新设置定时器，每10秒刷新一次
        this.todoTimer = setInterval(() => {
          this.fetchData()
        }, 10000)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .index-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 185px); /* 减去头部和其他固定元素的高度 */
    padding: 20px;
    background-color: #ccc;

    .app-row {
      flex: 1;
      height: 100%;
      margin-bottom: 0;

      .el-col {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
    }

    .module-card {
      position: relative;
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;

      &.half-height {
        height: calc(50% - 10px); /* 减去间距的一半 */
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .num {
          height: 18px;
          padding: 0 6px;
          margin-left: 4px;
          font-size: 12px;
          line-height: 18px;
          color: #fff;
          background-color: #ff4d4f;
          border-radius: 9px;
        }

        span {
          font-size: 16px;
          font-weight: bold;
        }

        .more-btn {
          position: absolute;
          top: 10px;
          right: 10px;
          cursor: pointer;
        }
      }

      .module-content {
        flex: 1;
        overflow: auto;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
</style>
