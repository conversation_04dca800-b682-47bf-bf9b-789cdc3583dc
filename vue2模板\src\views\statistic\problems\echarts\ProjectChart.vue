<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'ProjectC<PERSON>',
    props: {
      chartData: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        chart: null,
      }
    },
    watch: {
      chartData: {
        handler() {
          this.initChart()
        },
        deep: true,
      },
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart()
      })
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    methods: {
      initChart() {
        if (!this.chart) {
          this.chart = echarts.init(this.$refs.chartRef)
        }

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
          },
          legend: {
            type: 'scroll',
            orient: 'horizontal',
            top: 10,
            data: this.chartData.map((item) => item.name),
            pageButtonCount: 5,
            pageButtonItemGap: 5,
            pageIconColor: '#2f4554',
            pageIconInactiveColor: '#aaa',
            pageIconSize: 12,
            pageTextStyle: {
              color: '#333',
            },
            pageFormatter: '{current}/{total}',
            selectedMode: true,
            animation: true,
            animationDurationUpdate: 500,
          },
          series: [
            {
              name: '项目待整改问题',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold',
                },
              },
              labelLine: {
                show: false,
              },
              data: this.chartData,
              // itemStyle: {
              //   color: function (params) {
              //     const colorList = ['#4e73df', '#ff6b6b', '#36b9cc', '#f6c23e']
              //     return colorList[params.dataIndex]
              //   },
              // },
            },
          ],
        }

        this.chart.setOption(option)

        // 初始化时确保所有项目都被选中显示
        if (this.chartData && this.chartData.length > 0) {
          const selected = {}

          this.chartData.forEach((item) => {
            selected[item.name] = true // 所有项目都设置为选中状态
          })

          this.chart.setOption({
            legend: {
              selected: selected,
            },
          })
        }

        // 移除之前的事件监听，不再限制只显示一个项目
        this.chart.off('legendselectchanged')
      },
      handleResize() {
        if (this.chart) {
          this.chart.resize()
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;
    height: 100%;
  }
</style>
