import request from '@/utils/request'

// 积分表查询
export function getPoints(params) {
  return request({
    url: '/hse-score/score/scorecard',
    method: 'get',
    params,
  })
}

// 积分明细记录查询
export function getPointsDetails(params) {
  return request({
    url: '/hse-score/score/detailsRecord',
    method: 'get',
    params,
  })
}

// 申诉记录查询
export function getAppealRecord(params) {
  return request({
    url: '/hse-score/appeal/record',
    method: 'get',
    params,
  })
}

// 创建申诉
export function createAppeal(data) {
  return request({
    url: '/hse-score/appeal/create',
    method: 'post',
    data,
  })
}

// 删除申诉记录
export function deleteAppealRecord(data) {
  return request({
    url: '/hse-score/appeal/delete',
    method: 'post',
    data,
  })
}

// 申诉详情查询
export function getAppealDetail(params) {
  return request({
    url: '/hse-score/appeal/details',
    method: 'get',
    params,
  })
}

// 申诉审批
export function approvalAppeal(data) {
  return request({
    url: '/hse-score/appeal/handle',
    method: 'post',
    data,
  })
}

// 撤回申诉
export function withdrawAppeal(data) {
  return request({
    url: '/hse-score/appeal/withdrawn',
    method: 'post',
    data,
  })
}

// 修改申诉（仅可以修改草稿状态下的）
export function updateAppeal(data) {
  return request({
    url: '/hse-score/appeal/modify',
    method: 'post',
    data,
  })
}
