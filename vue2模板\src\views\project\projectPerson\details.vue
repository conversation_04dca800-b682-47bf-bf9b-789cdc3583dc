<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '项目人员'"
      :visible.sync="dialogFormVisible"
      width="700px"
      @close="close"
    >
      <el-form
        ref="form"
        label-position="right"
        label-width="100px"
        :model="form"
        :rules="rules"
      >
        <el-form-item label="项目名称：" prop="projectId">
          <el-select
            v-model="form.projectId"
            placeholder="请选择"
            style="width: 96%"
            @change="changeProject"
          >
            <el-option
              v-for="item in baseProjectList"
              :key="item.id"
              :label="item.projectName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门：" prop="deptId">
          <el-cascader
            v-model="form.deptId"
            clearable
            filterable
            :options="deptOptions"
            placeholder="所属部门"
            :props="{ checkStrictly: true }"
            :show-all-levels="false"
            style="width: 96%"
            @change="changeDept"
          />
        </el-form-item>
        <el-form-item label="岗位：" prop="positionId">
          <el-select
            v-model="form.positionId"
            clearable
            filterable
            placeholder="请选择"
            style="width: 96%"
            @change="changePosition"
          >
            <el-option
              v-for="item in positionList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="级别：" prop="level">
          <el-input
            v-model="form.level"
            placeholder="请输入"
            readonly
            style="width: 96%"
          />
        </el-form-item>
        <el-form-item label="项目人员：" prop="employeeId">
          <el-select
            v-model="form.employeeId"
            clearable
            filterable
            multiple
            placeholder="请选择"
            style="width: 96%"
            @change="changeEmployee"
          >
            <el-option
              v-for="item in clientUserList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { getDictItems } from '@/api/user'
  import { getClientUserList } from '@/api/clientUser'
  import { listSelectDepartments } from '@/api/system/dept'
  import { getProjectList } from '@/api/project/projectInfo'
  import { creatProjectRelations } from '@/api/project/projectPerson'
  export default {
    name: '',
    data() {
      return {
        form: {},
        content: '',
        rules: {
          projectId: [{ required: true, trigger: 'change', message: '请选择' }],
          deptId: [{ required: true, trigger: 'change', message: '请选择' }],
          positionId: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          employeeId: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
        },
        title: '',
        dialogFormVisible: false,
        baseProjectList: [],
        userList: [],
        // 部门树选项
        deptOptions: [],
        positionList: [],
        clientUserList: [],
      }
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          this.getDetails(row.id)
        }
        this.getBaseProjectList()
        this.loadDeptOptions()
        this.goDictItems()
        this.dialogFormVisible = true
      },
      async getDetails(id) {
        console.log(id)
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            const form = {
              ...this.form,
              deptId: JSON.stringify(this.form.deptId),
              employeeId: undefined,
            }
            creatProjectRelations(form).then(() => {
              this.$message.success('保存成功')
              this.$emit('refreshDataList')
              this.close()
            })
          } else {
            return false
          }
        })
      },
      changeProject(e) {
        const obj = this.baseProjectList.find((item) => item.id === e)
        if (obj) {
          this.form.projectName = obj.projectName
        }
      },
      changeDept(e) {
        if (e && e.length > 0) {
          const deptId = e[e.length - 1]
          // 根据ID查找部门名称
          this.getDeptNameById(deptId)
          // 根据部门ID获取人员列表
          this.fetchData(deptId)
        } else {
          // 清空部门名称
          this.form.deptName = ''
        }
      },
      // 根据部门ID获取部门名称
      getDeptNameById(deptId) {
        // 递归查找部门树
        const findDeptName = (deptList, id) => {
          for (const dept of deptList) {
            if (dept.value === id) {
              // 找到匹配的部门，保存名称
              this.form.deptName = dept.label
              return true
            }
            // 如果有子部门，递归查找
            if (dept.children && dept.children.length > 0) {
              if (findDeptName(dept.children, id)) {
                return true
              }
            }
          }
          return false
        }

        // 在部门树中查找
        findDeptName(this.deptOptions, deptId)
      },
      changePosition(e) {
        const obj = this.positionList.find((item) => item.id === e)
        if (obj) {
          this.form.position = obj.name
          this.form.level = obj.fatherName
          this.form.levelId = obj.fatherId
        }
      },
      fetchData(id) {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          deptId: id,
        }
        getClientUserList('hse-pd-perform-duty', params).then(({ data }) => {
          this.clientUserList = data.list
        })
      },
      changeEmployee(e) {
        this.form.personList = []
        e.forEach((item) => {
          const obj = this.clientUserList.find((it) => it.id === item)
          if (obj) {
            this.form.personList.push({
              id: obj.id,
              name: obj.nickname,
            })
          }
        })
      },
      getBaseProjectList() {
        getProjectList({
          pageNum: 1,
          pageSize: 1000,
        }).then((res) => {
          this.baseProjectList = res.data || []
        })
      },
      /**
       * 加载部门
       */
      async loadDeptOptions() {
        listSelectDepartments().then((response) => {
          this.deptOptions = response.data
        })
      },
      // 获取类型字典
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'project_position',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        // this.positionList = res.data.data.list || []
        this.calcPosition(res.data.data.list || [])
      },
      calcPosition(arr) {
        if (arr.length) {
          arr.forEach((item) => {
            if (item.children && item.children.length > 0) {
              item.children.forEach((child) => {
                child.fatherName = item.name
                child.fatherId = item.id
                this.positionList.push(child)
              })
            }
          })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
