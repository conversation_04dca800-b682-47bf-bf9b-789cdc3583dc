const state = () => ({
  admin: false,
  role: [],
  permission: [],
  client: [],
  btnRole: [],
  contentRole: [],
})
const getters = {
  admin: (state) => state.admin,
  role: (state) => state.role,
  permission: (state) => state.permission,
  client: (state) => state.client,
  btnRole: (state) => state.btnRole,
  contentRole: (state) => state.contentRole,
}
const mutations = {
  setFull(state, admin) {
    state.admin = admin
  },
  setRole(state, role) {
    state.role = role
  },
  setPermission(state, permission) {
    state.permission = permission
  },
  setClient(state, client) {
    state.client = client
  },
  setBtnRole(state, btnRole) {
    state.btnRole = btnRole
  },
  setContentRole(state, contentRole) {
    state.contentRole = contentRole
  },
}
const actions = {
  setFull({ commit }, admin) {
    commit('setFull', admin)
  },
  setRole({ commit }, role) {
    commit('setRole', role)
  },
  setPermission({ commit }, permission) {
    commit('setPermission', permission)
  },
  setClient({ commit }, client) {
    commit('setClient', client)
  },
  setBtnRole({ commit }, btnRole) {
    commit('setBtnRole', btnRole)
  },
  setContentRole({ commit }, contentRole) {
    commit('setContentRole', contentRole)
  },
}
export default { state, getters, mutations, actions }
