<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="title + '设备场所'"
      :visible.sync="dialogFormVisible"
      width="700px"
      @close="close"
    >
      <el-form ref="form" label-width="140px" :model="form" :rules="rules">
        <el-form-item label="设备/场所名称：" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入设备/场所名称
"
          />
        </el-form-item>
        <el-form-item label="类别：" prop="category">
          <!-- <el-input
            v-model="form.category"
            placeholder="请输入类别
"
          /> -->
          <el-select
            v-model="form.category"
            placeholder="请选择"
            style="width: 100%"
            @change="changePerson"
          >
            <el-option
              v-for="item in categoryList"
              :key="item.Id"
              :label="item.name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="地址/编号：" prop="addressOrCode">
          <el-input
            v-model="form.addressOrCode"
            placeholder="请输入地址/编号
"
          />
        </el-form-item>
        <el-form-item label="设备/场所负责人：" prop="responsiblePersonId">
          <el-select
            v-model="form.responsiblePersonId"
            clearable
            filterable
            placeholder="请选择"
            style="width: 100%"
            @change="changePerson"
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickname"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属项目：" prop="projectId">
          <el-select
            v-model="form.projectId"
            placeholder="请选择"
            style="width: 100%"
            @change="changeProject"
          >
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.projectName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检查类型：" prop="inspectionType">
          <el-select
            v-model="form.inspectionType"
            clearable
            filterable
            placeholder="请选择"
            style="width: 100%"
            @change="changeInspection"
          >
            <el-option
              v-for="item in safeList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检查内容：" prop="inspectionContent">
          <el-select
            v-model="form.inspectionContent"
            clearable
            filterable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in latesList"
              :key="item.id"
              :label="item.inspectionContent"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { addEquipmentSites } from '@/api/system/facilities'
  import { getSafetyInspectionStandardsList } from '@/api/perform/safetyCheck'
  export default {
    name: '',
    props: {
      processList: {
        type: Array,
        default: () => [],
      },
      projectList: {
        type: Array,
        default: () => [],
      },
      safeList: {
        type: Array,
        default: () => [],
      },
      userList: {
        type: Array,
        default: () => [],
      },
      // latesList: {
      //   type: Array,
      //   default: () => [],
      // },
    },
    data() {
      return {
        form: {},
        content: '',
        rules: {
          name: [
            { required: true, trigger: 'blur', message: '请输入设备/场所名称' },
          ],
          category: [
            { required: true, trigger: 'change', message: '请选择类别' },
          ],
          addressOrCode: [
            { required: true, trigger: 'blur', message: '请输入地址/编号' },
          ],
          responsiblePersonId: [
            {
              required: true,
              trigger: 'change',
              message: '请选择设备/场所负责人',
            },
          ],
          projectId: [
            { required: true, trigger: 'change', message: '请选择所属项目' },
          ],
          inspectionType: [
            { required: true, trigger: 'change', message: '请选择检查类型' },
          ],
          inspectionContent: [
            {
              required: true,
              trigger: 'change',
              message: '请选择检查内容',
            },
          ],
        },
        title: '',
        dialogFormVisible: false,
        categoryList: [
          {
            Id: 1,
            name: '设备',
          },
          {
            Id: 2,
            name: '场所',
          },
        ],
        latesList: [],
      }
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          this.form = row
        }
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            addEquipmentSites(this.form).then(() => {
              this.$message.success('添加成功')
              this.$emit('refreshDataList')
              this.close()
            })
          } else {
            return false
          }
        })
      },
      changePerson(e) {
        const obj = this.userList.find((item) => item.userId === e)
        if (obj) {
          this.form.responsiblePersonName = obj.nickname
        }
      },
      changeProject(e) {
        const obj = this.projectList.find((item) => item.id === e)
        if (obj) {
          this.form.projectName = obj.projectName
        }
      },
      changeInspection(e) {
        this.latesList = []
        this.goDutyTemplatesList({
          pageNum: 1,
          pageSize: 9999,
          inspectionType: e,
        })
      },
      goDutyTemplatesList(params) {
        getSafetyInspectionStandardsList(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.latesList = data
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>
