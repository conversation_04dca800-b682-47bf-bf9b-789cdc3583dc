<template>
  <el-dialog
    :close-on-click-modal="false"
    title="整改回执"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <div class="correction-receipt">
      <!-- 问题信息 -->
      <div class="info-section">
        <div class="section-title">问题信息</div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">问题描述：</span>
                <span class="value">
                  {{ formData.sourceNumber || '' }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">问题类型：</span>
                <span class="value">
                  {{ formData.sourceNumber || '' }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">问题责任人：</span>
                <span class="value">
                  {{ formData.sourceNumber || '' }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">整改审核人：</span>
                <span class="value">
                  {{ formData.sourceNumber || '' }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">问题生成日期：</span>
                <span class="value">
                  {{ formData.sourceNumber || '' }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">整改期限：</span>
                <span class="value">
                  {{ formData.sourceNumber || '' }}
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">问题照片：</span>
                <div class="value">
                  <div class="evidence-item">
                    <div class="evidence-content">
                      <img
                        v-if="form.images"
                        alt="培训记录表"
                        :src="form.images"
                      />
                      <div v-else class="evidence-placeholder">培训记录表</div>
                    </div>
                    <div class="evidence-info">
                      <span class="location">生产车间A区</span>
                      <span class="time">2025-03-05 14:30</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 整改信息 -->
      <div class="correction-section">
        <div class="section-title">整改信息</div>
        <div class="form-content">
          <el-form ref="form" label-width="100px" :model="form" :rules="rules">
            <el-form-item label="整改措施" prop="measures">
              <el-input
                v-model="form.measures"
                placeholder="请输入整改措施"
                :rows="4"
                type="textarea"
              />
            </el-form-item>
            <el-form-item label="整改照片">
              <el-upload
                action="#"
                :auto-upload="false"
                :file-list="fileList"
                :limit="5"
                list-type="picture-card"
                :on-change="handleChange"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="dialogImageVisible">
                <img alt="" :src="dialogImageUrl" width="100%" />
              </el-dialog>
            </el-form-item>
            <el-form-item label="整改说明" prop="description">
              <el-input
                v-model="form.description"
                placeholder="请输入整改说明"
                :rows="3"
                type="textarea"
              />
            </el-form-item>
            <el-form-item label="整改日期" prop="correctionDate">
              <el-date-picker
                v-model="form.correctionDate"
                placeholder="选择日期"
                style="width: 100%"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="submitForm">提 交</el-button>
    </div>
  </el-dialog>
</template>

<script>
  // import { submitCorrection } from '@/api/question/issuesList'

  export default {
    name: 'CorrectionReceipt',
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      formData: {
        type: Object,
        default: () => ({}),
      },
    },
    data() {
      return {
        dialogVisible: false,
        form: {
          measures: '',
          description: '',
          correctionDate: '',
          images: [],
        },
        rules: {
          measures: [
            { required: true, message: '请输入整改措施', trigger: 'blur' },
          ],
          description: [
            { required: true, message: '请输入整改说明', trigger: 'blur' },
          ],
          correctionDate: [
            { required: true, message: '请选择整改日期', trigger: 'change' },
          ],
        },
        fileList: [],
        dialogImageUrl: '',
        dialogImageVisible: false,
      }
    },
    watch: {
      visible(val) {
        this.dialogVisible = val
        if (val) {
          this.initForm()
        }
      },
      formData: {
        handler(val) {
          if (val && Object.keys(val).length > 0) {
            this.initFormData()
          }
        },
        deep: true,
      },
    },
    methods: {
      initForm() {
        this.form = {
          measures: '',
          description: '',
          correctionDate: '',
          images: [],
        }
        this.fileList = []
        this.$nextTick(() => {
          this.$refs.form && this.$refs.form.clearValidate()
        })
      },
      initFormData() {
        if (this.formData.measures) {
          this.form.measures = this.formData.measures
        }
        if (this.formData.images) {
          // 如果有图片数据，转换为fileList格式
          this.fileList = [
            {
              name: '整改照片',
              url: this.formData.images,
            },
          ]
        }
      },
      handleClose() {
        this.dialogVisible = false
        this.$emit('update:visible', false)
      },
      handleRemove(file, fileList) {
        this.fileList = fileList
      },
      handlePreview(file) {
        this.dialogImageUrl = file.url
        this.dialogImageVisible = true
      },
      handleChange(file, fileList) {
        this.fileList = fileList
      },
      submitForm() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            // 处理图片数据
            const formData = {
              ...this.form,
              id: this.formData.id,
              // 其他需要提交的数据
            }
            console.log(formData)
            // 调用API提交整改信息
            // submitCorrection(formData)
            //   .then((res) => {
            //     if (res.code === 200) {
            //       this.$message.success('提交成功')
            //       this.handleClose()
            //       this.$emit('success')
            //     } else {
            //       this.$message.error(res.msg || '提交失败')
            //     }
            //   })
            //   .catch((err) => {
            //     console.error(err)
            //     this.$message.error('提交失败')
            //   })
          } else {
            return false
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .correction-receipt {
    .section-title {
      padding-left: 10px;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      border-left: 4px solid #409eff;
    }

    .info-section {
      margin-bottom: 20px;

      .info-content {
        padding: 15px;
        background-color: #f8f8f8;
        border-radius: 4px;

        .info-item {
          display: flex;
          margin-bottom: 10px;

          .label {
            min-width: 100px;
            font-weight: 500;
            color: #606266;
          }

          .value {
            color: #303133;

            .evidence-item {
              width: 100%;
              overflow: hidden;
              border: 1px solid #ebeef5;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

              .evidence-content {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 160px;
                font-size: 14px;
                color: #909399;
                background-color: #f5f7fa;
              }

              .evidence-info {
                display: flex;
                justify-content: space-between;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #fff;
                border-top: 1px solid #ebeef5;

                .location {
                  font-weight: 500;
                  color: #606266;
                }

                .time {
                  color: #909399;
                }
              }
            }
          }
        }
      }
    }

    .correction-section {
      margin-bottom: 20px;

      .form-content {
        padding: 15px;
        background-color: #fff;
        border-radius: 4px;
      }
    }
  }
</style>
