<!-- 安全检查标准库 -->
<template>
  <div class="app-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item>
        <el-button icon="el-icon-plus" type="success" @click="handleAdd">
          新增
        </el-button>
        <el-button
          :disabled="single"
          icon="el-icon-delete"
          type="danger"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-dropdown
          split-button
          style="margin-left: 0px"
          @command="handleDropdown"
        >
          导入
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                command="handleDownloadTemplate"
                icon="el-icon-download"
              >
                下载模板
              </el-dropdown-item>
              <el-dropdown-item command="showImportDialog" icon="el-icon-top">
                导入数据
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-menu" type="primary" @click="handlePrintList">
          打印二维码
        </el-button>
      </el-form-item>

      <el-form-item label="设备/场所名称" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          placeholder="请输入名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          class="filter-item"
          icon="el-icon-search"
          type="primary"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="设备/场所名称" prop="name" />
      <el-table-column align="center" label="类别" prop="category" />
      <el-table-column align="center" label="地址/编号" prop="addressOrCode" />
      <el-table-column
        align="center"
        label="设备/场所负责人"
        prop="responsiblePersonName"
      />
      <el-table-column align="center" label="所属项目" prop="projectName" />
      <el-table-column align="center" label="检查类型" prop="inspectionType">
        <template #default="scope">
          {{ processId(scope.row.inspectionType) }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        align="center"
        label="检查内容"
        prop="inspectionContent"
      /> -->
      <el-table-column align="center" label="检查内容" prop="inspectionContent">
        <template #default="scope">
          {{ safetyType(scope.row.inspectionContent) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="140">
        <template #default="scope">
          <!-- <el-button
            circle
            icon="el-icon-edit-outline"
            plain
            type="primary"
            @click.stop="handleUpdate(scope.row)"
          /> -->
          <!-- <el-button
            circle
            icon="el-icon-plus"
            plain
            type="success"
            @click.stop="handleAdd(scope.row)"
          /> -->
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(scope.row)"
          />
          <el-button
            circle
            icon="el-icon-menu"
            plain
            type="primary"
            @click="handlePrint(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页工具条 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <Details
      ref="details"
      :lates-list="latesList"
      :process-list="processList"
      :project-list="projectList"
      :safe-list="safeList"
      :user-list="userList"
      @refreshDataList="getList"
    />

    <!-- 添加二维码弹窗 -->
    <el-dialog
      center
      title="设备/场所二维码"
      :visible.sync="qrcodeDialogVisible"
      width="400px"
    >
      <div class="qrcode-container">
        <div v-if="selectedItems" class="qrcode-item">
          <div :id="`qrcode-${selectedItems.id}`" class="qrcode-box"></div>
          <el-button
            size="small"
            style="margin-top: 20px"
            type="primary"
            @click="downloadQRCode(selectedItems.id, selectedItems.name)"
          >
            下载二维码
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 导入 -->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="importDialog.title"
      :visible.sync="importDialog.visible"
      width="600px"
      @close="closeImportDialog"
    >
      <el-form
        ref="importFormRef"
        v-loading="importDialog.loading"
        label-width="80px"
        :model="importFormData"
      >
        <el-form-item label="Excel">
          <el-upload
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            action=""
            :auto-upload="false"
            class="upload-demo"
            drag
            :file-list="excelFilelist"
            :limit="1"
            :on-change="handleExcelChange"
          >
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">xls/xlsx 文件</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitImportForm">确 定</el-button>
          <el-button @click="closeImportDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import Details from './details.vue'
  import {
    getEquipmentSitesList,
    delEquipmentSites,
    importEquipmentSites,
    exportEquipmentSites,
  } from '@/api/system/facilities'
  import { getSafetyInspectionStandardsList } from '@/api/perform/safetyCheck'
  // getDutyInspectionItemsList
  import { getDictItems } from '@/api/user'
  import { getUser, getProjectList } from '@/api/project/projectInfo'
  import QRCode from 'qrcodejs2'
  import JSZip from 'jszip'
  import { saveAs } from 'file-saver'

  export default {
    name: 'Facilities',
    components: {
      Details,
    },
    data() {
      return {
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          name: '',
        },
        total: 0,
        dataList: [],
        tableData: [], // 用于存储处理后的表格数据
        loading: false,
        selectionList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        single: true,
        userList: [],
        projectList: [],
        processList: [],
        safeList: [],
        latesList: [],
        // 添加二维码相关数据
        qrcodeDialogVisible: false,
        selectedItems: null,
        qrcodeInstances: {},
        // 导入相关数据
        importFormData: {},
        importDialog: {
          title: '设备场所导入',
          visible: false,
          loading: false,
        },
        excelFile: undefined,
        excelFilelist: [],
        messageBox: undefined,
      }
    },
    computed: {
      processId: function () {
        return function (value) {
          return this.safeList.find((item) => item.value == value)?.name || ''
        }
      },
      safetyType: function () {
        return function (value) {
          return (
            this.latesList.find((item) => item.id == value)
              ?.inspectionContent || ''
          )
        }
      },
    },
    mounted() {
      this.getList()
      this.getUserList()
      this.goProjectList()
      this.goDictItems('project_state', 'process')
      this.goDictItems('safe_check_type', 'safe')
      this.goDutyTemplatesList({
        pageNum: 1,
        pageSize: 9999,
      })
    },
    methods: {
      async getList() {
        this.loading = true
        try {
          const res = await getEquipmentSitesList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            // this.dataList = data
            this.tableData = data
            this.total = page.totalCount
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      handleAdd() {
        this.$refs.details.showEdit()
      },
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
      handleDelete(row) {
        let idstr = ''
        if (row) {
          idstr = row.id
        } else {
          const ids = this.selectionList.map((item) => item.id)
          if (ids.length === 0) {
            this.$message.warning('请选择要删除的记录')
            return
          }
          idstr = ids.join(',')
        }
        console.log(idstr)

        this.$confirm('确认删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          delEquipmentSites({ ids: idstr }).then(() => {
            this.$message.success('删除成功')
            this.single = true
            this.getList()
          })
        })
      },
      handleQuery() {
        this.getList()
      },
      resetQuery() {
        this.$refs.queryFormRef.resetFields()
        this.getList()
      },
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length ? false : true
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.handleQuery()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.handleQuery()
      },
      getUserList() {
        getUser({
          pageNum: 1,
          pageSize: 9999,
        }).then((res) => {
          this.userList = res.data.list || []
        })
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      goDutyTemplatesList(params) {
        getSafetyInspectionStandardsList(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.latesList = data
          }
        })
      },
      // 获取类型字典
      async goDictItems(type, str) {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: type,
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        if (str === 'process') {
          this.processList = res.data.data.list || []
        } else {
          this.safeList = res.data.data.list || []
        }
      },
      handlePrint(val) {
        // 设置选中的项目
        this.selectedItems = val
        // 显示二维码弹窗
        this.qrcodeDialogVisible = true

        this.$nextTick(() => {
          this.generateQRCodes()
        })
      },

      // 生成二维码
      generateQRCodes() {
        const qrcodeElement = document.getElementById(
          `qrcode-${this.selectedItems.id}`
        )
        if (qrcodeElement) {
          qrcodeElement.innerHTML = ''

          // 创建二维码实例
          const qrcode = new QRCode(qrcodeElement, {
            text: JSON.stringify({
              id: this.selectedItems.id,
            }),
            width: 200,
            height: 200,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H,
          })

          // 保存二维码实例以便后续使用
          this.qrcodeInstances[this.selectedItems.id] = qrcode
        }
      },

      // 下载单个二维码
      downloadQRCode(id, name) {
        const qrcodeElement = document.getElementById(`qrcode-${id}`)
        if (qrcodeElement) {
          const img = qrcodeElement.querySelector('img')
          if (img) {
            const link = document.createElement('a')
            link.href = img.src
            link.download = `${name}-二维码.png`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          }
        }
      },

      // 获取二维码图片数据URL
      getQRCodeDataURL(id) {
        return new Promise((resolve) => {
          // 创建一个临时容器
          const tempElement = document.createElement('div')
          tempElement.style.position = 'absolute'
          tempElement.style.left = '-9999px'
          document.body.appendChild(tempElement)

          // 创建二维码
          new QRCode(tempElement, {
            text: JSON.stringify({
              id: id,
            }),
            width: 200,
            height: 200,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H,
          })

          // 获取图片元素
          setTimeout(() => {
            const img = tempElement.querySelector('img')
            const dataURL = img ? img.src : ''
            document.body.removeChild(tempElement)
            resolve(dataURL)
          }, 100)
        })
      },

      // 将DataURL转换为Blob
      dataURLtoBlob(dataURL) {
        const arr = dataURL.split(',')
        const mime = arr[0].match(/:(.*?);/)[1]
        const bstr = atob(arr[1])
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        return new Blob([u8arr], { type: mime })
      },

      // 批量下载二维码
      async handlePrintList() {
        if (this.selectionList.length > 0) {
          try {
            this.$message.info('正在生成二维码压缩包，请稍候...')

            const zip = new JSZip()
            const folder = zip.folder('二维码')

            // 创建每个二维码并添加到zip
            for (const item of this.selectionList) {
              const dataURL = await this.getQRCodeDataURL(item.id)
              const blob = this.dataURLtoBlob(dataURL)
              folder.file(`${item.name}-二维码.png`, blob)
            }

            // 生成zip文件并下载
            zip.generateAsync({ type: 'blob' }).then((content) => {
              saveAs(content, '设备场所二维码.zip')
              this.$message.success('二维码压缩包已生成')
            })
          } catch (error) {
            console.error('生成二维码压缩包失败:', error)
            this.$message.error('生成二维码压缩包失败')
          }
        } else {
          this.$message.warning('请选择要下载的二维码')
        }
      },
      /**
       * 导入下拉框
       */
      handleDropdown(command) {
        if (command == 'handleDownloadTemplate') {
          this.handleDownloadTemplate()
        }
        if (command == 'showImportDialog') {
          this.showImportDialog()
        }
      },
      /**
       * 下载导入模板
       */
      handleDownloadTemplate() {
        exportEquipmentSites().then((response) => {
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
          })
          const a = document.createElement('a')
          const href = window.URL.createObjectURL(blob)
          a.href = href
          a.setAttribute('download', '设备场所库模板.xlsx')
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(href)
        })
      },

      /**
       * 导入表单弹窗
       */
      async showImportDialog() {
        this.importDialog.visible = true
      },
      /**
       * Excel文件change事件
       *
       * @param file
       */
      handleExcelChange(file) {
        if (!/\.(xlsx|xls|XLSX|XLS)$/.test(file.name)) {
          this.$baseMessage('上传Excel只能为xlsx、xls格式', 'warning')
          this.excelFile = undefined
          this.excelFilelist = []
          return false
        }
        this.excelFile = file.raw
      },

      /**
       * Excel文件上传
       */
      submitImportForm() {
        this.importDialog.loading = true
        this.$refs.importFormRef.validate((valid) => {
          if (valid) {
            if (!this.excelFile) {
              this.$baseMessage('上传Excel文件不能为空', 'warning')
              return false
            }
            this.messageBox = this.$loading({
              lock: true,
              text: '上传中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            console.log(this.excelFile)

            importEquipmentSites(this.excelFile)
              .then((response) => {
                console.log(response, 'response')
                this.messageBox.close()
                this.closeInnerVisible()
                this.$message.success('导入成功')
                this.importDialog.loading = false
              })
              .catch((err) => {
                this.messageBox.close()
                console.log(err, 'err')
                this.importDialog.loading = false
              })
          }
        })
      },
      closeInnerVisible() {
        this.errTitle = ''
        this.errData = {}
        this.innerVisible = false
        this.closeImportDialog()
        this.handleQuery()
      },
      /**
       * 关闭导入弹窗
       */
      closeImportDialog() {
        this.importDialog.visible = false
        this.excelFile = undefined
        this.excelFilelist = []
        this.$refs.importFormRef.resetFields()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .qrcode-item {
    margin-bottom: 20px;
    text-align: center;
  }

  .qrcode-title {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
  }

  .qrcode-box {
    width: 200px;
    height: 200px;
    margin: 0 auto;
  }

  .qrcode-info {
    margin-top: 10px;
    text-align: left;

    p {
      margin: 5px 0;
      font-size: 14px;
    }
  }
</style>
