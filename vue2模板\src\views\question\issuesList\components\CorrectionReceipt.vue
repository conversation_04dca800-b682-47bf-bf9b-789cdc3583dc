<template>
  <el-dialog
    :close-on-click-modal="false"
    title="整改回执"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <div class="correction-receipt">
      <!-- 问题信息 -->
      <div class="info-section">
        <div class="section-title">问题信息</div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">问题描述：</span>
                <span class="value">
                  {{ formData.issueDescription || '' }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">检查设备/场所：</span>
                <span class="value">
                  <span v-for="(item, index) in siteList" :key="index">
                    <span
                      v-if="
                        item.id == formData.dutyDetails.inspectedEquipmentOrSite
                      "
                    >
                      {{ item.name }}
                    </span>
                  </span>
                </span>
              </div>
              <div class="info-item">
                <span class="label">问题责任人：</span>
                <span class="value">
                  {{ formData.rectifierName || '' }}
                </span>
              </div>
              <!-- <div class="info-item">
                <span class="label">整改审核人：</span>
                <span class="value">
                  {{ formData.rectificationReviewerName || '' }}
                </span>
              </div> -->
              <div class="info-item">
                <span class="label">问题生成日期：</span>
                <span class="value">
                  {{ formData.issueGenerationDate || '' }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">整改期限：</span>
                <span class="value">
                  {{ formData.requiredCompletionTime || '' }}
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <div class="label" style="min-width: 120px">
                  现场照片或视频：
                </div>
                <div class="value">
                  <div
                    v-if="
                      formData.onSitePhotosOrVideosAttach &&
                      formData.onSitePhotosOrVideosAttach.attachInfoList &&
                      formData.onSitePhotosOrVideosAttach.attachInfoList.length
                    "
                    class="image-content"
                  >
                    <div
                      v-for="(item, index) in formData
                        .onSitePhotosOrVideosAttach.attachInfoList"
                      :key="index"
                    >
                      <el-image
                        v-if="
                          ['img', 'jpg', 'jpeg', 'png'].includes(item.platform)
                        "
                        lazy
                        :preview-src-list="[item.url]"
                        :src="item.url"
                        style="width: 50%"
                      />
                      <el-tag
                        v-else
                        style="cursor: pointer"
                        @click="handlePreview(item.url)"
                      >
                        {{ item.name }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 整改信息 -->
      <div class="correction-section">
        <div class="section-title">整改信息</div>
        <div class="form-content">
          <el-form
            ref="form"
            label-width="140px"
            :model="formData"
            :rules="rules"
          >
            <el-form-item label="整改措施及效果" prop="rectificationMeasures">
              <el-input
                v-model="formData.rectificationMeasures"
                placeholder="请输入整改措施"
                :rows="6"
                style="width: 100%"
                type="textarea"
              />
            </el-form-item>
            <el-form-item label="整改审核人" prop="rectificationReviewerId">
              <el-select
                v-model="formData.rectificationReviewerId"
                clearable
                filterable
                placeholder="请选择"
                style="width: 100%"
                @change="handlePersonChange"
              >
                <el-option
                  v-for="item in storeUserList"
                  :key="item.userId"
                  :label="item.nickname"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="整改效果附件"
              prop="rectificationEffectAttachmentsAttach"
            >
              <file-uploader
                ref="fileUploader"
                v-model="uploadedFiles"
                @on-remove="handleFileRemove"
                @on-success="handleFileSuccess"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="submitForm">提 交</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { mapState } from 'vuex'
  import FileUploader from '@/components/FileUploader'
  import { rectificationSubmit } from '@/api/question/issuesList'
  import { getEquipmentSitesList } from '@/api/system/facilities'

  export default {
    name: 'CorrectionReceipt',
    components: {
      FileUploader,
    },
    data() {
      return {
        dialogVisible: false,
        rules: {
          rectificationMeasures: [
            { required: true, message: '请输入', trigger: 'blur' },
          ],
          rectificationReviewerId: [
            { required: true, message: '请选择', trigger: 'change' },
          ],
          // rectificationEffectAttachmentsAttach: [
          //   { required: true, message: '请选择', trigger: 'change' },
          // ],
        },
        formData: {},
        uploadedFiles: [],
        imageFiles: [],
        siteList: [],
      }
    },
    computed: {
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
    },
    mounted() {
      console.log(this.storeUserList, 'storeUserList')
    },
    methods: {
      showReceipt(row) {
        this.formData = row
        if (
          row.rectificationEffectAttachmentsAttach &&
          row.rectificationEffectAttachmentsAttach.attachInfoList
        ) {
          this.uploadedFiles =
            row.rectificationEffectAttachmentsAttach.attachInfoList || []
        }
        this.goSiteList()
        this.dialogVisible = true
      },
      handlePersonChange(e) {
        const obj = this.storeUserList.find((item) => item.userId === e)
        if (obj) {
          this.formData.rectificationReviewerName = obj.nickname
        }
      },
      handleClose() {
        this.dialogVisible = false
        this.$emit('update:visible', false)
      },
      // 附件上传
      handleFileSuccess(fileInfo) {
        if (!this.formData.attachmentsAttach) {
          this.formData.attachmentsAttach = {
            code: '',
            attachInfoList: [],
          }
        }

        this.formData.attachmentsAttach.attachInfoList = [...this.uploadedFiles]

        this.$message.success(`文件 ${fileInfo.name} 上传成功`)
      },
      handleFileRemove(file) {
        if (this.formData.attachmentsAttach) {
          this.formData.attachmentsAttach.attachInfoList = [
            ...this.uploadedFiles,
          ]
        }

        this.$message.info(`文件 ${file.name} 已移除`)
      },
      goSiteList() {
        getEquipmentSitesList({ pageNum: 1, pageSize: 1000 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.siteList = data
          }
        })
      },
      submitForm() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (!this.uploadedFiles.length) {
              this.$message.error('请选择整改效果附件')
              return
            }
            const formData = {
              ...this.formData.dutyDetails,
              ...this.formData,
              rectificationEffectAttachmentsAttach: {
                code: '',
                attachInfoList: this.uploadedFiles,
              },
            }
            rectificationSubmit(formData)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success('提交成功')
                  this.handleClose()
                  // this.$emit('success')
                  this.$emit('refreshDataList')
                } else {
                  this.$message.error(res.msg || '提交失败')
                }
              })
              .catch((err) => {
                console.error(err)
                this.$message.error('提交失败')
              })
          } else {
            return false
          }
        })
      },
      handlePreview(url) {
        window.open(url, '_blank')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .correction-receipt {
    .section-title {
      padding-left: 10px;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      border-left: 4px solid #409eff;
    }

    .info-section {
      margin-bottom: 20px;

      .info-content {
        padding: 15px;
        background-color: #f8f8f8;
        border-radius: 4px;

        .info-item {
          display: flex;
          margin-bottom: 10px;

          .label {
            max-width: 120px;
            font-weight: 500;
            color: #606266;
          }

          .value {
            color: #303133;

            .evidence-item {
              width: 100%;
              overflow: hidden;
              border: 1px solid #ebeef5;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

              .evidence-content {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 160px;
                font-size: 14px;
                color: #909399;
                background-color: #f5f7fa;
              }

              .evidence-info {
                display: flex;
                justify-content: space-between;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #fff;
                border-top: 1px solid #ebeef5;

                .location {
                  font-weight: 500;
                  color: #606266;
                }

                .time {
                  color: #909399;
                }
              }
            }
          }
        }
      }
    }

    .correction-section {
      margin-bottom: 20px;

      .form-content {
        padding: 15px;
        background-color: #fff;
        border-radius: 4px;
      }
    }
  }
</style>
