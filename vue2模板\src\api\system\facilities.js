import request from '@/utils/request'
import { getToken } from '@/utils/token'

// 创建数据
export function addEquipmentSites(data) {
  return request({
    url: '/perform-duties-service/system/equipmentSites',
    method: 'post',
    data,
  })
}

// 分页获取列表
export function getEquipmentSitesList(params) {
  return request({
    url: '/perform-duties-service/system/equipmentSites/list',
    method: 'get',
    params,
  })
}

//删除对象组
export function delEquipmentSites(params) {
  return request({
    url: '/perform-duties-service/system/equipmentSites/delete',
    method: 'delete',
    params,
  })
}

// 导入设备场所
export function importEquipmentSites(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/perform-duties-service/system/equipmentSites/import_excel',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `${getToken()}`,
    },
  })
}

// 模板导出

export function exportEquipmentSites(params) {
  return request({
    url: '/perform-duties-service/system/equipmentSites/downloadExcelTemplate',
    method: 'get',
    params,
    responseType: 'blob',
  })
}
