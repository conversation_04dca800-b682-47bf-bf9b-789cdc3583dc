<template>
  <div>
    <el-drawer
      ref="drawer"
      :close-on-click-modal="false"
      custom-class="demo-drawer"
      direction="rtl"
      size="80%"
      :title="title + '责任清单'"
      :visible.sync="dialogFormVisible"
      @close="close"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form
            ref="form"
            label-position="top"
            label-width="130px"
            :model="form"
            :rules="rules"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目信息：" prop="projectId">
                  <el-select
                    v-model="form.projectId"
                    :disabled="form.id"
                    placeholder="请选择"
                    style="width: 96%"
                    @change="changeResponItem"
                  >
                    <el-option
                      v-for="item in projectList"
                      :key="item.id"
                      :label="item.projectName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="人员：" prop="userId">
                  <el-select
                    v-model="form.userId"
                    clearable
                    :disabled="form.id"
                    filterable
                    placeholder="请选择"
                    style="width: 96%"
                    @change="handlePersonChange"
                  >
                    <el-option
                      v-for="item in projectUserList"
                      :key="item.employeeId"
                      :label="item.employeeName"
                      :value="item.employeeId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="岗位：" prop="positionName">
                  <el-input
                    v-model="form.positionName"
                    placeholder="请输入"
                    readonly
                    style="width: 96%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="级别：" prop="level">
                  <el-input
                    v-model="form.level"
                    placeholder="请输入"
                    readonly
                    style="width: 96%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="履职模板：" prop="dutyTemplateId">
                  <el-select
                    v-model="form.dutyTemplateId"
                    clearable
                    :disabled="form.id"
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                    @change="changeDutyTemplate"
                  >
                    <el-option
                      v-for="item in templatesList"
                      :key="item.id"
                      :label="item.templateName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="detail-section">
              <div class="detail-title">
                <span class="required-mark">*</span>
                安全生产责任清单明细
              </div>
              <el-button
                v-if="form.id"
                style="margin-bottom: 10px"
                type="primary"
                @click="handleAdd"
              >
                添 加
              </el-button>
              <el-table border :data="form.userDutyListsInfoDtoList">
                <el-table-column
                  align="center"
                  label="序号"
                  prop=""
                  width="50px"
                >
                  <template #default="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="履职检查项"
                  prop="inspectionItemsId"
                  width="400px"
                >
                  <template #default="scope">
                    <template v-if="!form.id">
                      <span v-for="item in dutyList" :key="item.id">
                        <span v-if="scope.row.inspectionItemsId === item.id">
                          {{ item.dutyInspectionItem }}
                        </span>
                        <span v-else-if="scope.row.templatesInfoId === item.id">
                          {{ item.dutyInspectionItem }}
                        </span>
                      </span>
                    </template>
                    <template v-else>
                      <el-select
                        v-if="
                          scope.row.listStatus == 1 || scope.row.listStatus == 2
                        "
                        v-model="scope.row.inspectionItemsId"
                        clearable
                        filterable
                        placeholder="请选择"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="item in dutyList"
                          :key="item.id"
                          :label="item.dutyInspectionItem"
                          :value="item.id"
                        />
                      </el-select>
                      <span v-else>
                        {{
                          scope.row?.dutyInspectionItemCont?.dutyInspectionItem
                        }}
                      </span>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="类型" prop="type">
                  <template #default="scope">
                    <el-select
                      v-if="
                        scope.row.listStatus == 1 || scope.row.listStatus == 2
                      "
                      v-model="scope.row.type"
                      placeholder="请选择"
                    >
                      <el-option label="自证履职" :value="0" />
                      <el-option label="扫码履职" :value="1" />
                    </el-select>
                    <span v-else>
                      <span v-if="scope.row.type">扫码履职</span>
                      <span v-else>自证履职</span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="工序" prop="process">
                  <template #default="scope">
                    <el-select
                      v-if="
                        scope.row.listStatus == 1 || scope.row.listStatus == 2
                      "
                      v-model="scope.row.process"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in processList"
                        :key="item.value"
                        :label="item.name"
                        :value="item.value"
                      />
                    </el-select>
                    <span v-else>
                      <span v-for="item in processList" :key="item.id">
                        <span v-if="scope.row.process === item.value">
                          {{ item.name }}
                        </span>
                      </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="履职时间/频率"
                  prop="frequency"
                >
                  <template #default="scope">
                    <el-select
                      v-if="
                        scope.row.listStatus == 1 || scope.row.listStatus == 2
                      "
                      v-model="scope.row.frequency"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in frequencyList"
                        :key="item.value"
                        :label="item.name"
                        :value="item.value"
                      />
                    </el-select>
                    <span v-else>
                      <span v-for="item in frequencyList" :key="item.id">
                        <span v-if="scope.row.frequency === item.value">
                          {{ item.name }}
                        </span>
                      </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="要求频次"
                  prop="frequencyCount"
                >
                  <template #default="scope">
                    <el-input
                      v-if="
                        scope.row.listStatus == 1 || scope.row.listStatus == 2
                      "
                      v-model="scope.row.frequencyCount"
                      min="1"
                      placeholder="请输入"
                      type="number"
                    />
                    <span v-else>
                      {{ scope.row.frequencyCount }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="form.id"
                  align="center"
                  label="状态"
                  prop="listStatus"
                  width="100"
                >
                  <template #default="scope">
                    <el-tag v-if="scope.row.listStatus == 0" type="info">
                      不改变
                    </el-tag>
                    <el-tag v-else-if="scope.row.listStatus == 1">修改</el-tag>
                    <el-tag
                      v-else-if="scope.row.listStatus == 2"
                      type="success"
                    >
                      新增
                    </el-tag>
                    <el-tag v-else-if="scope.row.listStatus == 3" type="danger">
                      删除
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="form.id"
                  align="center"
                  label="操作"
                  prop="listStatus"
                  width="110"
                >
                  <template #default="scope">
                    <el-select
                      v-model="scope.row.listStatus"
                      placeholder="请选择"
                      @change="changeTableList(scope.row, scope.$index)"
                    >
                      <el-option
                        v-for="item in statusList"
                        :key="item.value"
                        :disabled="
                          item.disabled ||
                          (scope.row.listStatus == 2 && item.value != 3)
                        "
                        :label="item.label"
                        :value="item.value"
                      >
                        <el-tag v-if="item.value == 0" type="info">
                          {{ item.label }}
                        </el-tag>
                        <el-tag v-else-if="item.value == 1">
                          {{ item.label }}
                        </el-tag>
                        <el-tag v-else-if="item.value == 2" type="success">
                          {{ item.label }}
                        </el-tag>
                        <el-tag v-else-if="item.value == 3" type="danger">
                          {{ item.label }}
                        </el-tag>
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-row v-if="form.id">
              <el-col :span="12">
                <el-form-item label="修改审批人：" prop="approverId">
                  <el-select
                    v-model="form.approverId"
                    clearable
                    :disabled="form.dutyStatus === -1"
                    filterable
                    placeholder="请选择"
                    style="width: 96%"
                    @change="handleApproverChange"
                  >
                    <el-option
                      v-for="item in userList"
                      :key="item.userId"
                      :label="item.nickname"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button size="medium" @click="close">取 消</el-button>
          <el-button size="medium" type="primary" @click="save">
            提 交
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { baseURL } from '@/config'
  import { getToken } from '@/utils/token'
  import { getProjectList } from '@/api/project/projectInfo'
  import { getProjectRelationsList } from '@/api/project/projectPerson'
  import {
    getDutyTemplatesList,
    getDutyTemplatesInfo,
  } from '@/api/perform/performanceTemplate'
  import {
    addUserDuty,
    updateUserDuty,
    userQueryAllList,
  } from '@/api/perform/responsibilityList'
  import moment from 'moment'
  export default {
    name: '',
    props: {
      dutyList: {
        type: Array,
        default: () => [],
      },
      processList: {
        type: Array,
        default: () => [],
      },
      frequencyList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          userId: null,
          userName: '',
          projectId: null,
          projectName: '',
          positionId: null,
          positionName: '',
          level: '',
          dutyTemplateId: null,
          dutyTemplateName: '',
          userDutyListsInfoDtoList: [],
        },
        content: '',
        rules: {
          userId: [{ required: true, trigger: 'change', message: '请选择' }],
          projectId: [{ required: true, trigger: 'change', message: '请选择' }],
          approverId: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
          dutyTemplateId: [
            { required: true, trigger: 'change', message: '请选择' },
          ],
        },
        title: '',
        dialogFormVisible: false,
        //上传
        uploadUrl: `${baseURL}/swisp-base-service/api/v1/oss/ali/upload?subPath=client-logo/${moment().format(
          'YYYY/MM/DD'
        )}&buketName=${process.env.VUE_APP_buketName}`,
        headerObj: {
          Authorization: `${getToken()}`,
        },
        statusList: [
          {
            label: '不改变',
            value: 0,
          },
          {
            label: '修改',
            value: 1,
          },
          {
            label: '新增',
            value: 2,
            disabled: true,
          },
          {
            label: '删除',
            value: 3,
          },
        ],
        userList: [],
        projectUserList: [],
        projectList: [],
        templatesList: [],
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          this.form = row
          if (row.dutyTemplateId) {
            this.goDutyTemplatesInfo(row.dutyTemplateId)
          }
          if (this.form.dutyStatus === -1) {
            this.form.approverId = row.userId
            this.form.approverName = row.userName
          }
          if (this.form.projectId) {
            this.getProjectUserList(this.form.projectId)
          } else {
            this.projectUserList = []
          }
          this.getUserAllList(row)
        }
        this.dialogFormVisible = true
        this.userList = this.storeUserList
        // this.getUserList()
        this.goProjectList()
        this.goTemplatesList()
      },
      getUserAllList(val) {
        const { id, projectId } = val
        userQueryAllList({
          queryProjectId: projectId,
          userListId: id,
          // queryUserId: this.userId,
        }).then((res) => {
          this.$set(this.form, 'userDutyListsInfoDtoList', res.data)
        })
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save() {
        const formData = {
          ...this.form,
          projectId: this.form.projectId || this.storeProjectId,
          allocateUserId: this.userId,
          type: undefined,
          isFirst: undefined,
          process: undefined,
          rowspan: undefined,
          frequency: undefined,
          frequencyCount: undefined,
          templatesInfoId: undefined,
          dutyInspectionItemId: undefined,
          dutyInspectionItemCont: undefined,
        }
        this.$refs['form'].validate((valid) => {
          if (valid) {
            if (this.form.id) {
              updateUserDuty(formData).then(() => {
                this.$message.success('修改成功')
                this.$emit('refreshDataList')
                this.close()
              })
            } else {
              addUserDuty(this.form).then(() => {
                this.$message.success('添加成功')
                this.$emit('refreshDataList')
                this.close()
              })
            }
          } else {
            return false
          }
        })
      },
      handleAdd() {
        this.form.userDutyListsInfoDtoList.push({
          score: 0,
          onOver: false,
          listStatus: 2,
          completedTimes: 0,
          userListId: this.form.id,
        })
      },
      handleDelete(index) {
        this.form.userDutyListsInfoDtoList.splice(index, 1)
      },
      handleAvatarSuccess(res) {
        this.formData.clientLogo = res.data.path
        this.formData.clientLogoUrl = res.data.file
      },
      beforeAvatarUpload(file) {
        const isLt20M = file.size / 1024 / 1024 < 200
        if (!isLt20M) {
          this.$message.error('上传附件大小不能超过 200MB!')
        }
        return isLt20M
      },
      async getProjectUserList(id) {
        this.projectUserList = []
        const params = {
          projectId: id,
          pageNum: 1,
          pageSize: 9999,
        }
        const res = await getProjectRelationsList(params)
        const { code, data } = res

        if (code === 200) {
          this.projectUserList = data
        }
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      goTemplatesList() {
        getDutyTemplatesList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.templatesList = data
          }
        })
      },
      goDutyTemplatesInfo(id) {
        getDutyTemplatesInfo(id).then((res) => {
          this.form.userDutyListsInfoDtoList =
            res.data?.dutyTemplatesInfoDtoList

          if (
            this.form.userDutyListsInfoDtoList &&
            this.form.userDutyListsInfoDtoList.length > 0
          ) {
            this.form.userDutyListsInfoDtoList.forEach((item) => {
              item.templatesInfoId = item.dutyInspectionItemId
              item.dutyInspectionItemCont = undefined
              item.dutyInspectionItemId = undefined
              item.templateId = undefined
            })
          }
        })
      },
      handlePersonChange(e) {
        const obj = this.projectUserList.find((item) => item.employeeId === e)
        if (obj) {
          this.form.userName = obj.employeeName
          this.form.positionId = obj.positionId || ''
          this.form.positionName = obj.position
          this.form.level = obj.level
        }
      },
      handleApproverChange(e) {
        const obj = this.userList.find((item) => item.userId === e)
        if (obj) {
          this.form.approverName = obj.nickname
        }
      },
      changeResponItem(e) {
        const obj = this.projectList.find((item) => item.id === e)
        if (obj) {
          this.form.projectName = obj.projectName
        }
        this.getProjectUserList(e)
      },
      changeDutyTemplate(e) {
        this.form.userDutyListsInfoDtoList = []
        const obj = this.templatesList.find((item) => item.id === e)
        if (obj) {
          this.form.dutyTemplateName = obj.templateName
          this.goDutyTemplatesInfo(obj.id)
        }
      },
      changeTableList(e, index) {
        if (e.listStatus === 3 && !e.id) {
          this.$confirm('确认删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            this.form.userDutyListsInfoDtoList.splice(index, 1)
          })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .drawer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .drawer-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .detail-section {
    margin-top: 20px;
  }

  .detail-title {
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 500;

    .required-mark {
      margin-right: 4px;
      color: #f56c6c;
    }
  }

  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    padding: 16px;
    text-align: center;
    background: #fff;
    border-top: 1px solid #e8e8e8;

    .el-button {
      margin-left: 8px;
    }
  }

  // 调整表单样式
  ::v-deep {
    .el-form-item {
      margin-bottom: 22px;

      .el-form-item__label {
        padding-bottom: 8px;
        line-height: 20px;
      }
    }

    .el-table {
      margin-bottom: 20px;
    }
  }
  .logo {
    ::v-deep {
      .avatar-uploader {
        .el-upload {
          position: relative;
          overflow: hidden;
          cursor: pointer;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
        }
        .el-upload:hover {
          border-color: #409eff;
        }
        .avatar-uploader-icon {
          width: 100px;
          height: 100px;
          font-size: 28px;
          line-height: 100px;
          color: #8c939d;
          text-align: center;
        }
        .avatar {
          display: block;
          width: 100px;
          height: 100px;
        }
      }
    }
  }
</style>

<style>
  /* 优化选择框弹窗样式 */
  .el-select-dropdown {
    max-width: 50vw !important;
    max-height: 300px !important;
  }

  .el-select-dropdown__item {
    height: auto !important;
    padding: 8px 20px !important;
    line-height: 1.5 !important;
    word-break: break-all !important;
    white-space: normal !important;
  }

  /* 优化抽屉内部滚动 */
  .demo-drawer .el-drawer__body {
    padding: 20px;
    overflow: auto;
  }

  /* 优化表单布局 */
  .drawer-container {
    padding: 0 15px;
  }

  .drawer-content {
    margin-bottom: 60px;
  }

  .drawer-footer {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    padding: 15px 20px;
    text-align: right;
    background: #fff;
    border-top: 1px solid #e8e8e8;
  }

  /* 优化表格内容显示 */
  .el-table .cell {
    line-height: 1.5 !important;
    word-break: break-word !important;
  }

  /* 优化必填标记样式 */
  .detail-title {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
  }

  .required-mark {
    margin-right: 4px;
    color: #f56c6c;
  }
</style>
