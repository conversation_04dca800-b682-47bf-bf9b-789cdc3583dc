<template>
  <div>
    <el-drawer
      ref="drawer"
      :close-on-click-modal="false"
      custom-class="demo-drawer"
      direction="rtl"
      size="100%"
      :title="title + '问题清单整改'"
      :visible.sync="dialogFormVisible"
      @close="close"
    >
      <!-- 返回列表 -->
      <div class="drawer-container">
        <div class="drawer-header">
          <el-button icon="el-icon-arrow-left" type="text" @click="close">
            返回列表
          </el-button>
        </div>

        <!-- 基本信息 -->
        <div class="info-section">
          <div class="info-container">
            <!-- 左侧基本信息 -->
            <div class="info-left">
              <div class="section-title">基本信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">清单编号：</span>
                    <span class="value">
                      <span class="info-text">
                        {{ form.sourceNumber || 'CL-2025-03-001' }}
                      </span>
                    </span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">问题生成日期：</span>
                    <span class="value">
                      <span class="info-text">
                        {{ form.issueGenerationDate || '' }}
                      </span>
                    </span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">问题来源：</span>
                    <span class="value">
                      <span class="info-tag">
                        {{ form.feedbackPersonName || '' }}
                      </span>
                      <span class="info-link">
                        {{ form.issueGenerationDate || '' }}
                      </span>
                    </span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">项目名称：</span>
                    <span class="value">
                      <span class="info-text">
                        {{ form.associatedProject || '' }}
                      </span>
                    </span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">整改截止日期：</span>
                    <span class="value">
                      <span class="info-text">
                        {{ form.requiredCompletionTime || '' }}
                      </span>
                    </span>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 右侧整改进度 -->
            <div class="info-right">
              <div class="section-title">整改进度</div>
              <div class="progress-info">
                <div class="progress-row">
                  <div class="progress-label">问题责任人：</div>
                  <div class="progress-value">
                    <span class="info-text">
                      {{ form.rectifierName || '' }}
                    </span>
                  </div>
                </div>
                <div class="progress-row">
                  <div class="progress-label">整改审核人：</div>
                  <div class="progress-value">
                    <span class="info-text">
                      {{ form.rectifierName || '' }}
                    </span>
                  </div>
                </div>
                <div class="progress-row">
                  <div class="progress-label">当前状态：</div>
                  <div class="progress-status">
                    <el-button
                      v-if="form.currentStatus === 2"
                      class="status-btn"
                      plain
                      round
                      size="mini"
                      type="success"
                    >
                      已通过
                    </el-button>
                    <el-button
                      v-if="form.currentStatus === 1"
                      class="status-btn"
                      plain
                      round
                      size="mini"
                      type="warning"
                    >
                      待整改
                    </el-button>
                    <el-button
                      v-if="form.currentStatus === 0"
                      class="status-btn"
                      plain
                      round
                      size="mini"
                      type="primary"
                    >
                      待审核
                    </el-button>
                    <el-button
                      v-if="form.currentStatus === 3"
                      class="status-btn"
                      plain
                      round
                      size="mini"
                      type="danger"
                    >
                      已驳回
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 问题详情 -->
        <div class="issue-section">
          <div class="section-title">问题详情</div>
          <div class="issue-card" :class="getStatusClass(form.currentStatus)">
            <!-- 顶部区域：左侧问题标题和信息，右侧状态 -->
            <div class="issue-header">
              <div class="issue-header-left">
                <div class="issue-title">
                  {{ form.issueDescription || '' }}
                </div>
                <div class="issue-info">
                  <span class="issue-source">
                    {{
                      form.dutyDetails?.inspectionContent ||
                      form.dutyDetails?.inspectionType
                    }}
                  </span>
                  <span class="issue-location">
                    责任人：{{ form.rectifierName || '' }}
                  </span>
                  <span class="issue-department">
                    整改人：{{ form.feedbackPersonName || '' }}
                  </span>
                </div>
              </div>
              <div class="issue-header-right">
                <div
                  class="issue-status-tag"
                  :class="getStatusClass(form.currentStatus)"
                >
                  <span v-if="form.currentStatus === 0">待审核</span>
                  <span v-if="form.currentStatus === 1">已通过</span>
                  <span v-if="form.currentStatus === 2">待整改</span>
                  <span v-if="form.currentStatus === 3">已驳回</span>
                </div>
              </div>
            </div>

            <!-- 内容区域：三栏布局 -->
            <div class="issue-content-wrapper">
              <!-- 左侧：问题描述 -->
              <div class="issue-content-left">
                <div class="content-section">
                  <div class="section-header">问题描述：</div>
                  <div class="section-body">
                    <div class="text-content">
                      {{ form.issueDescription || '' }}
                    </div>
                    <div class="image-content">
                      <div class="image-item">
                        <div class="evidence-content">
                          <img
                            v-if="form.images"
                            alt="现场照片"
                            :src="form.images"
                          />
                          <div v-else class="evidence-placeholder">
                            整改照片
                          </div>
                        </div>
                        <div class="image-desc">
                          <span class="location">生产车间A区</span>
                          <span class="time">2025-03-05 14:30</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 中间：整改措施 -->
              <div class="issue-content-middle">
                <div class="content-section">
                  <div class="section-header">整改措施</div>
                  <div class="section-body">
                    <div class="text-content">
                      <p v-if="form.measures">{{ form.measures }}</p>
                      <template v-else>
                        <p>1、对相关作业人员进行安全教育培训；</p>
                        <p>2、增加现场安全检查频次；</p>
                        <p>3、制定安全帽佩戴管理规定。</p>
                      </template>
                    </div>
                    <!-- 通过状态更新class -->
                    <div
                      class="correction-status"
                      :class="getStatusClass(form.currentStatus)"
                    >
                      <div class="status-tag">
                        <span v-if="form.currentStatus === 0">待审核</span>
                        <span v-if="form.currentStatus === 1">已通过</span>
                        <span v-if="form.currentStatus === 2">待整改</span>
                        <span v-if="form.currentStatus === 3">已驳回</span>
                      </div>
                      <div class="deadline">
                        整改期限：{{ form.requiredCompletionTime }}
                      </div>
                      <div
                        :class="{
                          'remaining-days': form.currentStatus === 2,
                          'time-days': form.currentStatus !== 2,
                        }"
                      >
                        {{
                          form.currentStatus === 2
                            ? '剩余三天'
                            : '提交时间：' +
                              (form.rectificationApprovalDate || '')
                        }}
                      </div>
                      <div class="action-buttons">
                        <el-button
                          v-if="form.currentStatus === 0"
                          class="fill-correction-btn"
                          size="mini"
                          type="primary"
                          @click="handleCorrection(form)"
                        >
                          审核
                        </el-button>
                        <el-tag
                          v-if="form.currentStatus === 1"
                          size="mini"
                          type="success"
                        >
                          已通过
                        </el-tag>
                        <el-button
                          v-if="form.currentStatus === 2"
                          class="fill-correction-btn"
                          size="mini"
                          type="warning"
                          @click="handleCorrection(form)"
                        >
                          填写整改
                        </el-button>
                        <el-tag
                          v-if="form.currentStatus === 3"
                          size="mini"
                          type="danger"
                        >
                          修改
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧：整改证据 -->
              <div class="issue-content-right">
                <div class="content-section">
                  <div class="section-header">整改证据：</div>
                  <div class="section-body">
                    <div class="evidence-container">
                      <!-- 整改照片 -->
                      <div class="evidence-item">
                        <div class="evidence-content">
                          <img
                            v-if="form.images"
                            alt="整改照片"
                            :src="form.images"
                          />
                          <div v-else class="evidence-placeholder">
                            整改照片
                          </div>
                        </div>
                        <div class="evidence-info">
                          <span class="location">生产车间A区</span>
                          <span class="time">2025-03-05 14:30</span>
                        </div>
                      </div>

                      <!-- 培训记录表 -->
                      <div class="evidence-item">
                        <div class="evidence-content">
                          <img
                            v-if="form.images"
                            alt="培训记录表"
                            :src="form.images"
                          />
                          <div v-else class="evidence-placeholder">
                            培训记录表
                          </div>
                        </div>
                        <div class="evidence-info">
                          <span class="location">生产车间A区</span>
                          <span class="time">2025-03-05 14:30</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 整改进度 -->
        <div class="progress-section">
          <div class="section-title">整改进度</div>
          <div class="progress-timeline">
            <div
              v-for="(activity, index) in progressList"
              :key="index"
              class="progress-item"
            >
              <div class="progress-dot" :class="{ active: index === 0 }">
                <div class="dot-inner"></div>
              </div>
              <div class="progress-content">
                <div class="progress-header">
                  <div class="progress-title">{{ activity.title }}</div>
                  <div class="progress-time">{{ activity.timestamp }}</div>
                </div>
                <div class="progress-desc">{{ activity.content }}</div>
                <div class="progress-user">
                  <i class="el-icon-user"></i>
                  <span>{{ activity.user || '张三（检查员）' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
    <correction-receipt
      :form-data="currentIssue"
      :visible.sync="correctionVisible"
      @success="handleCorrectionSuccess"
    />
  </div>
</template>
<script>
  import { getRectificationById } from '@/api/question/issuesList'
  import CorrectionReceipt from './components/CorrectionReceipt'
  export default {
    name: 'IssuesDetails',
    components: {
      CorrectionReceipt,
    },
    data() {
      return {
        title: '',
        dialogFormVisible: false,
        form: {},
        rules: {},
        progressList: [],
        rectificationData: {},
        currentIssue: null,
        correctionVisible: false,
      }
    },
    methods: {
      showEdit(row) {
        this.title = ''
        this.dialogFormVisible = true
        if (row) {
          // this.form = row
          this.getProgressList(row.id)
        }
      },
      close() {
        this.dialogFormVisible = false
        this.form = {}
        this.progressList = []
      },
      getStatusClass(status) {
        const statusMap = {
          0: 'status-processing', // 处理中
          1: 'status-completed', // 已完成
          2: 'status-pending', // 待处理
          3: 'status-rejected', // 已驳回
        }
        return statusMap[status] || 'status-pending'
      },
      getStatusText(status) {
        const statusMap = {
          1: '待审核',
          2: '已通过',
          3: '待整改',
          4: '已驳回',
        }
        return statusMap[status] || '待审核'
      },
      // 获取整改进度列表
      getProgressList(issueId) {
        getRectificationById(issueId).then((res) => {
          if (res.code === 200) {
            this.form = res.data
          }
        })
      },
      // 处理整改
      handleCorrection(row) {
        this.currentIssue = JSON.parse(JSON.stringify(row))
        this.correctionVisible = true
      },
      // 整改成功回调
      handleCorrectionSuccess() {
        this.$message.success('操作成功')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .drawer-container {
    padding: 20px;

    .drawer-header {
      margin-bottom: 20px;
    }

    .section-title {
      padding-left: 10px;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      border-left: 4px solid #409eff;
    }

    .info-section,
    .issue-section,
    .progress-section {
      padding: 20px;
      margin-bottom: 30px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .progress-timeline {
        position: relative;
        padding-left: 20px;

        &:before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 10px;
          width: 1px;
          content: '';
          background-color: #dcdfe6;
        }

        .progress-item {
          position: relative;
          padding-bottom: 20px;
          padding-left: 15px;

          &:last-child {
            padding-bottom: 0;
          }

          .progress-dot {
            position: absolute;
            top: 0;
            left: -20px;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background-color: #f2f6fc;
            border-radius: 50%;

            &.active {
              background-color: #e6f1fc;

              .dot-inner {
                background-color: #409eff;
              }
            }

            .dot-inner {
              width: 8px;
              height: 8px;
              background-color: #909399;
              border-radius: 50%;
            }
          }

          .progress-content {
            padding: 12px 15px;
            background-color: #f8f8f8;
            border-radius: 4px;

            .progress-header {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;

              .progress-title {
                font-size: 14px;
                font-weight: 500;
                color: #303133;
              }

              .progress-time {
                font-size: 12px;
                color: #909399;
              }
            }

            .progress-desc {
              margin-bottom: 8px;
              font-size: 13px;
              line-height: 1.5;
              color: #606266;
            }

            .progress-user {
              display: flex;
              align-items: center;
              margin-top: 5px;
              font-size: 12px;
              color: #909399;

              i {
                margin-right: 5px;
                font-size: 14px;
              }

              span {
                color: #606266;
              }
            }
          }
        }
      }
    }

    .info-item {
      display: flex;
      margin-bottom: 10px;

      .label {
        color: #606266;
      }

      .value {
        color: #303133;

        &.status {
          padding: 2px 8px;
          font-size: 12px;
          border-radius: 4px;
        }
      }
    }

    .issue-card {
      position: relative;
      padding: 20px;
      padding-bottom: 0;
      margin-bottom: 15px;
      background-color: #fff;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      &.status-pending {
        border-top: 3px solid #e6a23c;
      }

      &.status-processing {
        border-top: 3px solid #409eff;
      }

      &.status-completed {
        border-top: 3px solid #67c23a;
      }

      &.status-rejected {
        border-top: 3px solid #f56c6c;
      }

      .issue-header {
        display: flex;
        justify-content: space-between;
        padding-bottom: 15px;
        margin-bottom: 20px;
        border-bottom: 1px solid #ebeef5;

        .issue-header-left {
          .issue-title {
            margin-bottom: 12px;
            font-size: 18px;
            font-weight: bold;
            color: #303133;
          }

          .issue-info {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #606266;

            .issue-source {
              padding: 2px 8px;
              margin-right: 15px;
              font-size: 12px;
              color: #409eff;
              background-color: #ecf5ff;
              border-radius: 4px;
            }

            .issue-location {
              margin-right: 15px;
              &:before {
                margin-right: 10px;
                margin-left: 0;
                color: #dcdfe6;
                content: '|';
              }
            }

            .issue-department {
              &:before {
                margin-right: 10px;
                margin-left: 0;
                color: #dcdfe6;
                content: '|';
              }
            }
          }
        }

        .issue-header-right {
          display: flex;
          align-items: flex-start;

          .issue-status-tag {
            display: inline-block;
            min-width: 80px;
            padding: 6px 12px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            border-radius: 4px;

            &.status-pending {
              color: #fff;
              background-color: #e6a23c;
            }

            &.status-processing {
              color: #fff;
              background-color: #409eff;
            }

            &.status-completed {
              color: #fff;
              background-color: #67c23a;
            }

            &.status-rejected {
              color: #fff;
              background-color: #f56c6c;
            }
          }

          .issue-deadline {
            margin-top: 8px;
            font-size: 13px;
            color: #909399;
          }
        }
      }

      .issue-content-wrapper {
        display: flex;
        margin-bottom: 20px;

        .issue-content-left,
        .issue-content-middle {
          flex: 0.9;
          padding: 0 10px;
        }

        .issue-content-right {
          flex: 1.2;
          padding: 0 10px;
        }

        .issue-content-left {
          border-right: 1px dashed #ebeef5;
        }

        .issue-content-right {
          border-left: 1px dashed #ebeef5;
        }

        .content-section {
          margin-bottom: 20px;

          .section-header {
            padding-left: 8px;
            /* 移除左侧边框 */
            padding-left: 0;
            margin-bottom: 12px;
            font-size: 15px;
            font-weight: bold;
            color: #303133;
            border-left: none;
          }

          .section-body {
            padding: 0 5px;

            .status-pending {
              color: #e6a23c;
              background-color: #fffbf0;
            }

            .status-processing {
              color: #409eff;
              background-color: #ebf5f9;
            }

            .status-completed {
              color: #67c23a;
              background-color: #f0f9eb;
            }

            .status-rejected {
              color: #f56c6c;
              background-color: #fef0f0;
            }

            .evidence-container {
              display: flex;
              flex-wrap: wrap;
              gap: 15px;

              .evidence-item {
                width: calc(50% - 8px);
                overflow: hidden;
                border: 1px solid #ebeef5;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

                .evidence-content {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  height: 180px;
                  font-size: 14px;
                  color: #909399;
                  background-color: #f5f7fa;
                }

                .evidence-info {
                  display: flex;
                  justify-content: space-between;
                  padding: 8px 12px;
                  font-size: 12px;
                  background-color: #fff;
                  border-top: 1px solid #ebeef5;

                  .location {
                    font-weight: 500;
                    color: #606266;
                  }

                  .time {
                    color: #909399;
                  }
                }
              }
            }

            .no-image,
            .no-file {
              display: none; /* 隐藏原来的占位元素 */
            }

            .file-list {
              display: none; /* 隐藏原来的文件列表 */
            }

            .text-content {
              margin-bottom: 15px;
              line-height: 1.6;
              color: #606266;

              p {
                margin-bottom: 8px;
              }
            }

            .correction-status {
              position: relative;
              padding: 15px;
              margin-top: 15px;
              border-radius: 8px;

              .status-tag {
                display: inline-block;
                font-size: 14px;
                border-radius: 4px;
              }

              .deadline {
                margin: 10px 0;
                font-size: 14px;
              }

              .remaining-days {
                font-size: 12px;
                color: #f56c6c;
              }

              .time {
                font-size: 12px;
                color: #909399;
              }

              .action-buttons {
                position: absolute;
                top: 10px;
                right: 10px;
                text-align: right;

                .fill-correction-btn {
                  padding: 4px 8px;
                  font-size: 14px;
                  border-radius: 4px;
                }
              }
            }

            .image-content {
              .image-item {
                margin-bottom: 15px;
                overflow: hidden;
                border: 1px solid #ebeef5;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

                .evidence-content {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  height: 180px;
                  font-size: 14px;
                  color: #909399;
                  background-color: #f5f7fa;
                }

                img {
                  display: block;
                  width: 100%;
                  height: 180px;
                  object-fit: cover;
                }

                .image-desc {
                  display: flex;
                  justify-content: space-between;
                  padding: 8px 12px;
                  font-size: 12px;
                  background-color: #fff;
                  border-top: 1px solid #ebeef5;

                  .location {
                    font-weight: 500;
                    color: #606266;
                  }

                  .time {
                    color: #909399;
                  }
                }
              }
            }

            .no-image {
              margin-bottom: 15px;
              overflow: hidden;
              border: 1px solid #ebeef5;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

              .evidence-content {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 180px;
                font-size: 14px;
                color: #909399;
                background-color: #f5f7fa;
              }

              .evidence-info {
                display: flex;
                justify-content: space-between;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #fff;
                border-top: 1px solid #ebeef5;

                .location {
                  font-weight: 500;
                  color: #606266;
                }

                .time {
                  color: #909399;
                }
              }

              .empty-image-placeholder {
                display: none;
              }
            }
          }
        }
      }

      .issue-footer {
        display: flex;
        justify-content: space-between;
        padding-top: 15px;
        font-size: 12px;
        color: #909399;
        border-top: 1px solid #ebeef5;

        .time-info {
          .label {
            margin-right: 5px;
          }
          .value {
            font-weight: 500;
          }
        }
      }
    }

    .timeline-content {
      .timeline-title {
        margin-bottom: 5px;
        font-weight: bold;
      }

      .timeline-desc {
        color: #606266;
      }
    }
  }

  .status-pending {
    color: #e6a23c;
  }

  .status-processing {
    color: #409eff;
  }

  .status-completed {
    color: #67c23a;
  }

  .status-rejected {
    color: #f56c6c;
  }

  :deep() {
    .vab-theme-blue-black .el-drawer {
      padding: 10px 20px !important;
    }
  }

  .info-section {
    padding: 20px;
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .info-container {
      display: flex;

      .sub-section-title {
        margin-bottom: 15px;
        font-size: 15px;
        font-weight: 500;
        color: #303133;
      }

      .info-left {
        flex: 1;
        padding-right: 20px;
        border-right: 1px solid #ebeef5;

        .info-item {
          display: flex;
          margin-bottom: 15px;

          .label {
            min-width: 70px;
            font-weight: 500;
            color: #606266;
          }

          .value {
            color: #303133;

            .info-text {
              font-weight: 500;
            }

            .info-tag {
              display: inline-block;
              padding: 2px 8px;
              font-size: 12px;
              color: #409eff;
              background-color: #ecf5ff;
              border-radius: 4px;
            }

            .info-link {
              margin-left: 8px;
              color: #409eff;
              text-decoration: underline;
              cursor: pointer;
            }
          }
        }
      }

      .info-right {
        width: 40%;
        padding-left: 20px;

        .progress-info {
          .progress-row {
            display: flex;
            margin-bottom: 15px;

            .progress-label {
              width: 90px;
              font-weight: 500;
              color: #606266;
            }

            .progress-value {
              color: #303133;

              .info-text {
                font-weight: 500;
              }
            }

            .progress-status {
              display: flex;
              flex-wrap: wrap;
              gap: 10px;
              align-items: center;

              .status-btn {
                padding: 5px 15px;
                margin: 0;
                font-size: 12px;
                border-radius: 15px;

                &.el-button--success {
                  color: #67c23a;
                  background-color: #f0f9eb;
                  border-color: #e1f3d8;
                }

                &.el-button--warning {
                  color: #e6a23c;
                  background-color: #fdf6ec;
                  border-color: #faecd8;
                }

                &.el-button--primary {
                  color: #409eff;
                  background-color: #ecf5ff;
                  border-color: #d9ecff;
                }

                &.el-button--danger {
                  color: #f56c6c;
                  background-color: #fef0f0;
                  border-color: #fde2e2;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
