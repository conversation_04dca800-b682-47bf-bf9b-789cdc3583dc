<!-- 问题整改仪表盘 -->
<template>
  <div class="problems-container">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="card" shadow="hover">
          <div class="card-header">
            <h2>待整改问题数</h2>
          </div>
          <div class="card-body">
            <div class="title">待解决</div>
            <div class="num">{{ solved || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="card" shadow="hover">
          <div class="card-header">
            <h2>已整改问题数</h2>
          </div>
          <div class="card-body">
            <div class="title">已解决</div>
            <div class="num">{{ unsolved || 0 }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card class="card" shadow="hover">
          <div class="card-header">
            <h2>工序待整改问题清单</h2>
          </div>
          <div class="card-body">
            <div class="chart-containers">
              <div class="chart">
                <process-chart :chart-data="processData" />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="card" shadow="hover">
          <div class="card-header">
            <h2>项目待整改问题清单</h2>
          </div>
          <div class="card-body">
            <div class="chart-containers">
              <div class="chart">
                <project-chart :chart-data="projectData" />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import ProcessChart from './echarts/ProcessChart.vue'
  import ProjectChart from './echarts/ProjectChart.vue'
  import { getProblemsStatistics } from '@/api/statistic/problems'
  import { getDictItems } from '@/api/user'

  export default {
    name: 'ProblemsDashboard',
    components: {
      ProcessChart,
      ProjectChart,
    },
    data() {
      return {
        processData: [],
        projectData: [],
        dictList: [],
        solved: 0,
        unsolved: 0,
      }
    },
    async mounted() {
      await this.goDictItems()
      this.gotProblemsStatistics()
    },
    methods: {
      gotProblemsStatistics() {
        const params = {
          start_time: new Date().getFullYear() + '-01-01' + ' 00:00:00',
          end_time: new Date().getFullYear() + '-12-31' + ' 23:59:59',
        }
        getProblemsStatistics(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.solved = data.pending_issue_num
            this.unsolved = data.rectified_issue_num
            if (data.pending_issue_by_inspection_type) {
              // 对象转数组
              this.processData = Object.entries(
                data.pending_issue_by_inspection_type
              ).map(([name, value]) => ({
                name:
                  this.dictList.find((item) => item.value == name)?.name || '',
                value: value,
              }))
            }
            if (data.pending_issue_by_project) {
              // 对象转数组
              this.projectData = Object.entries(
                data.pending_issue_by_project
              ).map(([name, value]) => ({
                name: name,
                value: value,
              }))
            }
          }
        })
      },
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'safe_check_type',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.dictList = res.data.data.list || []
      },
    },
  }
</script>

<style lang="scss" scoped>
  .problems-container {
    padding: 20px;

    .card {
      margin-bottom: 20px;
      border-radius: 8px;

      .card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;

        h2 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .card-body {
        position: relative;
        box-sizing: border-box;
        padding: 20px;

        .title {
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: 600;
          text-align: center;
        }

        .num {
          font-size: 36px;
          font-weight: bold;
          color: #333;
          text-align: center;
        }
      }
    }

    .chart-containers {
      position: relative;
      box-sizing: border-box;
      width: 100%;
      height: 300px;
      padding: 0;

      .chart {
        width: 100%;
        height: 100%;
      }

      .pagination-indicator {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        align-items: center;

        span {
          margin-right: 10px;
          color: #666;
        }

        .pagination-controls {
          display: flex;
          gap: 5px;
        }
      }
    }
  }
</style>
