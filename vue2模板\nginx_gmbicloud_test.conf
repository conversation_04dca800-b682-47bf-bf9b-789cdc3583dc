worker_processes auto;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    client_max_body_size    1000M;
    proxy_ignore_client_abort on;
     #是否启动gzip压缩,on代表启动,off代表开启
      gzip  on;

      #需要压缩的常见静态资源
      gzip_types text/plain application/javascript   application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;

      #由于nginx的压缩发生在浏览器端而微软的ie6很坑爹,会导致压缩后图片看不见所以该选
      #此项是禁止ie6发生压缩
      gzip_disable "MSIE [1-6]\.";

      #如果文件大于1k就启动压缩
      gzip_min_length 1k;

      #以16k为单位,按照原始数据的大小以4倍的方式申请内存空间,一般此项不要修改
      gzip_buffers 4 16k;

      #压缩的等级,数字选择范围是1-9,数字越小压缩的速度越快,消耗cpu就越大
      gzip_comp_level 2;
    server {
        listen       8080;
        server_name  localhost;
        location / {
           root   /home/<USER>
           index  index.html index.htm;
           try_files $uri $uri/ /index.html;
           if ($request_method = OPTIONS ) {
                add_header Content-Type *;
                add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods 'POST, GET, OPTIONS, PUT, DELETE';
                add_header Access-Control-Allow-Credentials true;
                add_header Access-Control-Allow-Headers '*,Content-Type,accessToken,Authorization';
                return 200;
          }
            if ($request_filename ~* .*\.(htm|html)$)  {
                 add_header Cache-Control "no-store";
             }
        }
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
        location /perform-duties/ {
           # 不过网关
           #proxy_pass http://gstsw-service.dev/season_work/;
           proxy_set_header Connection close;
           #不允许重定向
           proxy_redirect off;
           proxy_set_header Host $host;
           #获取客户端真实ip
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header REMOTE-HOST $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           # 过网关：网关地址+服务名
           proxy_pass http://swisp-gateway-service.swisp/perform-duties;
        }

    }
}
