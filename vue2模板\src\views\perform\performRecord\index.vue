<!-- 履职记录 -->
<template>
  <div class="app-container">
    <!-- 查询区域 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item label="项目名称" prop="projectId">
        <el-select
          v-model="queryParams.projectId"
          clearable
          filterable
          placeholder="请选择"
          @change="goUserByProject"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="userByProjectList && userByProjectList.length && isAdmin"
        label="人员"
        prop="userId"
      >
        <el-select
          v-model="queryParams.userId"
          clearable
          filterable
          placeholder="请选择"
          style="width: 200px"
          @change="changeUser"
        >
          <el-option
            v-for="item in userByProjectList"
            :key="item.employeeId"
            :label="item.employeeName"
            :value="item.employeeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="部门" prop="deptIds">
        <el-cascader
          v-model="deptIdArray"
          clearable
          filterable
          :options="deptList"
          placeholder="所属部门"
          :props="{ checkStrictly: true, multiple: true, emitPath: false }"
          :show-all-levels="false"
          style="width: 96%"
          @change="handleDeptChange"
        />
      </el-form-item>
      <el-form-item label="时间段" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          style="width: 300px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="是否合格" prop="qualified">
        <el-select
          v-model="queryParams.qualified"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">
          查询
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格筛选按钮 -->
    <div class="table-operations">
      <el-dropdown @command="handleColumnFilter">
        <el-tag style="cursor: pointer">
          <i class="el-icon-s-operation"></i>
          筛选
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-tag>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="item in columnOptions"
            :key="item.prop"
            :command="item.prop"
          >
            <el-checkbox v-model="item.visible" @click.stop>
              {{ item.label }}
            </el-checkbox>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      row-key="id"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="getColumnVisible('userName')"
        align="center"
        label="人员"
        prop="userName"
      />
      <el-table-column
        v-if="getColumnVisible('projectName')"
        align="center"
        label="项目名称"
        prop="projectName"
      />
      <!-- <el-table-column align="center" label="数据标题" prop="projectName">
        <template slot-scope="scope">
          {{ scope.row.userName }} - {{ scope.row.projectName }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="人员单位" prop="positionName" /> -->
      <el-table-column
        v-if="getColumnVisible('templatesInfoId')"
        align="center"
        label="履职检查项"
        prop="templatesInfoId"
        width="260px"
      >
        <template #default="scope">
          {{ scope.row?.dutyInspectionItemCont?.dutyInspectionItem }}
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="是否完成" prop="dutyStatus">
        <el-tag
          v-model="form.dutyStatus"
          :type="form.dutyStatus === 'true' ? 'success' : 'danger'"
        >
          {{ form.dutyStatus ? '是' : '否' }}
        </el-tag>
      </el-table-column> -->
      <el-table-column
        v-if="getColumnVisible('dutyDate')"
        align="center"
        label="履职日期"
        prop="dutyDate"
      />
      <el-table-column
        v-if="getColumnVisible('dutyType')"
        align="center"
        label="类型"
        prop="dutyType"
      >
        <template #default="scope">
          <span v-if="scope.row.dutyType">扫码履职</span>
          <span v-else>自证履职</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getColumnVisible('process')"
        align="center"
        label="工序"
        prop="process"
      >
        <template #default="scope">
          <span v-for="item in processList" :key="item.id">
            <span v-if="scope.row.process === item.value">
              {{ item.name }}
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getColumnVisible('frequency')"
        align="center"
        label="履职时间/频率"
        prop="frequency"
      >
        <template #default="scope">
          <span v-for="item in frequencyList" :key="item.id">
            <span v-if="scope.row.frequency === item.value">
              {{ item.name }}
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getColumnVisible('templatesInfoId')"
        align="center"
        label="要求频次"
        prop="frequencyCount"
      />
      <!-- <el-table-column label="已完成次数" prop="completedTimes" />
      <el-table-column label="周期内完成次数" prop="cycleCompletedTimes" /> -->
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleView(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 查看组件 -->
    <SelfView
      ref="selfView"
      :duty-list="dutyList"
      :frequency-list="frequencyList"
      :process-list="processList"
    />
    <ScanView
      ref="scanView"
      :duty-list="dutyList"
      :frequency-list="frequencyList"
      :process-list="processList"
    />
  </div>
</template>

<script>
  import { mapGetters, mapState } from 'vuex'
  import { getUserDutyDetailsList } from '@/api/perform/performRecord'
  import { getDutyInspectionItemsList } from '@/api/perform/performItems'
  import { getDictItems } from '@/api/user'
  import SelfView from './components/SelfView.vue'
  import ScanView from './components/ScanView.vue'
  import { getProjectList, getUserByProject } from '@/api/project/projectInfo'
  import { listSelectDepartments } from '@/api/system/dept'
  export default {
    name: 'ResponsibilityList',
    components: {
      SelfView,
      ScanView,
    },
    data() {
      return {
        // 遮罩层
        loading: false,
        // 选中数组
        selectionList: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 责任清单表格数据
        dataList: [],
        tableData: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          deptName: undefined,
          projectName: undefined,
          projectId: undefined,
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          dataTitle: [
            { required: true, message: '数据标题不能为空', trigger: 'blur' },
          ],
          deptName: [
            { required: true, message: '人员单位不能为空', trigger: 'blur' },
          ],
          projectName: [
            { required: true, message: '项目信息不能为空', trigger: 'blur' },
          ],
        },
        // 添加列筛选配置
        columnOptions: [
          {
            label: '人员',
            prop: 'userName',
            visible: true,
          },
          {
            label: '项目名称',
            prop: 'projectName',
            visible: true,
          },
          {
            label: '履职检查项',
            prop: 'templatesInfoId',
            visible: true,
          },
          {
            label: '履职日期',
            prop: 'dutyDate',
            visible: true,
          },
          {
            label: '类型',
            prop: 'dutyType',
            visible: true,
          },
          {
            label: '工序',
            prop: 'process',
            visible: true,
          },
          {
            label: '履职时间/频率',
            prop: 'frequency',
            visible: true,
          },
          {
            label: '要求频次',
            prop: 'frequencyCount',
            visible: true,
          },
        ],
        dutyList: [],
        processList: [],
        frequencyList: [],
        projectList: [],
        userByProjectList: [],
        isAdmin: false,
        deptList: [], // 部门列表
        timeRange: [], // 时间范围
        deptIdArray: [], // 用于存储级联选择器的值
      }
    },
    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        userInfo: (state) => state.user.user,
        storeUserList: (state) => state.user.userList,
      }),
      projectId: {
        get() {
          return this.storeProjectId
        },
        set(value) {
          this.setProjectId(value)
        },
      },
    },
    // watch: {
    //   storeProjectId: {
    //     handler(newVal) {
    //       this.queryParams.projectId = newVal
    //       this.getList()
    //     },
    //   },
    // },
    async mounted() {
      const { roles, userId } = this.userInfo
      const isAdmin = roles.includes('root', 'admin') || roles.includes('admin')
      this.isAdmin = isAdmin
      this.$set(this.queryParams, 'userId', isAdmin ? undefined : userId)
      // this.queryParams.projectId = this.storeProjectId
      await this.goProjectList()
      this.getList()
      this.getDutyList()
      this.getDeptList()
      this.goDictItems('project_state', 'process')
      this.goDictItems('project_frequency', 'frequency')
    },
    methods: {
      /** 查询责任清单列表 */
      async getList() {
        this.loading = true
        // this.queryParams.projectId = this.storeProjectId
        // this.queryParams.userId = this.userId
        try {
          // if (!this.queryParams.projectId) return

          const res = await getUserDutyDetailsList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            this.tableData = data
            this.total = page.totalCount
            if (this.tableData && this.tableData.length) {
              this.tableData.forEach((item) => {
                item.process = item.dutyListInfo.process
                item.frequency = item.dutyListInfo.frequency
                item.frequencyCount = item.dutyListInfo.frequencyCount
                item.score = item.dutyListInfo.score
              })
            }
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      getDeptList() {
        // 从API获取部门列表
        listSelectDepartments().then((response) => {
          this.deptList = response.data || []
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        // 处理时间范围
        if (this.timeRange && this.timeRange.length === 2) {
          this.queryParams.queryStartTime = this.timeRange[0] + ' 00:00:00'
          this.queryParams.queryEndTime = this.timeRange[1] + ' 23:59:59'
        } else {
          this.queryParams.queryStartTime = undefined
          this.queryParams.queryEndTime = undefined
        }
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.$refs.queryFormRef.resetFields()
        if (this.isAdmin) {
          this.queryParams.userId = undefined
        } else {
          this.queryParams.userId = this.userId
        }
        this.timeRange = []
        this.userByProjectList = []
        this.deptIdArray = []
        this.queryParams.deptIdArray = undefined
        // this.queryParams.projectId = this.storeProjectId
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 履职 */
      handleView(row) {
        if (row.dutyType) {
          this.$refs.scanView.showView(row)
        } else {
          this.$refs.selfView.showView(row)
        }
      },
      // 获取类型字典
      async goDictItems(type, str) {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: type,
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        if (str === 'process') {
          this.processList = res.data.data.list || []
        } else {
          this.frequencyList = res.data.data.list || []
        }
      },
      async getDutyList() {
        const res = await getDutyInspectionItemsList({
          pageNum: 1,
          pageSize: 9999,
        })
        const { code, data } = res

        if (code === 200) {
          this.dutyList = data
        }
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.getList()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.getList()
      },
      handleColumnFilter(command) {
        console.log(command)
        return
      },
      handleDeptChange(value) {
        if (value && value.length > 0) {
          this.queryParams.deptIdArray = value.join(',')
        } else {
          this.queryParams.deptIdArray = undefined
        }
      },
      getColumnVisible(prop) {
        const column = this.columnOptions.find((item) => item.prop === prop)
        return column ? column.visible : true
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
            // this.queryParams.projectId =
            //   this.storeProjectId || data[0].projectId
          }
        })
      },
      goUserByProject() {
        this.queryParams.userId = null
        if (!this.queryParams.projectId) {
          this.userByProjectList = []
          return
        }
        getUserByProject({
          projectId: this.queryParams.projectId,
          pageNum: 1,
          pageSize: 9999,
        }).then((res) => {
          const { code, data } = res
          if (code === 200 && data.relationship !== 2) {
            this.userByProjectList = data
          } else {
            this.userByProjectList = []
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .mb8 {
    margin-bottom: 8px;
  }
  .table-operations {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }
</style>
