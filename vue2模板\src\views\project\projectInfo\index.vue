<!-- 安全检查标准库 -->
<template>
  <div class="app-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item>
        <el-button icon="el-icon-plus" type="success" @click="handleAdd">
          新增
        </el-button>
        <el-button
          :disabled="single"
          icon="el-icon-delete"
          type="danger"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-form-item>
      <!-- <el-form-item>
        <el-dropdown
          split-button
          style="margin-left: 0px"
          @command="handleDropdown"
        >
          导入
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                command="handleDownloadTemplate"
                icon="el-icon-download"
              >
                下载模板
              </el-dropdown-item>
              <el-dropdown-item command="showImportDialog" icon="el-icon-top">
                导入数据
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          icon="el-icon-download"
          style="margin-left: 12px"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-form-item> -->

      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          clearable
          placeholder="请输入名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          class="filter-item"
          icon="el-icon-search"
          type="primary"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="项目名称" prop="projectName" />
      <el-table-column
        align="center"
        label="安全责任人"
        prop="safetyResponsiblePerson"
      >
        <template slot-scope="scope">
          <span
            v-for="(item, index) in scope.row.safetyResponsiblePerson"
            :key="item.id"
          >
            {{ index ? '、' + item.name : item.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="项目管理员" prop="projectAdmin">
        <template slot-scope="scope">
          <span v-for="(item, index) in scope.row.projectAdmin" :key="item.id">
            {{ index ? ',' + item.name : item.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="项目重点生产安全风险防控责任清单"
        prop="projectId"
      >
        <template #default="scope">
          <el-button type="text" @click.stop="showRiskList(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="工序开始" prop="statusLogsStart">
        <template #default="scope">
          <span v-if="scope.row.statusLogsStart?.length">
            <span
              v-for="(item, index) in typeof scope.row.statusLogsStart ===
              'string'
                ? JSON.parse(scope.row.statusLogsStart)
                : scope.row.statusLogsStart"
              :key="index"
            >
              {{ getDictName(item) }}
              {{ index != scope.row.statusLogsStart.length - 1 ? '、' : '' }}
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="工序结束" prop="statusLogsEnd">
        <template #default="scope">
          <span v-if="scope.row.statusLogsEnd?.length">
            <span
              v-for="(item, index) in typeof scope.row.statusLogsEnd ===
              'string'
                ? JSON.parse(scope.row.statusLogsEnd)
                : scope.row.statusLogsEnd"
              :key="index"
            >
              {{ getDictName(item) }}
              {{ index != scope.row.statusLogsStart.length - 1 ? '、' : '' }}
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="审批记录" prop="">
        <template #default="scope">
          <el-button type="text" @click.stop="handleApproval(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="140">
        <template #default="scope">
          <el-button
            circle
            icon="el-icon-edit-outline"
            plain
            type="primary"
            @click.stop="handleUpdate(scope.row)"
          />
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(scope.row)"
          />
          <el-tooltip
            class="item"
            content="变更项目阶段状态"
            effect="dark"
            placement="top"
          >
            <el-button
              circle
              icon="el-icon-refresh"
              plain
              type="primary"
              @click.stop="changeApproval(scope.row)"
            />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页工具条 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 风险清单弹窗 -->
    <el-dialog
      title="项目重点生产安全风险防控责任清单"
      :visible.sync="riskDialog.visible"
      width="80%"
    >
      <el-table border :data="riskDialog.list" style="width: 100%">
        <el-table-column
          label="项目风险名称"
          min-width="120"
          prop="riskName"
          show-overflow-tooltip
        />
        <el-table-column
          label="方案名称/管控措施"
          min-width="150"
          prop="schemeMeasures"
          show-overflow-tooltip
        />
        <el-table-column
          label="关键环节"
          min-width="120"
          prop="keyLink"
          show-overflow-tooltip
        />
        <el-table-column label="责任领导" min-width="120" show-overflow-tooltip>
          <template #default="scope">
            <span
              v-for="(item, index) in scope.row.responsibilityLeader"
              :key="item.id"
            >
              {{ index ? '、' + item.name : item.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="协管领导" min-width="120" show-overflow-tooltip>
          <template #default="scope">
            <span
              v-for="(item, index) in scope.row.assistLeader"
              :key="item.id"
            >
              {{ index ? '、' + item.name : item.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="直线管理人员"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-for="(item, index) in scope.row.lineManager" :key="item.id">
              {{ index ? '、' + item.name : item.name }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="riskDialog.visible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 详情 -->
    <Details ref="details" @refreshDataList="getList" />

    <!-- 项目阶段状态 -->
    <el-dialog
      :before-close="handleClose"
      title="项目状态变更"
      :visible.sync="dialog.visible"
      width="50%"
    >
      <el-form
        ref="formRef"
        label-width="80px"
        :model="dialog.formData"
        :rules="dialog.rules"
      >
        <el-form-item label="项目名称" prop="projectId">
          {{ dialog.formData.projectName }}
        </el-form-item>
        <el-form-item label="项目阶段" prop="projectStage">
          <el-select
            v-model="dialog.formData.projectStage"
            filterable
            placeholder="请选择"
            style="width: 100%"
            @change="changeProjectStage"
          >
            <el-option
              v-for="item in dictList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工序状态" prop="processStatus">
          <!-- 单选 -->
          <el-radio-group v-model="dialog.formData.processStatus" size="small">
            <el-radio :label="0">开始</el-radio>
            <el-radio :label="1">结束</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批人" prop="approverArr">
          <el-select
            v-model="dialog.formData.approverArr"
            clearable
            filterable
            multiple
            placeholder="请选择"
            style="width: 100%"
            @change="(val) => handlePersonChange(val, 'approver')"
          >
            <el-option
              v-for="item in storeUserList"
              :key="item.userId"
              :label="item.nickname"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 审批记录弹窗 -->
    <approval-record-dialog
      :loading="approvalDialog.loading"
      :record-list="approvalRecordList"
      :title="approvalDialog.title"
      :visible.sync="approvalDialog.visible"
      @close="approvalRecordList = []"
    />
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import Details from './details.vue'
  import {
    getProjectList,
    delProject,
    getLogsExamineRecord,
  } from '@/api/project/projectInfo'
  import { getDictItems } from '@/api/user'
  import {
    creatProjectStatusLogs,
    updateProjectStatusLogs,
  } from '@/api/project/projectApproval'
  export default {
    name: 'ProjectInfo',
    components: {
      Details,
    },
    data() {
      return {
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          projectName: '',
        },
        total: 0,
        dataList: [],
        tableData: [], // 用于存储处理后的表格数据
        loading: false,
        selectionList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        single: true,
        // 添加风险清单弹窗数据
        riskDialog: {
          visible: false,
          list: [],
        },
        dialog: {
          title: '添加',
          visible: false,
          formData: {
            processStatus: 0,
          },
          rules: {
            projectId: [
              {
                required: true,
                trigger: 'change',
                message: '选择项目不能为空',
              },
            ],
            projectStage: [
              {
                required: true,
                trigger: 'change',
                message: '项目阶段不能为空',
              },
            ],
            approverArr: [
              { required: true, trigger: 'change', message: '审批人不能为空' },
            ],
            ccPersonsArr: [
              { required: true, trigger: 'change', message: '抄送人不能为空' },
            ],
          },
        },
        dictList: [],
        projectList: [],
        // 审批记录弹窗数据
        approvalDialog: {
          visible: false,
          loading: false,
          title: '审批记录',
        },
        approvalRecordList: null,
      }
    },
    computed: {
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
      getDictName() {
        return (value) => {
          return this.dictList.find((c) => c.value === value)?.name
        }
      },
    },
    mounted() {
      this.getList()
      this.goDictItems()
      this.goProjectList()
    },
    methods: {
      async getList() {
        this.loading = true
        try {
          const res = await getProjectList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            this.tableData = data
            this.total = page.totalCount
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      // 添加显示风险清单的方法
      showRiskList(row) {
        this.riskDialog.list = row.riskResponsibilityDtoList || []
        this.riskDialog.visible = true
      },
      handleAdd() {
        this.$refs.details.showEdit()
      },
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
      handleDelete(row) {
        let idstr = ''
        if (row) {
          idstr = row.id
        } else {
          const ids = this.selectionList.map((item) => item.id)
          if (ids.length === 0) {
            this.$message.warning('请选择要删除的记录')
            return
          }
          idstr = ids.join(',')
        }
        console.log(idstr)

        this.$confirm('确认删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          delProject({ ids: idstr }).then(() => {
            this.$message.success('删除成功')
            this.single = true
            this.getList()
          })
        })
      },
      handleApproval(row) {
        const params = {
          projectId: row.id,
          pageNum: 1,
          pageSize: 1000,
        }
        getLogsExamineRecord(params).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.approvalRecordList = data || []
            this.approvalDialog.visible = true
            this.approvalDialog.title = `项目状态变更审批记录`
          }
        })
      },
      changeApproval(row) {
        this.dialog.visible = true
        this.dialog.formData = {
          projectId: row.id,
          projectName: row.projectName,
        }
      },
      handleClose() {
        this.$refs.formRef.resetFields()
        this.dialog.visible = false
      },
      save() {
        this.$refs['formRef'].validate((valid) => {
          if (valid) {
            const form = {
              ...this.dialog.formData,
              approverArr: undefined,
              ccPersonsArr: undefined,
            }
            if (!this.dialog.formData.id) {
              creatProjectStatusLogs(form).then(() => {
                this.$message.success('添加成功')
                this.handleClose()
                this.getList()
              })
            } else {
              updateProjectStatusLogs(form).then(() => {
                this.$message.success('修改成功')
                this.handleClose()
                this.getList()
              })
            }
          } else {
            return false
          }
        })
      },
      handleQuery() {
        this.getList()
      },
      resetQuery() {
        this.$refs.queryFormRef.resetFields()
        this.getList()
      },
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length ? false : true
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.handleQuery()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.handleQuery()
      },
      handlePersonChange(ids, type) {
        this.dialog.formData[type] = []
        if (ids?.length) {
          this.dialog.formData[type] = ids.map((id) => {
            const user = this.storeUserList.find((item) => item.userId === id)
            return user
              ? {
                  id: user.userId,
                  name: user.nickname,
                }
              : null
          })
        }
      },
      changeProjectStage(e) {
        const obj = this.dictList.find((item) => item.value === e)
        if (obj) {
          this.dialog.formData.projectStageName = obj.name
        }
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },
      // 获取类型字典
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'project_state',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.dictList = res.data.data.list || []
      },
    },
  }
</script>

<style lang="scss" scoped></style>
