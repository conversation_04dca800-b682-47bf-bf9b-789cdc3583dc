<!-- 安全检查标准库 -->
<template>
  <div class="app-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item>
        <el-button icon="el-icon-plus" type="success" @click="handleAdd">
          新增
        </el-button>
        <el-button
          :disabled="single"
          icon="el-icon-delete"
          type="danger"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-form-item>
      <!-- <el-form-item>
        <el-dropdown
          split-button
          style="margin-left: 0px"
          @command="handleDropdown"
        >
          导入
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                command="handleDownloadTemplate"
                icon="el-icon-download"
              >
                下载模板
              </el-dropdown-item>
              <el-dropdown-item command="showImportDialog" icon="el-icon-top">
                导入数据
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          icon="el-icon-download"
          style="margin-left: 12px"
          @click="handleExport"
        >
          导出
        </el-button>
      </el-form-item> -->

      <el-form-item label="检查内容" prop="inspectionContent">
        <el-input
          v-model="queryParams.inspectionContent"
          clearable
          placeholder="请输入名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          class="filter-item"
          icon="el-icon-search"
          type="primary"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :span-method="objectSpanMethod"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="检查类型" prop="inspectionType">
        <template #default="scope">
          {{ inspectionType(scope.row.inspectionType) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="检查内容"
        prop="inspectionContent"
      />
      <el-table-column
        align="center"
        label="检查明细"
        prop="safetyInspectionInfoDtoList"
      >
        <el-table-column align="center" label="检查项" prop="inspectionItem" />
        <el-table-column
          align="center"
          label="检查要求"
          prop="inspectionRequirements"
        />
      </el-table-column>
      <!-- <el-table-column
        align="center"
        label="检查项参考标准"
        prop="referenceStandardAttachment"
      >
        <template #default="scope">
          <template
            v-if="
              scope.row.referenceStandardAttachment &&
              scope.row.referenceStandardAttachmentAttach.attachInfoList
            "
          >
            <el-tag
              v-for="(item, index) in scope.row
                .referenceStandardAttachmentAttach.attachInfoList"
              :key="index"
              style="cursor: pointer"
              type="success"
              @click="handlePreview(item.url)"
            >
              {{ item.name }}
              <br />
            </el-tag>
          </template>
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="操作" width="140">
        <template #default="scope">
          <el-button
            circle
            icon="el-icon-edit-outline"
            plain
            type="primary"
            @click.stop="handleUpdate(scope.row)"
          />
          <!-- <el-button
            circle
            icon="el-icon-plus"
            plain
            type="success"
            @click.stop="handleAdd(scope.row)"
          /> -->
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页工具条 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <Details ref="details" :dict-list="dictList" @refreshDataList="getList" />
  </div>
</template>

<script>
  import Details from './details.vue'
  import {
    getSafetyInspectionStandardsList,
    delSafetyInspectionStandards,
  } from '@/api/perform/safetyCheck'
  import { getDictItems } from '@/api/user'
  export default {
    name: 'SafetyCheck',
    components: {
      Details,
    },
    data() {
      return {
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          inspectionContent: '',
        },
        total: 0,
        dataList: [],
        tableData: [], // 用于存储处理后的表格数据
        loading: false,
        selectionList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        single: true,
        dictList: [],
      }
    },
    computed: {
      inspectionType() {
        return (value) => {
          return this.dictList.find((item) => item.value == value)?.name || ''
        }
      },
    },
    mounted() {
      this.getList()
      this.goDictItems()
    },
    methods: {
      async getList() {
        this.loading = true
        try {
          const res = await getSafetyInspectionStandardsList(this.queryParams)
          const { code, data, page } = res

          if (code === 200) {
            this.dataList = data
            this.total = page.totalCount
            // 处理表格数据
            this.handleTableData()
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.loading = false
        }
      },
      // 获取类型字典
      async goDictItems() {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          dictCode: 'safe_check_type',
          clientId: 'hse-pd-perform-duty',
        }
        const res = await getDictItems(params)
        this.dictList = res.data.data.list || []
      },
      // 处理表格数据
      handleTableData() {
        this.tableData = []
        this.dataList.forEach((item) => {
          if (
            item.safetyInspectionInfoDtoList &&
            item.safetyInspectionInfoDtoList.length
          ) {
            item.safetyInspectionInfoDtoList.forEach((detail, index) => {
              this.tableData.push({
                ...item,
                inspectionItem: detail.inspectionItem,
                inspectionRequirements: detail.inspectionRequirements,
                isFirst: index === 0,
                rowspan: item.safetyInspectionInfoDtoList.length,
              })
            })
          } else {
            this.tableData.push({
              ...item,
              isFirst: true,
              rowspan: 1,
            })
          }
        })
      },
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        console.log(row, column, rowIndex, columnIndex)
        // 只对选择列(0)、检查内容(1)、检查类型(4)、检查项参考标准(5)和操作列(6)进行合并
        if (
          columnIndex === 0 ||
          columnIndex === 1 ||
          columnIndex === 2 ||
          columnIndex === 5
        ) {
          if (row.isFirst) {
            return {
              rowspan: row.rowspan,
              colspan: 1,
            }
          } else {
            return {
              rowspan: 0,
              colspan: 0,
            }
          }
        }
      },
      handleAdd() {
        this.$refs.details.showEdit()
      },
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
      handleDelete(row) {
        let idstr = ''
        if (row) {
          idstr = row.id
        } else {
          const ids = this.selectionList.map((item) => item.id)
          if (ids.length === 0) {
            this.$message.warning('请选择要删除的记录')
            return
          }
          idstr = ids.join(',')
        }
        console.log(idstr)

        this.$confirm('确认删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          delSafetyInspectionStandards({ ids: idstr }).then(() => {
            this.$message.success('删除成功')
            this.single = true
            this.getList()
          })
        })
      },
      handleQuery() {
        this.getList()
      },
      resetQuery() {
        this.$refs.queryFormRef.resetFields()
        this.getList()
      },
      handleSelectionChange(selection) {
        this.selectionList = selection
        this.single = selection.length ? false : true
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.handleQuery()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.handleQuery()
      },
      handlePreview(file) {
        window.open(file, '_blank')
      },
    },
  }
</script>

<style lang="scss" scoped></style>
