<template>
  <div class="error-container">
    <div class="error-content">
      <div class="pic-error">
        <el-image
          class="pic-error-parent"
          fit="contain"
          :src="require('@/assets/logo.png')"
        />
      </div>
      <div class="bullshit">
        <div class="bullshit-headline">{{ headline }}</div>
        <div class="bullshit-headline">{{ headlines }}</div>
        <div class="bullshit-info">{{ info }}</div>
      </div>
      <el-button
        style="width: 100%; margin-top: 10%"
        type="primary"
        @click="gotoUrl"
      >
        下载安装
      </el-button>
      <div class="bullshit-info" style="margin-top: 50px">
        使用微信扫码下载时！请点击右上角“...”,选择浏览器打开下载
      </div>
    </div>
  </div>
</template>

<script>
  import { getLastVersionList } from '@/api/code'
  export default {
    name: 'Code',
    data() {
      return {
        jumpTime: 5,

        headline: 'HSE履职',
        headlines: 'Android版',
        info: '版本：',
        btn: '下载安装',
        data: {},
      }
    },
    created() {
      this.getCode()
    },

    methods: {
      async getCode() {
        console.log('123')
        const { data } = await getLastVersionList()
        console.log(data)
        this.info += data.versionCode
        this.data = data
      },
      gotoUrl() {
        window.open(this.data.downloadAddressUrl)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .pic-error {
    margin: 0 auto;
    margin-top: 30%;
    text-align: center;
  }
  .pic-error-parent {
    width: 5vm;
    height: 20vh;
  }
  .error-container {
    min-height: 100vh;

    .error-content {
      width: 300px;
      height: 400px;
      margin: 0 auto;
      margin-top: 20%;
    }
    .bullshit {
      width: 300px;
      padding: 30px 0;
      overflow: hidden;
      text-align: center;

      &-headline {
        margin-bottom: 10px;
        font-size: 20px;
        font-weight: bold;
        line-height: 24px;
        color: #222;
      }

      &-info {
        margin-bottom: 30px;
        font-size: 13px;
        line-height: 21px;
        color: $base-color-grey;
        opacity: 0;
        animation-name: slideUp;
        animation-duration: 0.5s;
        animation-delay: 0.2s;
        animation-fill-mode: forwards;
      }

      &-return-home {
        display: block;

        width: 100%;
        height: 36px;
        font-size: 14px;
        line-height: 36px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background: $base-color-blue;
        border-radius: 50px;
        opacity: 0;
        animation-name: slideUp;
        animation-duration: 0.5s;
        animation-delay: 0.3s;
        animation-fill-mode: forwards;
      }

      @keyframes slideUp {
        0% {
          opacity: 0;
          transform: translateY(60px);
        }

        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }
    }
  }
</style>
