<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'Code<PERSON><PERSON>',
    props: {
      chartData: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        chart: null,
      }
    },
    watch: {
      chartData: {
        handler() {
          this.initChart()
        },
        deep: true,
      },
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart()
      })
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    methods: {
      initChart() {
        if (!this.chart) {
          this.chart = echarts.init(this.$refs.chartRef)
        }

        // 处理x轴数据，限制显示长度为6个字符
        const formattedData = this.chartData.map((item) => {
          const name = item.name || ''
          return {
            ...item,
            formattedName:
              name.length > 20 ? name.substring(0, 20) + '...' : name,
          }
        })

        const xAxisData = formattedData.map((item) => item.formattedName)
        const seriesData = formattedData.map((item) => item.value)

        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: (params) => {
              const dataIndex = params[0].dataIndex
              const originalName = this.chartData[dataIndex].name
              const value = params[0].value
              return `${originalName}<br/>${params[0].seriesName}: ${value}`
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true,
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 100,
              height: 20,
              bottom: 0,
              borderColor: '#ccc',
              fillerColor: 'rgba(30, 144, 255, 0.2)',
              handleStyle: {
                color: '#1E90FF',
              },
            },
            {
              type: 'inside',
              xAxisIndex: [0],
              start: 0,
              end: 100,
              zoomOnMouseWheel: 'shift',
            },
          ],
          xAxis: [
            {
              type: 'category',
              data: xAxisData,
              axisTick: {
                alignWithLabel: true,
              },
              axisLabel: {
                interval: 0,
                rotate: 30,
                textStyle: {
                  fontSize: 12,
                },
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              name: '数量',
              nameTextStyle: {
                color: '#666',
              },
            },
          ],
          series: [
            {
              name: '打码数量',
              type: 'bar',
              barWidth: '60%',
              data: seriesData,
              itemStyle: {
                color: '#1E90FF',
              },
            },
          ],
        }

        this.chart.setOption(option)
      },
      handleResize() {
        if (this.chart) {
          this.chart.resize()
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;
    height: 100%;
  }
</style>
