/**
 * @description 路由拦截状态管理，目前两种模式：all模式与intelligence模式，其中partialRoutes是菜单暂未使用
 */
import { getProjectList } from '@/api/project/projectInfo'
import { getRouterList } from '@/api/user'
import { authentication, rolesControl } from '@/config'
import { asyncRoutes, constantRoutes, resetRouter } from '@/router'
import store from '@/store'
import { convertRouter, filterRoutes } from '@/utils/routes'
import { isArray } from '@/utils/validate'
import Vue from 'vue'
const state = () => ({
  routes: [],
  activeName: '',
})
const getters = {
  routes: (state) => state.routes,
  activeName: (state) => state.activeName,
}
const mutations = {
  /**
   * @description 多模式设置路由
   * @param {*} state
   * @param {*} routes
   */
  setRoutes(state, routes) {
    state.routes = routes
  },
  /**
   * @description 修改Meta
   * @param {*} state
   * @param options
   */
  changeMenuMeta(state, options) {
    function handleRoutes(routes) {
      return routes.map((route) => {
        if (route.name === options.name) Object.assign(route.meta, options.meta)
        if (route.children && route.children.length)
          route.children = handleRoutes(route.children)
        return route
      })
    }
    state.routes = handleRoutes(state.routes)
  },
  /**
   * @description 修改 activeName
   * @param {*} state
   * @param activeName 当前激活菜单
   */
  changeActiveName(state, activeName) {
    state.activeName = activeName
  },
}
const actions = {
  /**
   * @description 多模式设置路由
   * @param {*} { commit }
   * @param mode
   * @returns
   */
  async setRoutes({ commit }, mode = 'none') {
    const userList = store.state.user.userList
    const projectId = store.state.user.projectId
    // 默认前端路由
    let asyncRoutesAll = asyncRoutes
    let routes = [...asyncRoutesAll]
    // 设置游客路由关闭路由拦截(不需要可以删除)
    const control = mode === 'visit' ? false : rolesControl
    // 设置后端路由(不需要可以删除)
    if (authentication === 'all') {
      const {
        data: { data, code },
      } = await getRouterList({
        clientId: 'hse-pd-perform-duty',
      })
      if (code != '00000' || data.length == 0) {
        Vue.prototype.$baseMessage(
          '该账号没有授权，请联系管理员！',
          'error',
          'vab-hey-message-error'
        )
      }
      var list = data
      if (!isArray(list))
        Vue.prototype.$baseMessage(
          '路由格式返回有误！',
          'error',
          'vab-hey-message-error'
        )
      if (list[list.length - 1].path !== '*')
        list.push({ path: '*', redirect: '/404', meta: { hidden: true } })
      if (list.length) {
        // 异步获取用户列表和项目列表
        Promise.all([
          // 如果用户列表为空，获取用户列表
          !userList || userList.length === 0
            ? (async () => {
                try {
                  console.log('后台异步获取用户列表')
                  await store.dispatch('user/getUserList')
                } catch (error) {
                  console.error('获取用户列表失败:', error)
                }
              })()
            : Promise.resolve(),

          // 如果项目ID为空，获取项目列表并设置默认项目
          !projectId
            ? (async () => {
                try {
                  console.log('后台异步获取项目列表')
                  const res = await getProjectList()
                  if (res.code === 200 && res.data && res.data.length > 0) {
                    await store.dispatch('user/setProjectId', res.data[0].id)
                  }
                } catch (error) {
                  console.error('获取项目列表失败:', error)
                }
              })()
            : Promise.resolve(),
        ]).catch((error) => {
          console.error('数据加载过程中出现错误:', error)
        })
      }
      routes = convertRouter(list)
      // 根据权限和rolesControl过滤路由
      const accessRoutes = filterRoutes([...constantRoutes, ...routes], control)
      // 设置菜单所需路由
      commit('setRoutes', accessRoutes)
      // 根据可访问路由重置Vue Router
      await resetRouter(accessRoutes)
    } else {
      // 根据权限和rolesControl过滤路由
      const accessRoutes = filterRoutes([...constantRoutes, ...routes], control)
      // 设置菜单所需路由
      commit('setRoutes', accessRoutes)
      // 根据可访问路由重置Vue Router
      await resetRouter(accessRoutes)
    }
  },
  /**
   * @description 修改Route Meta
   * @param {*} { commit }
   * @param options
   */
  changeMenuMeta({ commit }, options = {}) {
    commit('changeMenuMeta', options)
  },
  /**
   * @description 修改 activeName
   * @param {*} { commit }
   * @param activeName 当前激活菜单
   */
  changeActiveName({ commit }, activeName) {
    commit('changeActiveName', activeName)
  },
}
export default { state, getters, mutations, actions }
