<template>
  <div class="image-uploader logo">
    <el-upload
      :accept="accept"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      class="uploader"
      :disabled="disabled"
      :file-list="fileList"
      :headers="headerObj"
      :http-request="customUpload"
      :limit="limit"
      list-type="picture-card"
      :multiple="multiple"
      :on-error="handleError"
      :on-exceed="handleExceed"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
    >
      <i class="el-icon-plus"></i>
      <div v-if="showTip" class="el-upload__tip" slot="tip">
        只能上传{{
          accept.replace(/\./g, '').replace(/,/g, '/')
        }}格式图片，且不超过{{ maxSize }}MB
      </div>
    </el-upload>

    <!-- 添加图片预览对话框 -->
    <el-dialog
      append-to-body
      :title="dialogTitle"
      :visible.sync="dialogVisible"
    >
      <img alt="" :src="dialogImageUrl" width="100%" />
    </el-dialog>
  </div>
</template>

<script>
  import { getToken } from '@/utils/token'
  import { baseURL, buketName } from '@/config'
  import moment from 'moment'
  import axios from 'axios'

  export default {
    name: 'ImageUploader',
    props: {
      value: {
        type: [Array, String],
        default: () => [],
      },
      maxSize: {
        type: Number,
        default: 10,
      },
      multiple: {
        type: Boolean,
        default: false,
      },
      limit: {
        type: Number,
        default: 10,
      },
      accept: {
        type: String,
        default: '.jpg,.jpeg,.png,.gif',
      },
      buttonText: {
        type: String,
        default: '上传图片',
      },
      subPath: {
        type: String,
        default: 'perform-file',
      },
      showTip: {
        type: Boolean,
        default: true,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      listType: {
        type: String,
        default: 'picture-card',
      },
    },
    data() {
      return {
        fileList: [],
        imageFiles: [],
        headerObj: {
          Authorization: `${getToken()}`,
        },
        uploadUrl: `${baseURL}swisp-base-service/api/v1/oss/ali/upload?subPath=${
          this.subPath
        }/${moment().format('YYYY/MM/DD')}&buketName=${buketName}`,
        uploadProgress: 0,
        // 添加预览对话框所需的数据
        dialogImageUrl: '',
        dialogVisible: false,
        dialogTitle: '图片预览',
        // 添加上传状态管理
        uploadingFiles: new Map(),
      }
    },
    watch: {
      value: {
        handler(val) {
          if (!val) {
            this.fileList = []
            this.imageFiles = []
            return
          }

          try {
            // 统一处理为数组
            const valueArray = Array.isArray(val) ? val : [val]

            // 过滤掉空值和重复值
            const filteredArray = valueArray.filter(
              (item) => item && (typeof item === 'string' ? item.trim() : true)
            )

            // 去重处理
            const uniqueUrls = new Set()
            const uniqueArray = filteredArray.filter((item) => {
              const url = typeof item === 'string' ? item : item.url
              if (uniqueUrls.has(url)) {
                return false
              }
              uniqueUrls.add(url)
              return true
            })

            if (uniqueArray.length > 0) {
              this.initFileList(uniqueArray)
            } else {
              this.fileList = []
              this.imageFiles = []
            }
          } catch (error) {
            console.error('处理上传文件列表出错:', error)
            this.fileList = []
            this.imageFiles = []
          }
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      // 从URL中提取文件名
      getFileNameFromUrl(url) {
        if (!url) return 'image'
        const parts = url.split('/')
        return parts[parts.length - 1] || 'image'
      },

      // 获取文件扩展名
      getFileExtension(filename) {
        if (!filename) return '.jpg'
        const ext = '.' + filename.split('.').pop().toLowerCase()
        return ext
      },

      // 获取文件扩展名（不带点号）
      getFileExtensionWithoutDot(filename) {
        if (!filename) return 'jpg'
        return filename.split('.').pop().toLowerCase()
      },

      initFileList(files) {
        // 处理上传文件列表
        this.imageFiles = files.map((item) => {
          const fileUrl = typeof item === 'string' ? item : item.url
          const fileName =
            typeof item === 'string' ? this.getFileNameFromUrl(item) : item.name
          const fileType = this.getFileExtensionWithoutDot(fileName)

          return {
            name: fileName,
            url: fileUrl,
            platform: fileType,
            size: item.size || 0,
            fileSizeFormatted:
              item.fileSizeFormatted || this.formatFileSize(item.size || 0),
          }
        })

        // 处理显示文件列表
        this.fileList = files.map((item) => {
          const fileUrl = typeof item === 'string' ? item : item.url
          const fileName =
            typeof item === 'string' ? this.getFileNameFromUrl(item) : item.name

          return {
            name: fileName,
            url: fileUrl,
          }
        })
      },

      // 格式化文件大小
      formatFileSize(size) {
        if (!size) return '未知大小'

        const units = ['B', 'KB', 'MB', 'GB']
        let fileSize = size
        let unitIndex = 0

        while (fileSize > 1024 && unitIndex < units.length - 1) {
          fileSize = fileSize / 1024
          unitIndex++
        }

        return `${fileSize.toFixed(2)} ${units[unitIndex]}`
      },

      async beforeUpload(file) {
        // 检查文件大小
        const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize
        if (!isLtMaxSize) {
          this.$message.error(`上传图片大小不能超过 ${this.maxSize}MB!`)
          return false
        }

        // 检查文件类型
        if (this.accept) {
          const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
          const acceptList = this.accept.split(',').filter((item) => item)
          const isAccept = acceptList.includes(fileExtension)
          if (!isAccept) {
            this.$message.error(
              `只能上传${this.accept
                .replace(/\./g, '')
                .replace(/,/g, '/')}格式的图片!`
            )
            return false
          }
        }

        // 如果是单图模式，先清空已有图片
        if (!this.multiple && this.fileList.length > 0) {
          this.fileList = []
          this.imageFiles = []
        }

        return true
      },

      customUpload(options) {
        const { file, onSuccess, onError, onProgress } = options

        if (!file.uid) {
          file.uid = Date.now() + '_' + Math.random().toString(36).substr(2, 9)
        }

        // 创建表单数据
        const formData = new FormData()
        formData.append('file', file)

        // 记录上传状态
        this.uploadingFiles.set(file.uid, {
          status: 'uploading',
          percentage: 0,
        })

        // 使用 axios 发送请求
        axios
          .post(this.uploadUrl, formData, {
            headers: {
              ...this.headerObj,
              'Content-Type': 'multipart/form-data',
            },
            onUploadProgress: (progressEvent) => {
              const percentage = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              )

              // 更新上传进度
              if (this.uploadingFiles.has(file.uid)) {
                const fileStatus = this.uploadingFiles.get(file.uid)
                fileStatus.percentage = percentage
                this.uploadingFiles.set(file.uid, fileStatus)
              }

              onProgress({ percent: percentage })
            },
          })
          .then((response) => {
            // 更新上传状态
            if (this.uploadingFiles.has(file.uid)) {
              const fileStatus = this.uploadingFiles.get(file.uid)
              fileStatus.status = 'success'
              this.uploadingFiles.set(file.uid, fileStatus)
            }

            onSuccess()

            // 处理上传成功的文件
            this.handleSuccessInternal(response.data, file)
          })
          .catch((error) => {
            // 更新上传状态
            if (this.uploadingFiles.has(file.uid)) {
              const fileStatus = this.uploadingFiles.get(file.uid)
              fileStatus.status = 'error'
              this.uploadingFiles.set(file.uid, fileStatus)
            }

            onError(error)

            this.$message.error('图片上传失败，请重试')
            this.$emit('on-error', error, file)
          })
          .finally(() => {
            // 清理上传状态
            setTimeout(() => {
              this.uploadingFiles.delete(file.uid)
            }, 3000)
          })
      },

      handleSuccess(res, file) {
        console.log('handleSuccess 被调用，可能导致重复添加图片', res, file)
      },

      handleSuccessInternal(res, file) {
        const fileExtension = this.getFileExtensionWithoutDot(file.name)
        const fileSize = file.size

        const fileInfo = {
          name: file.name,
          url: res.data.path,
          // url: res.data.file,
          size: fileSize,
          platform: fileExtension,
          fileSizeFormatted: this.formatFileSize(fileSize),
        }

        if (this.multiple) {
          // 多图模式
          this.imageFiles.push(fileInfo)
          this.fileList.push({
            name: file.name,
            url: res.data.file,
          })
        } else {
          // 单图模式 - 也使用数组形式
          this.imageFiles = [fileInfo]
          this.fileList = [
            {
              name: file.name,
              url: res.data.file,
            },
          ]
        }

        // 统一返回数组
        this.$emit('input', this.imageFiles)
        this.$emit('on-success', fileInfo)
      },

      handleRemove(file) {
        const fileIndex = this.fileList.findIndex(
          (item) => item.name === file.name && item.url === file.url
        )
        if (fileIndex > -1) {
          this.fileList.splice(fileIndex, 1)
        }

        const uploadedIndex = this.imageFiles.findIndex(
          (item) => item.name === file.name && item.url === file.url
        )
        if (uploadedIndex > -1) {
          this.imageFiles.splice(uploadedIndex, 1)
        }

        // 统一返回数组
        this.$emit('input', this.imageFiles)
        this.$emit('on-remove', file)
      },

      // 照片墙预览方法
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogTitle = file.name || '图片预览'
        this.dialogVisible = true
      },

      handleError(err, file) {
        this.$message.error('图片上传失败，请重试')
        this.$emit('on-error', err, file)
      },

      handleExceed(files, fileList) {
        this.$message.warning(
          `当前限制选择 ${this.limit} 张图片，本次选择了 ${
            files.length
          } 张图片，共选择了 ${files.length + fileList.length} 张图片`
        )
        this.$emit('on-exceed', files, fileList)
      },

      reset() {
        this.fileList = []
        this.imageFiles = []
        this.$emit('input', [])
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image-uploader {
    width: 100%;

    .uploader {
      width: 100%;
    }

    ::v-deep {
      .el-upload--picture-card {
        width: 110px;
        height: 110px;
        line-height: 110px;
      }

      .el-upload-list--picture-card .el-upload-list__item {
        width: 110px;
        height: 110px;
      }
    }
  }
</style>
