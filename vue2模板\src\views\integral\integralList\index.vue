<!-- 积分列表 -->
<template>
  <div class="integralList-container">
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item label="项目名称" prop="projectId">
        <el-select v-model="queryParams.projectId" placeholder="请选择">
          <el-option
            v-for="item in projectList"
            :key="item.id"
            clearable
            filterable
            :label="item.projectName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员" prop="userId">
        <el-select v-model="queryParams.userId" placeholder="请选择">
          <el-option
            v-for="item in storeUserList"
            :key="item.userId"
            clearable
            filterable
            :label="item.nickname"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">
          查询
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :span-method="objectSpanMethod"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" prop="name">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="员工" prop="username" />

      <el-table-column align="center" label="项目得失分情况">
        <el-table-column align="center" label="项目" prop="project_name" />
        <el-table-column align="center" label="得分" prop="incr_score" />
        <el-table-column align="center" label="扣分" prop="decr_score" />
      </el-table-column>

      <!-- <el-table-column align="center" label="岗位" prop="position" />
      <el-table-column align="center" label="级别" prop="level" />
      <el-table-column align="center" label="数据状态">
        <template #default="{ row }">
          {{ safetyType(row.safetyType) }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="140">
        <template #default="{ row }">
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(row)"
          />
        </template>
      </el-table-column> -->
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="total"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>
<script>
  import { mapGetters, mapState } from 'vuex'
  import { getProjectList } from '@/api/project/projectInfo'
  import { getPoints } from '@/api/integral/integralList'

  export default {
    name: 'IntegralList',

    data() {
      return {
        loading: false,
        selectedIds: [],
        total: 0,
        dataList: [],
        tableData: [],
        projectList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          projectId: undefined,
          userId: undefined,
        },
      }
    },

    computed: {
      ...mapGetters({
        storeProjectId: 'user/projectId',
        userId: 'user/userId',
      }),
      ...mapState({
        storeUserList: (state) => state.user.userList,
      }),
    },

    mounted() {
      // this.queryParams.userId = this.userId
      // this.queryParams.projectId = this.storeProjectId
      this.getList()
      this.goProjectList()
    },

    methods: {
      async getList() {
        // if (!this.storeProjectId) return

        this.loading = true
        try {
          const { code, data } = await getPoints(this.queryParams)

          if (code === 200) {
            this.dataList = data.list
            this.total = data.total
            this.handleTableData()
          }
        } catch (error) {
          console.error('Failed to fetch points:', error)
        } finally {
          this.loading = false
        }
      },
      goProjectList() {
        getProjectList({ pageNum: 1, pageSize: 9999 }).then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.projectList = data
          }
        })
      },

      // 处理表格数据
      handleTableData() {
        this.tableData = []
        this.dataList.forEach((item) => {
          if (item.projects && item.projects.length) {
            item.projects.forEach((detail, index) => {
              this.tableData.push({
                ...item,
                decr_score: detail.decr_score,
                incr_score: detail.incr_score,
                project_id: detail.project_id,
                project_name: detail.project_name,
                isFirst: index === 0,
                rowspan: item.projects.length,
              })
            })
          } else {
            this.tableData.push({
              ...item,
              isFirst: true,
              rowspan: 1,
            })
          }
        })
      },

      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        console.log(row, column, rowIndex, columnIndex)
        if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
          if (row.isFirst) {
            return {
              rowspan: row.rowspan,
              colspan: 1,
            }
          } else {
            return {
              rowspan: 0,
              colspan: 0,
            }
          }
        }
      },

      handleSelectionChange(selection) {
        this.selectedIds = selection.map((item) => item.id)
      },

      async handleDelete(row) {
        const ids = row ? [row.id] : this.selectedIds
        if (!ids.length) {
          this.$message.warning('请选择要删除的记录')
          return
        }

        try {
          await this.$confirm('确认删除所选记录?', '提示', {
            type: 'warning',
          })

          // await deletePoints(ids.join(','))
          this.$message.success('删除成功')
          this.getList()
        } catch (error) {
          // User canceled or API error
        }
      },

      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.getList()
      },

      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.getList()
      },

      handleDropdown() {
        // Implement dropdown actions
      },

      handleDownloadTemplate() {
        // Implement template download
      },

      showImportDialog() {
        // Implement import dialog
      },

      handleExport() {
        // Implement export
      },

      handleAdd() {
        // Implement add functionality
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParams.pageNum = 1
        this.$refs.queryFormRef.resetFields()
        // this.queryParams.userId = this.userId
        // this.queryParams.projectId = this.storeProjectId
        this.handleQuery()
      },
    },
  }
</script>

<style scoped>
  .ml-3 {
    margin-left: 12px;
  }
</style>
