import request from '@/utils/request'

// 当前登录用户积分统计
export function getUserIntegralStatistics(params) {
  return request({
    url: '/hse-perform-statistics/score/user',
    method: 'get',
    params,
  })
}

// 项目内用户积分统计-成员积分明细
export function getProjectMemberList(params) {
  return request({
    url: '/hse-perform-statistics/score/projectMemberList',
    method: 'get',
    params,
  })
}

// 导出项目内用户积分统计-成员积分明细
export function exportProjectMemberList(params) {
  return request({
    url: '/hse-perform-statistics/score/projectMemberList',
    method: 'get',
    params,
    responseType: 'blob',
  })
}

// 项目积分统计
export function getProjectCount(params) {
  return request({
    url: '/hse-perform-statistics/score/projectCount',
    method: 'get',
    params,
  })
}

// 项目内用户积分统计-按级别
export function getProjectLevelList(params) {
  return request({
    url: '/hse-perform-statistics/score/projectByLevel',
    method: 'get',
    params,
  })
}

// 系统管理员-项目平均分排名
export function getProjectAvgScore(params) {
  return request({
    url: '/hse-perform-statistics/score/projectAverageScore',
    method: 'get',
    params,
  })
}

// 系统管理员-部门平均分排名
export function getDepartmentAvgScore(params) {
  return request({
    url: '/hse-perform-statistics/score/departmentAverageScore',
    method: 'get',
    params,
  })
}
