/**
 * @description 路由守卫，目前两种模式：all模式与intelligence模式
 */
import {
  authentication,
  loginInterception,
  openWaterMark,
  routesWhiteList,
  supportVisit,
} from '@/config'
import router from '@/router'
import store from '@/store'
import getPageTitle from '@/utils/pageTitle'
import { toLoginRoute } from '@/utils/routes'
import watermark from '@/utils/waterMarker.js'
import moment from 'moment'
import VabProgress from 'nprogress'
import 'nprogress/nprogress.css'
VabProgress.configure({
  easing: 'ease',
  speed: 500,
  trickleSpeed: 200,
  showSpinner: false,
})
router.beforeEach(async (to, _from, next) => {
  const { showProgressBar } = store.getters['settings/theme']
  if (showProgressBar) VabProgress.start()
  let hasToken = store.getters['user/token']

  if (!loginInterception) hasToken = true

  if (hasToken) {
    if (store.getters['routes/routes'].length) {
      isIframeUrl(to)
      // 禁止已登录用户返回登录页
      if (to.path === '/login') {
        next({ path: '/' })
        if (showProgressBar) VabProgress.done()
      } else if (
        to.path !== '/system/personalCenter' &&
        to.matched.length > 0
      ) {
        next()
        // let userInfo = store.getters['user/user']
        // if (userInfo.pwdChanged == 0) {
        //   Vue.prototype.$baseMessage(`请修改初始密码！`, 'error')
        //   next({ path: '/system/personalCenter' })
        // } else next()
      } else next()
      if (showProgressBar) VabProgress.done()
    } else {
      try {
        if (loginInterception) await store.dispatch('user/getUserInfo')
        // config/setting.config.js loginInterception为false(关闭登录拦截时)时，创建虚拟角色
        else await store.dispatch('user/setVirtualRoles')
        // 根据路由模式获取路由并根据权限过滤
        await store.dispatch('routes/setRoutes', authentication)
        next({ ...to, replace: true })
      } catch (err) {
        console.error('vue-admin-beautiful错误拦截:', err)
        await store.dispatch('user/resetAll')
        next(toLoginRoute(to.path))
      }
    }
  } else {
    if (routesWhiteList.includes(to.path)) {
      // 设置游客路由(不需要可以删除)
      if (supportVisit && !store.getters['routes/routes'].length) {
        await store.dispatch('routes/setRoutes', 'visit')
        next({ ...to, replace: true })
      } else next()
    } else next(toLoginRoute(to.path))
    if (showProgressBar) VabProgress.done()
  }
})
router.afterEach((to) => {
  document.title = getPageTitle(to.meta.title)
  if (VabProgress.status) VabProgress.done()
  //启用水印时，添加水印文字
  if (openWaterMark) {
    if (to.name !== 'Login') {
      const userInfo = store.getters['user/user']
      watermark.watermark({
        content: [
          userInfo.nickname || userInfo.username,
          moment().format('YYYY/MM/DD'),
          `HSE履职`,
        ],
        // content: 'HSE履职',//一行水印时，传字符串
      })
    } else {
      watermark.watermark({
        content: '', //如果当前是登录页，水印内容设置为空
      })
    }
  }
})

function isIframeUrl(params) {
  let routes = store.getters['routes/routes']
  var a
  let finddata = (route) => {
    route.forEach((i) => {
      if (i.name == params.name) {
        a = i
      } else {
        if (i.children && i.children.length > 0) {
          finddata(i.children)
        }
      }
    })
    return a
  }
  let d = finddata(routes)
  if (d) {
    //如果是外部链接，需要iframe内嵌的，将路由信息保存至store中
    let iframeInfo = d.isUrl ? d : {}
    store.dispatch('iframe/setIframeInfo', iframeInfo, { root: true })
  }
}
