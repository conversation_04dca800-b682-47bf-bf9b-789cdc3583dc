<template>
  <div class="login">
    <el-form
      ref="loginRef"
      :model="activeName === 'captcha' ? loginForm : smsForm"
      :rules="activeName === 'captcha' ? loginRules : smsRules"
      class="login-form"
    >
      <div class="title-box">
        <h3 class="title">{{ title }}</h3>
      </div>

      <!-- Vue2风格的登录方式选择 -->
      <el-tabs v-model="activeName" @tab-click="handleClickTabs" class="login-tabs">
        <el-tab-pane label="验证码登录" name="captcha">
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="用户名">
              <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码" @keyup.enter="handleLogin">
              <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="code">
            <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="验证码" style="width: 60%" @keyup.enter="handleLogin">
              <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
            </el-input>
            <div class="login-code">
              <el-image class="login-code-img" :src="codeUrl" @click="changeCode">
                <template #error><div class="login-code-error">获取失败</div></template>
              </el-image>
            </div>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="短信登录" name="sms_code">
          <el-form-item prop="mobile">
            <el-input v-model="smsForm.mobile" type="text" size="large" auto-complete="off" placeholder="手机号">
              <template #prefix><svg-icon icon-class="phone" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="smsCode">
            <el-input
              v-model="smsForm.smsCode"
              size="large"
              auto-complete="off"
              placeholder="短信验证码"
              style="width: 63%"
              @keyup.enter="handleLogin"
            >
              <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
            </el-input>
            <el-button :disabled="codeState" @click="handleGetMessage" class="sms-button" type="primary">
              {{ codeState ? `${time}s后重新获取` : '获取验证码' }}
            </el-button>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>

      <el-form-item style="width: 100%">
        <el-button :loading="loading" size="large" type="primary" style="width: 100%" @click.prevent="handleLogin">
          <span v-if="!loading">登录</span>
          <span v-else>登录中...</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>智能化地震队西南研发团队提供技术服务</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getCodeImg, getSmsCode } from '@/api/login';
import { useUserStore } from '@/store/modules/user';

const title = import.meta.env.VITE_APP_TITLE;
const userStore = useUserStore();
const router = useRouter();

const activeName = ref('captcha');

const loginForm = ref({
  username: '',
  password: '',
  code: '',
  uuid: ''
});
const smsForm = ref({
  mobile: '',
  smsCode: ''
});

const loginRules = ref({
  username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
  password: [{ required: true, trigger: 'blur', message: '密码不能为空' }],
  code: [{ required: true, trigger: 'blur', message: '验证码不能为空' }]
});
const smsRules = ref({
  mobile: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  smsCode: [{ required: true, trigger: 'blur', message: '短信验证码不能为空' }]
});

const codeUrl = ref('');
const loading = ref(false);
const codeState = ref(false);
const time = ref(60);
const loginRef = ref();

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true;
      try {
        let paramsform = { grant_type: activeName.value };
        if (activeName.value === 'captcha') {
          paramsform = Object.assign(paramsform, loginForm.value);
        } else if (activeName.value === 'sms_code') {
          paramsform = Object.assign(paramsform, smsForm.value);
        }
        await userStore.login(paramsform);
        await router.push('/');
      } catch (error) {
        if (activeName.value === 'captcha') changeCode();
      } finally {
        loading.value = false;
      }
    }
  });
};

const changeCode = async () => {
  try {
    const { data } = await getCodeImg();
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    loginForm.value.uuid = data.uuid;
  } catch (error) {
    console.error('获取验证码失败:', error);
  }
};

const handleGetMessage = async () => {
  if (!codeState.value) {
    try {
      const params = {
        phoneNumber: smsForm.value.mobile
      };
      const { data } = await getSmsCode(params);
      if (data.code === '00000') {
        ElMessage.success('验证码已发送，5分钟内有效！');
        codeState.value = true;
        const timeout = setInterval(() => {
          time.value--;
          if (time.value === 0) {
            clearInterval(timeout);
            time.value = 60;
            codeState.value = false;
          }
        }, 1000);
      } else {
        ElMessage.error(data.msg);
      }
    } catch (error) {
      console.error('发送短信验证码失败:', error);
    }
  }
};

const handleClickTabs = (tab) => {
  if (tab.name === 'captcha') {
    loginRules.value = {
      username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
      password: [{ required: true, trigger: 'blur', message: '密码不能为空' }],
      code: [{ required: true, trigger: 'blur', message: '验证码不能为空' }]
    };
    loginRef.value?.resetFields();
    changeCode();
  } else if (tab.name === 'sms_code') {
    loginRules.value = {
      mobile: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
      smsCode: [{ required: true, trigger: 'blur', message: '短信验证码不能为空' }]
    };
    loginRef.value?.resetFields();
    codeState.value = false;
    time.value = 60;
  }
};

onMounted(() => {
  changeCode();
});
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url('../assets/images/login-background.jpg');
  background-size: cover;
}

.title-box {
  display: flex;
  justify-content: center;

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
  }
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  z-index: 1;

  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-tabs {
  margin-bottom: 20px;
}

.login-code {
  width: 40%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.login-code-img {
  width: 100%;
  height: 40px !important;
  padding-left: 12px;
  .login-code-error {
    width: 100%;
    height: 40px;
    color: #999;
    background-color: #f1f1f1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.sms-button {
  width: 33%;
  height: 40px;
  margin-left: 12px;
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial, serif;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
